import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';

export interface InviteAcceptedPayload {
	invite: InviteSongEntity & {
		invitee: UserEntity;
		inviter: UserEntity;
		song: SongEntity & {
			collaborations: CollaborationEntity[];
		};
	};
	interestedUserIds?: number[];
}
