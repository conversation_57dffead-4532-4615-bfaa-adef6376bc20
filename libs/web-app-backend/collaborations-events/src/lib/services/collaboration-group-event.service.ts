import { NotificationsProducer } from 'web-app-backend/notifications';
import { Injectable, Logger } from '@nestjs/common';
import { NotificationEntity } from 'web-app-backend/notifications/entities';
import { CollaborationGroup } from '../types/interfaces/collaboration-group.interface';
import { NotificationType, RefreshScope } from '@major/shared/models';
import { RefreshMessageService } from '@major/web-app-backend/util-websocket';
import { instanceToPlain } from 'class-transformer';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';

/**
 * Handle LEAVE and REMOVED collaboration events.
 *
 * Logic for each is almost identical:
 *
 * - Take the CollaborationEntity whose status will be changed to represent the user having left
 * - Gather the other collaborators
 * - Send them a notification that the collaborator left
 * - Send websocket message to connected collaborators so their UIs refresh
 *   and the song's authors are updated
 */
@Injectable()
export class CollaborationGroupEventService {
	private logger = new Logger(CollaborationGroupEventService.name);

	constructor(
		private notificationProducer: NotificationsProducer,
		protected refreshMessageService: RefreshMessageService,
		@InjectRepository(NotificationEntity)
		private readonly notificationRepository: Repository<NotificationEntity>,
	) {}

	/**
	 * @param group
	 * @param event
	 */
	async collaboratorLeft(
		group: CollaborationGroup,
		entity: CollaborationEntity,
	) {
		for (const recipient of group.otherCollaborators) {
			const doc = this.notificationRepository.create({
				type: NotificationType.COLLABORATOR_LEAVE,
				message: `${entity.invitee.displayName} left ${entity.song.title}`,
				userId: recipient.userId,
				email: recipient.email,
				deliveryStatus: 'pending',
				content: instanceToPlain(entity),
				contentType: 'Collaboration',
			});

			doc.referenceId = entity.id;

			try {
				const notification = await this.notificationRepository.save(doc);
				await this.notificationProducer.addDeliveryJob(notification);
			} catch (e) {
				this.logger.error(`Failed to save notification ${JSON.stringify(doc)}`);
			}
		}

		this.sendGroupRefreshMessages(group, entity);
	}

	/**
	 * @param group
	 * @param event
	 */
	async collaboratorRemoved(
		group: CollaborationGroup,
		collaboration: CollaborationEntity,
	) {
		for (const recipient of group.otherCollaborators) {
			const doc = this.notificationRepository.create({
				type: NotificationType.COLLABORATOR_REMOVED,
				message: `${collaboration.invitee.displayName} was removed from ${collaboration.song.title}`,
				userId: recipient.userId,
				email: recipient.email,
				deliveryStatus: 'pending',
				content: instanceToPlain(collaboration),
				contentType: 'Collaboration',
			});

			doc.referenceId = collaboration.id;

			try {
				const notification = await this.notificationRepository.save(doc);
				await this.notificationProducer.addDeliveryJob(notification);
			} catch (e) {
				this.logger.error(`Failed to save notification ${JSON.stringify(doc)}`);
			}
		}

		this.sendGroupRefreshMessages(group, collaboration);
	}

	/**
	 * @param group
	 * @param event
	 * @private
	 */
	private sendGroupRefreshMessages(
		group: CollaborationGroup,
		collaboration: CollaborationEntity,
	) {
		const targetUserIds = group.otherUserIds;
		const context = { id: collaboration.song.externalId };

		this.logger.debug(
			`Notify users ${JSON.stringify(group.otherUserIds)} about userId=${
				collaboration.invitee.id
			}`,
		);

		// Notify other collaborators to refresh their song data
		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.SONG_DETAIL,
			targetUserIds: group.otherUserIds,
			context,
		});

		// Also refresh collaborations list for all users
		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.INVITES_BY_SONG,
			targetUserIds,
			context,
		});

		// Notify collaborators and user to refresh their songs list
		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.SONGS,
			targetUserIds,
		});
	}
}
