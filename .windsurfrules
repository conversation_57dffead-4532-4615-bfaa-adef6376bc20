## Tech stack

- Backend app in NestJS
  - Redis used with @nestjs/bullmq
  - PostgreSQL used with TypeORM
- UI in Angular
- File storage in CloudFlare R2
  - All uploads directly to CloudFlare R2 from client
- UI uses DaisyUI 4.x and Tailwind 3.x
- This project uses @tanstack/angular-query-experimental

## Nx Workspace
- The code is an Nx workspace. Please don't use the Angular 'ng' executable.
- New features/modules need new angular/nest Nx libraries generated. UI libs should go in libs/web-app and backend nest libs should go in libs/web-app-backend
- Use nx-mcp server to help with nx workspace management and discovering about the code structure
- Nx workspace is used so new libraries should be created via the nx CLI and not simply created as directories

## Rules that must be followed

1. NotifyService is for showing toasts in the UI only (via the info() method)
2. All shared interfaces/enums/etc. should go into the shared-models nx library. Other types should live in the library that consumes them in sub directories, e.g.: types/entities, types/enums etc
4. In the UI, HTTP requests go in 'data' services, and angular-query queries and mutations
   must always live in a dedicated 'query' service that uses the appropriate 'data' service
5. Propose changes to the UI (web-app) as well as the backend nestjs service (web-app-backend) when appropriate
7. Angular component templates should always be in a separate HTML file named \*.component.html
8. Angular Material is only used for dialog, icons and menus. Everything else is DaisyUI and Tailwind only.
9. Favour signals over RxJs except for business side-effects
10. Use firstValueFrom and lastValueFrom instead of toPromise
11. Group related lines of code, separate by blank lines
12. Add comments to explain what is happening in the code. Reference any related changes that were made.
13. The UI has many common components created in libs/web-app/ui please check there and try use them where applicable
14. You may not use any code inside the libs/web-app when making changes inside the libs/web-app-backend and vica versa (because UI and node code cannot be mixed) - shared types are fine, these are located in the libs/shared nx library
15. NestJS should be idiomatic
16. Angular code should be idiomatic
17. Use lodash-es in the UI when it makes for simpler code
18. Use lodash (not lodash-es) in the backend when it makes for simpler code
19. The SongEntity on the backend mainly works with the primary key column (id) but when sending to the frontend the value of the externalId column is mapped to the 'id' property. Please keep this in mind. The UI only deals with song externalIds to prevent leaking primary keys. This is the same for UserEntity, except the property is named UserEntity.userId. When a payload is received from the UI, the value of 'id' parameters will for these entities be externalId strings. Controllers will often then resolve an entity from this. Then when calling a service with the entity, the primary key column will be used. So that means when you have an instance of an entity on the backend, you should never assume the 'id' field is an externalId string in these cases.
20. For web-app features, I like to place data and query services in a dedicated "domain" nx lib, e.g. libs/web-app/feature-projects/src/lib/domain/data/project-data.service.ts - but I never create these libs without using the nx cli to create them
21. Always use the JwtUser() inside controller methods that require the current user's id - never put a user id (external or primary key) into a URL
22. Do not put lots of logic into Angular templates - when you need to check state in a specific way, favour computed signals instead
23. Ensure Angular standalone components are correctly importing all directives, pipes, etc.
24. when executing npm commands please use bun as your first choice and only use npm as a fallback. do not use yarn.
25. when working on tooling and writing scripts, please favour typescript over bash.
26. Create string templates when variable substitution is required, but string literals otherwise
27. When import node packages always use the node: prefix e.g. "import * as path from 'node:path'"

## Code Style

Please adhere to these prettier rules:

{
  "singleQuote": true,
  "useTabs": false,
  "tabWidth": 2,
  "trailingComma": "all",
  "printWidth": 95
}

In addition and these are all IMPORTANT:

- Always use braces with control flow statements
- Control flow statements should have a blank line above and below
- Try to group related statements together (like variable assignment) with blank lines inbetween groups
- Assign the outcome of slightly longer expressions to variables to aid readability
- Create loosely coupled code which has the highest standard of design and testability, but don't over-engineer

## Circular Dependency Issues

When there are circular dependency issues, refer to libs/web-app/editor/feature-song-editor/src/lib/web-app-editor-feature-song-editor.module.ts as this is importing some components using relative paths, not ts config paths. If these have been changed they may cause an issue. e.g. the import:
import { SongMetaComponent } from '../../../../songs/feature-detail/src/lib/components/song-meta/song-meta.component';
is fine, but the ts path import import { SongMetaComponent } from '@major/web-app/songs/feature-detail'; causes circular dependency issues

## Testing

When writing unit tests please use the createTestApp() utility function to create a test app in a hook, probably beforeAll(). createTestApp() makes a realistic nest app instance. It runs testcontainers of postgres and redis via the global setup script, which closely mirrors production. Please also use createMock() as provided by @golevelup/ts-jest package for creating mocks. Then for each test you can just use jest.spyOn() to create the desired behaviour for the specific test.

### Naming tests

Please use the prefix 'it must' or 'it must not' to indicate whether the test should pass or fail. e.g. 'it must return the correct result' or 'it must not return the correct result'. For describe blocks please include parantheses to mark a function e.g. describe('functionName()'...)

### Context

Also you should never install new dependencies. My package.json is off-limits for changes UNLESS explicitally state otherwise.

# Documentation

When modifying the code to a library, please update the README.md file in the same directory. README files should not be too long but should describe the library in a concise manner and how the library is used in the repo.
