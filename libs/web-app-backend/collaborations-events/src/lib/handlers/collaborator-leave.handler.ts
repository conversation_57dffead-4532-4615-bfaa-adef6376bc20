import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CollaborationGroupEventService } from '../services/collaboration-group-event.service';
import { CollaborationGroupFactory } from '../services/collaboration-group.factory';
import { MissingRelationException } from '@major/shared/models/classes';
import { CollaborationLeftEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-left.event';

@Injectable()
export class CollaboratorLeaveHandler {
	private logger = new Logger(CollaboratorLeaveHandler.name);

	constructor(
		private collaborationGroupFactory: CollaborationGroupFactory,
		private groupEventService: CollaborationGroupEventService,
	) {}

	@OnEvent(CollaborationLeftEvent.eventName)
	async onUserLeave(event: CollaborationLeftEvent) {
		this.logger.debug(`Handle ${CollaborationLeftEvent.eventName} event`);

		const { collaboration } = event.payload;

		if (!collaboration.inviter) {
			throw new MissingRelationException('"inviter" relation required');
		}

		if (!collaboration.invitee) {
			throw new MissingRelationException('"invitee" relation required');
		}

		if (!collaboration.song) {
			throw new MissingRelationException('"song" relation required');
		}

		try {
			const group =
				await this.collaborationGroupFactory.createFromCollaboration(
					collaboration,
				);
			await this.groupEventService.collaboratorLeft(group, collaboration);
		} catch (e: any) {
			throw new Error(
				`Could not create group and handle group event: "${e.message}"`,
			);
		}
	}
}
