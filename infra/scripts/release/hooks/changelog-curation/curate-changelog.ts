import { GoogleGenAI } from '@google/genai';
import { readFileSync, writeFileSync } from 'node:fs';
import { join } from 'node:path';
// @ts-ignore
import chalk from 'chalk';
import * as dotenv from 'dotenv';
import { curateChangelogPrompt } from 'infra/scripts/release/hooks/changelog-curation/curate-changelog.prompt';

// Configure dotenv to look for .env in the project root
// Assuming your .env file is at the root of your project where package.json is.
dotenv.config({
	path: join(process.cwd(), '.env'),
});

const apiKey = process.env.GOOGLE_GEMINI_API_KEY;

// It's a good practice to ensure the API key is loaded.
if (!apiKey) {
	console.error(
		chalk.red.bold(
			'Error: GOOGLE_GEMINI_API_KEY is not set in your environment.',
		),
	);
	console.error(
		chalk.red(
			'Please ensure it is defined in your .env file at the project root.',
		),
	);
	process.exit(1); // Exit if the API key is crucial and missing
}

const genAI = new GoogleGenAI({ apiKey });

const modelName = 'gemini-2.5-pro';
const systemInstruction =
	"You are the voice of my SaaS app's changelog. Your goal is to ensure the user is informed and ultimately more engaged with the app. You only reply with the release notes, strictly no commentary.";

export async function curateChangelog() {
	// Path to the standard CHANGELOG.md in the project root
	const pathToInputFile = join(process.cwd(), 'CHANGELOG.md');

	// Corrected: Output CHANGELOG-USER.md (or CHANGELOG-CUSTOMER.md) in the project root
	// If you prefer "CHANGELOG-CUSTOMER.md", change the filename here:
	const pathToOutputFile = join(process.cwd(), 'CHANGELOG-CURATED.md');

	console.log(chalk.yellow.bold('Reading from:'), chalk.cyan(pathToInputFile));

	let changelogContent: string;
	try {
		changelogContent = readFileSync(pathToInputFile, 'utf-8');
	} catch (error) {
		console.error(
			chalk.red.bold(`Error reading changelog file at ${pathToInputFile}:`),
		);
		console.error(
			chalk.red(error instanceof Error ? error.message : String(error)),
		);
		throw error; // Re-throw to let the hook runner know this step failed
	}

	console.log(chalk.blue('Generating customer-facing release notes...'));

	try {
		const response = await genAI.models.generateContent({
			model: modelName,
			contents: curateChangelogPrompt(changelogContent),
			config: {
				systemInstruction,
				// Slightly reduced temperature can sometimes lead to more focused output
				temperature: 0.8,
				topP: 0.95,
				topK: 64,
				// Adjusted based on typical model limits, 65536 might be too high for flash models
				maxOutputTokens: 8192,
				// responseModalities is often not needed for text/plain
				responseMimeType: 'text/plain',
			},
		});

		const releaseNotes = response.text;

		if (typeof releaseNotes !== 'string') {
			console.error(
				chalk.red.bold('Error: Failed to get valid text response from Gemini.'),
			);
			// Log the response for debugging if possible, but be mindful of sensitive data
			console.error(
				chalk.red('Full API response:'),
				JSON.stringify(response, null, 2),
			);
			throw new Error('Invalid response from Gemini API.');
		}

		writeFileSync(pathToOutputFile, releaseNotes, 'utf-8');
		console.log(
			chalk.green.bold('Successfully saved customer release notes to:'),
			chalk.cyan(pathToOutputFile),
		);

		return true; // Or simply let it exit with 0 if no explicit return is needed by caller
	} catch (error) {
		console.error(
			chalk.red.bold('Error during Gemini API call or file writing:'),
		);
		console.error(
			chalk.red(error instanceof Error ? error.message : String(error)),
		);
		// If the error object from the API has more details, log them
		if (error && (error as any).response) {
			console.error(
				chalk.red('API Error Details:'),
				JSON.stringify((error as any).response.data, null, 2),
			);
		}
		throw error; // Re-throw to ensure the release process knows this step failed
	}
}
