import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CollaborationGroupEventService } from '../services/collaboration-group-event.service';
import { CollaborationGroupFactory } from '../services/collaboration-group.factory';
import { MissingRelationException } from '@major/shared/models/classes';
import { CollaborationRemovedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-removed.event';

@Injectable()
export class CollaboratorRemovedHandler {
	private logger = new Logger(CollaboratorRemovedHandler.name);

	constructor(
		private collaborationGroupFactory: CollaborationGroupFactory,
		private groupEventService: CollaborationGroupEventService,
	) {}

	/**
	 * Notification to removed user when song owner removes them as collaborator
	 * @param event
	 */
	@OnEvent(CollaborationRemovedEvent.eventName)
	async onUserRemoved(event: CollaborationRemovedEvent) {
		this.logger.debug(`Handle ${CollaborationRemovedEvent.eventName} event`);

		const { collaboration } = event.payload;

		if (!collaboration.inviter) {
			throw new MissingRelationException('"inviter" relation required');
		}

		if (!collaboration.invitee) {
			throw new MissingRelationException('"invitee" relation required');
		}

		if (!collaboration.song) {
			throw new MissingRelationException('"song" relation required');
		}

		try {
			const group =
				await this.collaborationGroupFactory.createFromCollaboration(
					collaboration,
				);
			await this.groupEventService.collaboratorRemoved(group, collaboration);
		} catch (e: any) {
			throw new Error(
				`Could not create group and handle group event: "${e.message}"`,
			);
		}
	}
}
