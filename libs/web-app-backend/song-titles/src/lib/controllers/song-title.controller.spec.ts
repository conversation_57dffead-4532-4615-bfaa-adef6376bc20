import { Test, TestingModule } from '@nestjs/testing';
import { SongTitleController } from 'libs/web-app-backend/song-titles/src/lib/controllers/song-title.controller';
import { SongTitleService } from 'libs/web-app-backend/song-titles/src/lib/services/song-title.service';

describe('SongTitleController', () => {
	let controller: SongTitleController;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [SongTitleController],
			providers: [SongTitleService],
		}).compile();

		controller = module.get<SongTitleController>(SongTitleController);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});
});
