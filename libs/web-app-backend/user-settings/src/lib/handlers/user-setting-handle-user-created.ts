import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { UserSettingsEntity } from 'libs/web-app-backend/user-settings/src/lib/types/entities/user-settings.entity';
import { Repository } from 'typeorm';
import { UserCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-created.event';
import { captureException } from '@sentry/nestjs';

@Injectable()
export class UserSettingsHandleUserCreated {
	private logger = new Logger(UserSettingsHandleUserCreated.name);

	constructor(
		@InjectRepository(UserSettingsEntity)
		private repo: Repository<UserSettingsEntity>,
	) {}

	/**
	 * Create the default user settings for every new user created
	 */
	@OnEvent(UserCreatedEvent.eventName)
	async createDefaultSettings(event: UserCreatedEvent) {
		const { user } = event.payload;

		const settings = this.repo.create({
			userId: user.id,
			songActivitySummary: true,
		});

		try {
			await this.repo.save(settings);
		} catch (e: any) {
      captureException(e);
			this.logger.error(e.message);
		}
	}
}
