import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';

export interface InviteDeclinedPayload {
	invite: InviteSongEntity & {
		invitee: UserEntity;
		inviter: UserEntity;
		song: SongEntity;
	};
	interestedUserIds?: number[];
}
