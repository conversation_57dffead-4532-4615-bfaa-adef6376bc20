import { Injectable } from '@nestjs/common';
import { WebAppGateway } from '@major/web-app-backend/util-websocket';
import { CloudConvertJobEntity } from '../types/entities/cloud-convert-job.entity';
import { DomainEventEmitter } from 'domain-events';
import { CloudConvertJobUpdatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/import/cloud-convert-job-updated.event';

@Injectable()
export class CloudConvertEventEmitter {
	constructor(private domainEventEmitter: DomainEventEmitter) {}

	async notifyJobUpdate(job: CloudConvertJobEntity) {
		this.domainEventEmitter.emitEvent(
			new CloudConvertJobUpdatedEvent({
				userId: job.userId,
				job,
			}),
		);
	}
}
