import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CollaborationEntity } from '../types/entities/collaboration.entity';
import { CollaborationStatus } from '@major/shared/models';
import { FileMetaDataEntity } from '@major/web-app-backend/file-meta-data/entities';
import { RemovalOptions } from '@major/shared/models/interfaces';
import { DomainEventEmitter } from 'domain-events';
import { CollaborationRemovedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-removed.event';
import { CollaborationLeftEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-left.event';

@Injectable()
export class CollaboratorRemovalService {
	constructor(
		@InjectRepository(CollaborationEntity)
		private readonly collaborationRepository: Repository<CollaborationEntity>,
		@InjectRepository(FileMetaDataEntity)
		private readonly fileMetaDataRepository: Repository<FileMetaDataEntity>,
		private domainEventEmitter: DomainEventEmitter,
	) {}

	async removeCollaboration(
		collaborationId: number,
		options: RemovalOptions,
		isLeaving = false,
	): Promise<void> {
		// Find the collaboration - don't check status since we want to allow removing in any state
		const collaboration = await this.collaborationRepository.findOne({
			where: {
				id: collaborationId,
			},
			relations: ['invitee', 'song', 'inviter'],
		});

		if (!collaboration) {
			throw new Error('Collaboration not found');
		}

		// Update collaboration status
		collaboration.status = CollaborationStatus.Removed;
		collaboration.removedAt = new Date();
		collaboration.removalNote =
			options.removalNote ?? 'Owner removed collaborator';
		collaboration.retainContent = options.keepContent;

		await this.collaborationRepository.save(collaboration);

		// the inviter is the only one who can 'remove' a collaborator
		// (if collaborator removes themselves, that is 'leaving')
		if (!isLeaving) {
			this.domainEventEmitter.emitEvent(
				new CollaborationRemovedEvent({
					actionUserId: collaboration.inviter.id,
					songId: collaboration.song.id,
					timestamp: Date.now(),
					collaboration,
				}),
			);
		} else {
			this.domainEventEmitter.emitEvent(
				new CollaborationLeftEvent({
					actionUserId: collaboration.invitee.id,
					songId: collaboration.song.id,
					timestamp: Date.now(),
					collaboration,
				}),
			);
		}

		// Handle files based on retention option
		const fileMetadata = await this.fileMetaDataRepository.find({
			where: {
				song: { id: collaboration.songId },
				owner: { id: collaboration.invitee.id },
			},
		});

		if (options.keepContent) {
			for (const file of fileMetadata) {
				file.retainedAfterCollaboratorRemoval = true;
			}
		} else {
			const now = new Date();
			for (const file of fileMetadata) {
				file.archivedAt = now;
				file.retainedAfterCollaboratorRemoval = false;
			}
		}

		await this.fileMetaDataRepository.save(fileMetadata);
	}

	async leaveCollaboration(
		collaborationId: number,
		options: RemovalOptions,
	): Promise<void> {
		// Reuse the same logic but with different messaging
		await this.removeCollaboration(
			collaborationId,
			{
				...options,
				removalNote: options.removalNote ?? 'User left the collaboration',
			},
			true,
		);
	}
}
