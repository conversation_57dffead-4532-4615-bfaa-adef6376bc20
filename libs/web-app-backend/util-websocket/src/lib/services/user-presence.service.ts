import { Injectable, Logger } from '@nestjs/common';
import { CurrentJwtUser } from '@major/web-app-backend/util-auth';
import { RedisService } from 'web-app-backend/redis';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

interface OnlineWebSocketUser extends CurrentJwtUser {
	socketId: string;
	lastSeen: string;
}

/**
 * Used by the NotificationsConsumer to determine if a user is online
 * in order to deliver via websocket not email.
 */
@Injectable()
export class UserPresenceService {
	private logger = new Logger(UserPresenceService.name);
	private readonly keyPrefix = 'jwt-user';

	constructor(private redisService: RedisService) {}

	async addUser(socketId: string, user: CurrentJwtUser) {
		const enriched: OnlineWebSocketUser = {
			...user,
			socketId,
			lastSeen: new Date().toISOString(),
		};

		// Use a multi operation to ensure atomic updates
		try {
			const pipeline = this.redisService.getClient().multi();

			pipeline.set(
				`${this.keyPrefix}-by-socket-id:${socketId}`,
				JSON.stringify(enriched),
			);
			pipeline.set(
				`${this.keyPrefix}-by-email:${user.email}`,
				JSON.stringify(enriched),
			);
			pipeline.set(
				`${this.keyPrefix}-by-id:${user.id}`,
				JSON.stringify(enriched),
			);

			await pipeline.exec();
			// Change from debug to verbose - these are very frequent operations
			this.logger.verbose(
				`Added user ${user.email} (ID: ${user.id}) with socket ${socketId}`,
			);
		} catch (error: any) {
			this.logger.error(
				`Failed to add user ${user.email}: ${error.message}`,
				error.stack,
			);
			throw error; // Re-throw to allow caller to handle
		}
	}

	async removeUser(socketId: string) {
		try {
			const user: OnlineWebSocketUser =
				await this.getUserBySocketIdOrThrow(socketId);

			// Use a multi operation for atomic deletion
			const pipeline = this.redisService.getClient().multi();

			pipeline.del(`${this.keyPrefix}-by-socket-id:${socketId}`);
			pipeline.del(`${this.keyPrefix}-by-email:${user.email}`);
			pipeline.del(`${this.keyPrefix}-by-id:${user.id}`);

			await pipeline.exec();
			this.logger.log(`Removed user ${user.email} (ID: ${user.id})`);
		} catch (error: any) {
			// warning - not an error as this can be a "silent" fail
			this.logger.log(
				`Failed to remove user with socket ${socketId}: ${error.message}`,
			);
		}
	}

	async getUserByIdOrThrow(userId: number): Promise<OnlineWebSocketUser> {
		const data = await this.redisService.get(
			`${this.keyPrefix}-by-id:${userId}`,
		);

		if (data) {
			return JSON.parse(data);
		}

		throw new Error(`User userId=${userId} not found in Redis`);
	}

	async getUserBySocketIdOrThrow(
		socketId: string,
	): Promise<OnlineWebSocketUser> {
		const data = await this.redisService.get(
			`${this.keyPrefix}-by-socket-id:${socketId}`,
		);

		if (data) {
			return JSON.parse(data);
		}

		throw new Error(`User socketId=${socketId} not found in Redis`);
	}

	async getUserByEmailOrThrow(email: string): Promise<OnlineWebSocketUser> {
		const data = await this.redisService.get(
			`${this.keyPrefix}-by-email:${email}`,
		);

		if (data) {
			return JSON.parse(data);
		}

		throw new Error(`User email=${email} not found in Redis`);
	}

	/**
	 * Check if user is online (connected to ANY replica)
	 */
	async isUserOnline(userId: number) {
		try {
			await this.getUserByIdOrThrow(userId);
			return true;
		} catch (error: any) {
			this.logger.log(`User userId=${userId} is offline`);
			return false;
		}
	}

	/**
	 * Update the last seen timestamp for a user
	 * @returns Boolean indicating success (true) or failure (false)
	 */
	async updateLastSeen(userId: number): Promise<boolean> {
		try {
			// Get the current user data
			const userData = await this.getUserByIdOrThrow(userId);

			// Only update the lastSeen timestamp
			userData.lastSeen = new Date().toISOString();

			// More efficient approach: only update existing keys rather than re-adding everything
			const pipeline = this.redisService.getClient().multi();

			pipeline.set(
				`${this.keyPrefix}-by-socket-id:${userData.socketId}`,
				JSON.stringify(userData),
			);
			pipeline.set(
				`${this.keyPrefix}-by-email:${userData.email}`,
				JSON.stringify(userData),
			);
			pipeline.set(
				`${this.keyPrefix}-by-id:${userData.id}`,
				JSON.stringify(userData),
			);

			await pipeline.exec();
			this.logger.verbose(
				`Updated last seen for user ${userData.email} (ID: ${userData.id})`,
			);

			return true;
		} catch (error: any) {
			this.logger.error(
				`Failed to update last seen for user ${userId}: ${error.message}`,
				error.stack,
			);
			return false; // Return false to indicate failure to caller
		}
	}

	/**
	 * Run less frequently (every 5 minutes) with a longer staleness window (90 minutes)
	 * to prevent accidentally removing active users
	 */
	@Cron(CronExpression.EVERY_MINUTE)
	async cleanupStaleUsers(): Promise<void> {
		// this.logger.log('Cleaning up stale user presence records');

		const now = new Date();
		// Increase staleness threshold to 90 minutes for greater safety margin
		const staleThreshold = new Date(now.getTime() - 1000 * 60 * 90);
		let staleUserCount = 0;

		// Process socket ID keys first as they're the source of truth
		const socketKeys = await this.redisService.keys(
			`${this.keyPrefix}-by-socket-id:*`,
		);

		// this.logger.log(`Found ${socketKeys.length} socket keys to check`);

		for (const key of socketKeys) {
			const data = await this.redisService.get(key);
			if (!data) continue;

			try {
				const userData = JSON.parse(data) as OnlineWebSocketUser;
				if (new Date(userData.lastSeen).getTime() < staleThreshold.getTime()) {
					// This is a stale user - remove all associated keys atomically
					const pipeline = this.redisService.getClient().multi();

					pipeline.del(`${this.keyPrefix}-by-socket-id:${userData.socketId}`);
					pipeline.del(`${this.keyPrefix}-by-email:${userData.email}`);
					pipeline.del(`${this.keyPrefix}-by-id:${userData.id}`);

					await pipeline.exec();
					staleUserCount++;

					this.logger.log(
						`Removed stale user ${userData.email} (${userData.id}) with socket ${userData.socketId}, last seen: ${userData.lastSeen}`,
					);
				}
			} catch (e) {
				this.logger.warn(
					`Failed to parse user data from ${key}, removing: ${e}`,
				);
				await this.redisService.del(key);
				staleUserCount++;
			}
		}

		if (staleUserCount > 0) {
			this.logger.log(`Removed ${staleUserCount} stale users`);
		}
	}
}
