import { Injectable } from '@nestjs/common';
import { InviteEventHandlerBase } from './invite-event-handler.base';
import { OnEvent } from '@nestjs/event-emitter';
import { InviteCreatedEvent } from 'domain-events';

@Injectable()
export class InviteCreated<PERSON><PERSON><PERSON> extends InviteEventHandlerBase {
	@OnEvent(InviteCreatedEvent.eventName)
	async onInviteCreated(event: InviteCreatedEvent) {
		const { invite } = event.payload;

		const doc =
			await this.notificationFactory.createInviteReceivedNotification(invite);
		await this.notificationProducer.addDeliveryJob(doc);
	}
}
