import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModuleBuilder } from '@nestjs/testing';
import { AppModule } from 'web-app-backend/application';
import { WebAppBackendUtilAuthModule } from '@major/web-app-backend/util-auth';
import { WebAppBackendUtilAuthMockModule } from '@major/web-app-backend/util-auth/testing';
import CloudConvert from 'cloudconvert';
import { S3 } from '@aws-sdk/client-s3';
import { WebAppBackendDatabaseSeedModule } from 'web-app-backend/database-seed';
import { LogtoService } from '../../../../webhooks/src/logto/logto.service';
import { SENDGRID_CLIENT } from 'libs/web-app-backend/util-sendgrid/src/lib/symbols/index';

/**
 * Create a testing module from the main AppModule.
 *
 * Provides all the necessary mocks (WIP) so that the running e2e
 * suite uses as realistic application as possible.
 */
export async function createTestingModuleBuilder(
	envFilePath = '.env.test',
): Promise<TestingModuleBuilder> {
	const module = Test.createTestingModule({
		imports: [
			WebAppBackendDatabaseSeedModule.withEnvFile(envFilePath),
			AppModule.withEnvFile(envFilePath),
		],
	})
		// .overrideProvider(TypeormPostgresFactory)
		// .useClass(TypeormBetterSqliteFactory)

		// -------------------------------
		// In-memory Redis server
		// will be used by Bull and websocket adapter
		// -------------------------------
		// .overrideProvider(RedisClientFactory)
		// .useClass(RedisInMemoryClientFactory)

		// -------------------------------
		// Mock CloudConvert
		// -------------------------------
		.overrideProvider('CloudConvert')
		.useValue(createMock<CloudConvert>())

		// -------------------------------
		// Mock S3 (used for Cloudflare R2)
		// -------------------------------
		.overrideProvider(S3)
		.useValue(createMock<S3>())

		// -------------------------------
		// SendGrid client
		// -------------------------------
		// .overrideProvider(SENDGRID_CLIENT)
		// .useValue({
		// 	send: jest.fn().mockResolvedValue({}),
		// 	setApiKey: jest.fn(),
		// })

		// -------------------------------
		// Mock auth system
		// When running e2e using Playwright we also need to use this mock module!
		// -------------------------------
		.overrideModule(WebAppBackendUtilAuthModule)
		.useModule(WebAppBackendUtilAuthMockModule)
		// the WebAppBackendUtilAuthMockModule module will validate
		// the JWT sent with supertest, test behaviour can be altered
		// using specific values for the token, see the mock module comments
		// on the mock of ValidateLogtoJwtService
		// TODO remove all these comments and JwtAuthGuard override commented out
		// .overrideGuard(JwtAuthGuard)
		// .useValue({ canActivate: jest.fn().mockReturnValue(true) })

		// -------------------------------
		// Mock Logto
		// -------------------------------
		.overrideProvider(LogtoService)
		.useValue(createMock<LogtoService>());

	return module;
}
