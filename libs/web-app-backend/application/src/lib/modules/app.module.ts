import { DynamicModule, Module } from '@nestjs/common';

import { WebAppBackendUsersModule } from '@major/web-app-backend/users';
import { WebAppBackendSongsModule } from '@major/web-app-backend/songs';
import { WebAppBackendCollaborationsModule } from '@major/web-app-backend/collaborations';
import { WebAppBackendUtilWebsocketModule } from '@major/web-app-backend/util-websocket';
import { WebAppBackendAdminModule } from '@major/web-app-backend/admin';
import { WebAppBackendArchivedModule } from '@major/web-app-backend/archived';
import { WebAppBackendStorageModule } from '@major/web-app-backend/storage';
import { ConfigModule } from '@nestjs/config';
import { WebAppBackendWebhooksModule } from '@major/web-app-backend/webhooks';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { WebAppBackendUtilAuthModule } from '@major/web-app-backend/util-auth';
import { WebAppBackendIdeasModule } from '@major/web-app-backend/ideas';
import { GlobalsModule } from './globals.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { WebAppBackendInvitesModule } from 'web-app-backend/invites';
import { WebAppBackendUtilLoggingModule } from 'web-app-backend/util-logging';
import { SentryModule } from '@sentry/nestjs/setup';
import { WebAppBackendNotificationsModule } from 'web-app-backend/notifications';
import { BullModule } from '@nestjs/bullmq';
import { WebAppBackendInvitesEventsModule } from 'web-app-backend/invites-events';
import { WebAppBackendDatabaseModule } from 'web-app-backend/database';
import { WebAppBackendMembershipsModule } from 'web-app-backend/memberships';
import { WebAppBackendCloudConvertModule } from 'web-app-backend/cloud-convert';
import { WebAppBackendProjectsModule } from 'web-app-backend/projects';
import { WebAppBackendCollaborationsEventsModule } from 'web-app-backend/collaborations-events';
import { WebAppBackendPaymentsModule } from 'web-app-backend/payments';
import { WebAppBackendRedisModule } from 'web-app-backend/redis';
import { FileMetaDataModule } from '../../../../storage/src/file-meta-data/file-meta-data.module';
import { AppController } from '../controllers/app.controller';
import { AppHealthService } from '../services/app-health.service';
import { BullConfigService } from '../services/bull-config.service';
import { ScheduleModule } from '@nestjs/schedule';
import { CollaborationMessagingModule } from '@major/web-app-backend/collaboration-messaging';
import { WebAppBackendAssistantModule } from 'web-app-backend/assistant';
import { WebAppBackendUserSettingsModule } from 'user-settings';
import { WebAppBackendUtilPosthogNodeModule } from 'util-posthog-node';
import { BetaCodesModule } from 'beta-codes';
import { BetaSignupModule } from 'beta-signup';
import { WebAppBackendUtilTelegramModule } from 'util-telegram';
import { WebAppBackendActivityModule } from 'libs/web-app-backend/activity/src/lib/web-app-backend-activity.module';
import { WebAppBackendDomainEventsModule } from 'domain-events';
import { WebAppBackendMailerModule } from 'mailer';
import { WebAppBackendUtilSecurityModule } from 'web-app-backend/util-security';
import { WebAppBackendSongTitleModule } from 'song-titles';

@Module({})
export class AppModule {
	static withEnvFile(
		envFilePath: string,
		ignoreEnvFile = false,
	): DynamicModule {
		return {
			module: AppModule,
			imports: [
				GlobalsModule,
				SentryModule.forRoot(),

				ScheduleModule.forRoot(),

				BullModule.forRootAsync({
					useClass: BullConfigService,
				}),

				ConfigModule.forRoot({
					cache: true,
					isGlobal: true,

					envFilePath,
					ignoreEnvFile,
				}),

				EventEmitterModule.forRoot({
					global: true,
				}),

				ThrottlerModule.forRoot([
					{
						ttl: 10000,
						limit: 10,
					},
				]),

				BetaCodesModule,
				BetaSignupModule,
				CollaborationMessagingModule,
				FileMetaDataModule,
				WebAppBackendActivityModule,
				WebAppBackendAdminModule,
				WebAppBackendArchivedModule,
				WebAppBackendAssistantModule,
				WebAppBackendCloudConvertModule,
				WebAppBackendCollaborationsEventsModule,
				WebAppBackendCollaborationsModule,
				WebAppBackendDatabaseModule,
				WebAppBackendDomainEventsModule,
				WebAppBackendIdeasModule,
				WebAppBackendInvitesEventsModule,
				WebAppBackendInvitesModule,
				WebAppBackendMailerModule,
				WebAppBackendMembershipsModule,
				WebAppBackendNotificationsModule,
				WebAppBackendPaymentsModule,
				WebAppBackendProjectsModule,
				WebAppBackendRedisModule,
				WebAppBackendSongsModule,
				WebAppBackendStorageModule,
				WebAppBackendUserSettingsModule,
				WebAppBackendUsersModule,
				WebAppBackendUtilAuthModule,
				WebAppBackendUtilLoggingModule,
				WebAppBackendUtilPosthogNodeModule,
				WebAppBackendUtilSecurityModule,
				WebAppBackendUtilTelegramModule,
				WebAppBackendUtilWebsocketModule,
				WebAppBackendWebhooksModule,
				WebAppBackendSongTitleModule,
			],
			controllers: [AppController],
			providers: [AppHealthService, BullConfigService],
		};
	}
}
