import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import {
	MessageBody,
	SubscribeMessage,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { WebSocketEvent } from '@major/shared/util-websocket/types';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';
import { JwtWsUser } from 'libs/web-app-backend/util-websocket/src/lib/decorators/ws-user.decorator';
import type { CurrentWebSocketUser } from '@major/web-app-backend/util-websocket/types';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class RecorderGateway {
	private logger = new Logger(RecorderGateway.name);

	@WebSocketServer()
	server!: Server;

	@SubscribeMessage(WebSocketEvent.RecorderRefreshRequired)
	async recorderUpdate(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { songId: string },
	) {
		user.client
			.to(`song:${payload.songId}`)
			.emit(WebSocketEvent.RecorderRefreshRequired, {});
	}
}
