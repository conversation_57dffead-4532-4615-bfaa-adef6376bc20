import { MigrationInterface, QueryRunner } from 'typeorm';

export class SongTitleCount1753432294841 implements MigrationInterface {
	name = 'SongTitleCount1753432294841';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."user_count" ADD "song_title_count" integer NOT NULL DEFAULT '0'`,
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."user_count" DROP COLUMN "song_title_count"`,
		);
	}
}
