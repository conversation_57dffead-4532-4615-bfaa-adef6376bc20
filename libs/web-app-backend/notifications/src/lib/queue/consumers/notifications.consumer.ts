import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { NotificationEntity } from 'libs/web-app-backend/notifications/src/lib/types/entities/notification.entity';
import { WebAppGateway } from '@major/web-app-backend/util-websocket';
import { NotificationJob } from 'libs/web-app-backend/notifications/src/lib/types/interfaces/notification-job.interface';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { captureException } from '@sentry/nestjs';
import { WebSocketEvent } from '@major/shared/util-websocket/types';
import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';
import { UserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/user-presence.service';
import { AppMailerService } from 'libs/web-app-backend/mailer/src/lib/services/app-mailer.service';

@Processor('notifications')
export class NotificationsConsumer extends WorkerHost {
	private logger = new Logger(NotificationsConsumer.name);

	constructor(
		@InjectRepository(NotificationEntity)
		private notificationRepo: Repository<NotificationEntity>,
		private webAppGateway: WebAppGateway,
		private userPresenceService: UserPresenceService,
		@InjectEntityManager() private entityManager: EntityManager,
		private appMailerService: AppMailerService,
	) {
		super();
	}

	@OnWorkerEvent('active')
	onActive(job: Job<NotificationJob>) {
		this.logger.debug(`Processing deliver job ${JSON.stringify(job)}`);
	}

	@OnWorkerEvent('completed')
	onCompleted() {
		this.logger.debug('Finished processing delivery');
	}

	@OnWorkerEvent('failed')
	onFailed(job: Job<NotificationJob>, error: Error) {
		this.logger.error(
			`Failed job ${job.id} of type ${job.name}: ${error.message}`,
			error.stack,
		);

		// You can add custom logic here, like notifying an admin or logging to a service
		captureException(error, {
			data: {
				job,
			},
		});
	}

	async process(job: Job<NotificationJob>) {
		const { notificationId } = job.data;

		if (!notificationId) {
			this.logger.debug(
				`job.data has no notificationId ${JSON.stringify(job)}`,
			);
			throw new Error('notificationId was expected in NotificationJob');
		}

		const currentChannel: 'websocket' | 'email' = 'websocket';

		try {
			const notificationEntity = await this.notificationRepo.findOneOrFail({
				where: { id: notificationId },
			});

			const { userId, content, contentType, email } = notificationEntity;

			notificationEntity.deliveryStatus = 'processing';
			await this.notificationRepo.save(notificationEntity);

			// if invitee is existing user and they're connected
			// attempt websocket notification
			if (userId && (await this.userPresenceService.isUserOnline(userId))) {
				this.logger.debug(`User id=${userId} is connected`);

				let data: unknown;

				// Transform the InviteSongEntity
				if (contentType === 'InviteSong') {
					const invite = plainToInstance(InviteSongEntity, content);

					data = {
						...instanceToPlain(notificationEntity),
						content: instanceToPlain(invite, {
							enableCircularCheck: true,
							excludeExtraneousValues: true,
						}),
					};
				}
				// Transform the CollaborationEntity
				else if (contentType === 'Collaboration') {
					// fetch the invitee and inviter relations here
					// to ensure when instanceToPlain() is called
					// the relations are available
					const collaborationWithRelations = await this.entityManager.findOne(
						CollaborationEntity,
						{
							where: {
								id: content.id,
							},
							relations: ['invitee', 'inviter'],
						},
					);

					if (!collaborationWithRelations) {
						this.logger.error(
							`Could not find collaboration ${notificationEntity.referenceId} with relations`,
						);
						throw new Error('Collaboration entity was not found');
					}

					data = {
						...instanceToPlain(notificationEntity),
						content: instanceToPlain(collaborationWithRelations, {
							enableCircularCheck: true,
							excludeExtraneousValues: true,
						}),
					};
				}
				// Do nothing with the content as we don't know what type it is
				else {
					this.logger.log(
						`Not transforming notification content type "${notificationEntity.contentType}"`,
					);
					data = instanceToPlain(NotificationEntity);
				}

				await this.webAppGateway.sendToUser(userId, {
					event: WebSocketEvent.USER_NOTIFICATION,
					data,
				});

				notificationEntity.deliveryAttempts.push({
					timestamp: new Date(),
					channel: 'websocket',
				});
			} else {
				this.logger.debug(`Email fallback, user email=${email}`);

				await this.appMailerService.sendNotificationEmail(notificationEntity);

				notificationEntity.deliveryAttempts.push({
					timestamp: new Date(),
					channel: 'email',
				});
			}

			notificationEntity.deliveryStatus = 'delivered';
			notificationEntity.deliveredAt = new Date();
			await this.notificationRepo.save(notificationEntity);

			// Mark job as completed before returning
			return true;
		} catch (error: any) {
			this.logger.error(
				`Failed to deliver notification ${notificationId}`,
				error.stack,
			);

			captureException(error, {
				data: {
					notificationId,
				},
			});

			// Find notification entity again in case it wasn't found in the try block
			const notificationEntity = await this.notificationRepo.findOne({
				where: { id: notificationId },
			});

			if (notificationEntity) {
				notificationEntity.deliveryStatus = 'failed';
				notificationEntity.errorMessage = error.message;
				notificationEntity.deliveryAttempts.push({
					timestamp: new Date(),
					channel: currentChannel,
					error: error.message,
				});

				await this.notificationRepo.save(notificationEntity);
			}

			// Let BullMQ handle the error properly
			throw error;
		}
	}
}
