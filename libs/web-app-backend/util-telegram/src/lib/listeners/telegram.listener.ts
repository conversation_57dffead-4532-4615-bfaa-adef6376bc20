import { Inject, Injectable, Logger } from '@nestjs/common';
import { TelegramNotificationService } from '../services/telegram-notification.service';
import { OnEvent } from '@nestjs/event-emitter';
import { LogtoWebhookEvent } from '@major/web-app-backend/webhooks';
import type { PostSignInPayload } from 'libs/web-app-backend/users/src/lib/handlers/logto-post-sign-in-event-handler.service';
import { NODE_ENV } from 'web-app-backend/util-config';
import type { NodeEnv } from '@major/shared/models';
import { UserCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-created.event';
import { InviteAcceptedEvent, InviteCreatedEvent } from 'domain-events';
import { SongCopySentEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-copy-sent.event';

@Injectable()
export class TelegramListener {
	private logger = new Logger(TelegramListener.name);

	constructor(
		@Inject(NODE_ENV) private readonly nodeEnv: NodeEnv,
		private readonly telegramNotificationService: TelegramNotificationService,
	) {}

	@OnEvent(LogtoWebhookEvent.PostSignIn)
	async onPostSignIn(payload: PostSignInPayload) {
		if (this.nodeEnv === 'development') {
			return;
		}

		await this.telegramNotificationService.sendNotification({
			title: `${payload.user.primaryEmail} signed in`,
			data: {},
		});

		this.logger.debug('Sent "User signed in" notification to Telegram');
	}

	@OnEvent(UserCreatedEvent.eventName)
	async onUserCreated(event: UserCreatedEvent) {
		if (this.nodeEnv === 'development') {
			return;
		}

		const { user, source } = event.payload;

		if (source === 'api') {
			await this.telegramNotificationService.sendNotification({
				title: `${user.email} beta user signed up`,
				data: {},
			});

			return;
		}

		await this.telegramNotificationService.sendNotification({
			title: `${user.email} signed up`,
			data: {},
		});

		this.logger.debug('Sent "User signed up" notification to Telegram');
	}

	@OnEvent(InviteCreatedEvent.eventName)
	async onInviteCreated(event: InviteCreatedEvent) {
		if (this.nodeEnv === 'development') {
			return;
		}

		const { invite } = event.payload;

		await this.telegramNotificationService.sendNotification({
			title: `${invite.inviter.email} invited ${invite.getInviteeEmail()}`,
			data: {},
		});
	}

	@OnEvent(InviteAcceptedEvent.eventName)
	async onInviteAccepted(event: InviteAcceptedEvent) {
		if (this.nodeEnv === 'development') {
			return;
		}

		const { invite } = event.payload;

		await this.telegramNotificationService.sendNotification({
			title: `${invite.invitee.email} accepted invite from ${invite.inviter.email}`,
			data: {},
		});
	}

	@OnEvent(SongCopySentEvent.eventName)
	async onSongSendCopy(event: SongCopySentEvent) {
		if (this.nodeEnv === 'development') {
			return;
		}

		const { song, senderEmail, recipientEmail } = event.payload;

		await this.telegramNotificationService.sendNotification({
			title: `${senderEmail} sent copy of "${song.title}" to ${recipientEmail}`,
			data: {},
		});
	}
}
