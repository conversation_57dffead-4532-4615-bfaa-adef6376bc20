import { Modu<PERSON> } from '@nestjs/common';
import { SongController } from './controllers/song.controller';
import { SongEntityService } from './services/song-entity.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SongEntity } from './entities/song.entity';
import { SongCollaborationService } from './services/song-collaboration.service';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';
import { WebAppBackendUsersModule } from '@major/web-app-backend/users';
import { FileOrderEntity } from '@major/web-app-backend/songs/entities';
import { FileOrderService } from './services/file-order.service';
import { SongFileOrderController } from './controllers/song-file-order.controller';
import { SendCopyController } from './controllers/send-copy.controller';
import { BullModule } from '@nestjs/bullmq';
import { SendCopyConsumer } from 'libs/web-app-backend/songs/src/lib/queue/consumers/send-copy.consumer';
import { FileMetaDataEntity } from '@major/web-app-backend/file-meta-data/entities';
import { WebAppBackendPaymentsModule } from 'web-app-backend/payments';
import { SongScratchEntity } from './entities/song-scratch.entity';
import { ScratchController } from './controllers/scratch.controller';
import { SongUpdatedEventHandler } from './handlers/song-updated-event.handler';
import { WebAppBackendInvitesModule } from 'web-app-backend/invites';
import { WebAppBackendMailerModule } from 'mailer';

@Module({
	imports: [
		BullModule.registerQueue({
			name: 'send-copy',
			defaultJobOptions: {
				removeOnComplete: 10,
				removeOnFail: 10,
			},
		}),
		TypeOrmModule.forFeature([
			SongEntity,
			SongScratchEntity,
			CollaborationEntity,
			FileOrderEntity,
			FileMetaDataEntity,
		]),
		WebAppBackendUsersModule,
		WebAppBackendPaymentsModule,
		WebAppBackendInvitesModule,
		WebAppBackendMailerModule,
	],
	controllers: [
		SongController,
		SongFileOrderController,
		SendCopyController,
		ScratchController,
	],
	providers: [
		FileOrderService,
		SendCopyConsumer,
		SongCollaborationService,
		SongEntityService,
		SongUpdatedEventHandler,
	],
	exports: [SongEntityService, SongCollaborationService],
})
export class WebAppBackendSongsModule {}
