import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';
import { Server } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { SongUpdatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-updated.event';
import { logColorPrimary } from 'libs/web-app-backend/util-logging/src/lib/utils/cli-colors/index';
import { WebSocketEvent } from '@major/shared/util-websocket/types';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class SongDomainEventsGateway {
	private logger = new Logger(SongDomainEventsGateway.name);

	@WebSocketServer()
	server!: Server;

	@OnEvent(SongUpdatedEvent.eventName)
	async onSongUpdated(event: SongUpdatedEvent) {
		const { externalId, updateSongDto } = event.payload;

		this.logger.debug(
			`${logColorPrimary(SongUpdatedEvent.eventName)} ${JSON.stringify(event)}`,
		);

		/**
     * Here we want to detect song updates, but we don't want to broadcast
     * lyrics updates as they're being handled purely by broadcasting Prosemirror transactions
     * So we check to see if "lyrics" property is included - if so, ignore the event
     *
     * TODO
     *  what we could do here is just remove "lyrics" so user's work can never be
     *  overwritten by the server update
     if (event.updateSongDto.lyrics) {
     this.logger.debug(
     `Found "lyrics" property, ignoring ${SongUpdatedEvent.eventName}`,
     );

     return;
     }
     */

		this.server.to(`song:${externalId}`).emit(WebSocketEvent.SongUpdated, {
			data: {
				...updateSongDto,
				id: externalId,
			},
		});
	}
}
