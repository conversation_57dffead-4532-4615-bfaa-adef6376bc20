#!/usr/bin/env node
/**
 * Major Release Process Tool (Orchestrated by release-it)
 *
 * This script provides pre-flight checks and invokes release-it,
 * which handles the core release process including:
 * - Bumping version using conventional commits
 * - Generating CHANGELOG.md
 * - Running custom hooks for version.ts, changelog.json, deployment-info.json
 * - Ensuring version consistency (via hook)
 * - Creating GitLab releases with release notes
 *
 * Usage:
 *   bun run release.ts [options]
 */

import { execSync } from 'node:child_process';
import * as fs from 'node:fs';
import * as path from 'node:path';
import * as readline from 'node:readline'; // Added for user prompts
import { Command } from 'commander';
// @ts-ignore
import chalk from 'chalk';

interface ReleaseOptions {
	dryRun: boolean;
	versionType?: 'major' | 'minor' | 'patch'; // Optional now, release-it can infer
	skipGitCheck: boolean;
	skipValidation: boolean;
}

class ReleaseOrchestrator {
	private readonly rootDir: string;
	private readonly options: ReleaseOptions;
	private startTime = 0;
	private originalHead = ''; // To store HEAD before release-it runs

	constructor(options: ReleaseOptions) {
		this.rootDir = process.cwd();
		this.options = options;
	}

	public async execute(): Promise<void> {
		this.startTime = Date.now();
		console.log(chalk.bold.yellow('\n🚀 Starting Major Release Process 🚀\n'));

		try {
			// Capture the original HEAD commit before any operations
			this.originalHead = execSync('git rev-parse HEAD', {
				encoding: 'utf-8',
				cwd: this.rootDir,
			}).trim();

			if (!this.options.skipGitCheck) {
				this.checkGitStatus();
			}
			if (!this.options.skipValidation) {
				this.validateInitialFiles();
			}

			this.runReleaseIt(); // release-it now handles the bulk of the work

			const totalTime = Math.round((Date.now() - this.startTime) / 1000);
			console.log(
				chalk.bold.green(
					`\n✅ Release process orchestrated by release-it completed in ${totalTime} seconds! 🎉\n`,
				),
			);
		} catch (error) {
			console.error(chalk.bold.red('\n❌ Release process failed! ❌'));
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			console.error(chalk.red(errorMessage));
			// Add a note if the error came from release-it itself
			if (error instanceof Error && (error as any).stderr) {
				console.error(chalk.red('release-it stderr:'));
				console.error(chalk.red((error as any).stderr.toString()));
			}

			// Prompt user with cleanup suggestions
			await this.promptForCleanupSuggestions();
			process.exit(1); // Ensure script exits with error code after prompt
		}
	}

	private checkGitStatus(): void {
		console.log(
			chalk.bold.blue('\n🔍 Checking Git working directory status...\n'),
		);
		try {
			const status = execSync('git status --porcelain', {
				encoding: 'utf-8',
				cwd: this.rootDir,
			});
			if (status.trim() !== '') {
				throw new Error(
					'Git working directory is not clean. Please commit or stash your changes.',
				);
			}
			console.log(chalk.green('✅ Git working directory is clean.'));
		} catch (error) {
			if (error instanceof Error && error.message.includes('not clean')) {
				throw error;
			}
			throw new Error(`Failed to check Git status: ${error}`);
		}
	}

	private validateInitialFiles(): void {
		console.log(
			chalk.bold.blue('\n🔍 Validating initial files (pre-release-it)...\n'),
		);
		const changelogPath = path.join(this.rootDir, 'CHANGELOG.md');
		if (!fs.existsSync(changelogPath)) {
			console.warn(
				chalk.yellow('⚠️ CHANGELOG.md not found. release-it will create it.'),
			);
		} else {
			console.log(chalk.green('✅ CHANGELOG.md exists.'));
		}
	}

	private runReleaseIt(): void {
		console.log(chalk.bold.blue('\n📦 Handing over to release-it...\n'));
		let releaseItCommand = 'npx release-it';

		if (this.options.versionType) {
			releaseItCommand += ` ${this.options.versionType}`;
		}
		if (this.options.dryRun) {
			releaseItCommand += ' --dry-run';
		}

		console.log(chalk.blue(`Executing: ${releaseItCommand}`));
		execSync(releaseItCommand, { stdio: 'inherit', cwd: this.rootDir });
	}

	private async promptForCleanupSuggestions(): Promise<void> {
		const rl = readline.createInterface({
			input: process.stdin,
			output: process.stdout,
		});

		try {
			const currentHead = execSync('git rev-parse HEAD', {
				encoding: 'utf-8',
				cwd: this.rootDir,
			}).trim();
			const gitStatusOutput = execSync('git status --porcelain', {
				encoding: 'utf-8',
				cwd: this.rootDir,
			}).trim();

			const newCommitMadeByReleaseIt =
				this.originalHead && this.originalHead !== currentHead;
			const uncommittedChangesExist = gitStatusOutput !== '';

			if (!newCommitMadeByReleaseIt && !uncommittedChangesExist) {
				console.log(
					chalk.yellow(
						'\nNo new commits or uncommitted changes detected that seem related to this script run.',
					),
				);
				return; // No cleanup suggestions needed if state seems clean relative to script actions
			}

			console.log(
				chalk.yellow(
					'\n-------------------------------------------------------------------',
				),
			);
			console.log(
				chalk.yellow(
					'Failure detected. Please review the state of your repository.',
				),
			);
			if (newCommitMadeByReleaseIt) {
				console.log(
					chalk.yellow(
						`- A new commit (${currentHead.substring(
							0,
							7,
						)}) was likely created by the release process.`,
					),
				);
			}
			if (uncommittedChangesExist) {
				console.log(
					chalk.yellow(
						'- There are uncommitted changes and/or untracked files:',
					),
				);
				for (const line of gitStatusOutput.split('\n')) {
					console.log(chalk.gray(`  ${line}`));
				}
			}
			console.log(
				chalk.yellow(
					'-------------------------------------------------------------------\n',
				),
			);

			const answer = await new Promise<string>((resolve) => {
				rl.question(
					chalk.cyan(
						'❓ Would you like to see suggested Git commands to help clean up? [y/N]: ',
					),
					(ans) => resolve(ans.trim().toLowerCase()),
				);
			});

			if (answer === 'y') {
				console.log(
					chalk.magenta(
						'\nSuggested cleanup commands (run these manually if desired):',
					),
				);
				if (newCommitMadeByReleaseIt) {
					console.log(
						chalk.magenta(
							'\n  To undo the last commit (likely made by release-it):',
						),
					);
					console.log(chalk.white('    git reset HEAD~1'));
					console.log(
						chalk.gray(
							`    (This keeps changes in your working directory. Use 'git status' to see them.)`,
						),
					);
					console.log(chalk.white('    git reset --hard HEAD~1'));
					console.log(
						chalk.gray(
							'    (This discards changes from that commit. Be careful!)',
						),
					);
					console.log(
						chalk.magenta(
							`\n  To restore specific files from before this release attempt (e.g., package.json from commit ${this.originalHead.substring(0, 7)}):`,
						),
					);
					console.log(
						chalk.white(
							`    git checkout ${this.originalHead} -- package.json`,
						),
					);
				}
				if (uncommittedChangesExist) {
					console.log(
						chalk.magenta(
							'\n  To discard all current uncommitted changes to tracked files:',
						),
					);
					console.log(chalk.white('    git reset --hard HEAD'));
					console.log(
						chalk.gray(
							'    (Warning: This will discard all local modifications to tracked files.)',
						),
					);
					console.log(
						chalk.magenta('\n  To remove all untracked files and directories:'),
					);
					console.log(chalk.white('    git clean -fdx'));
					console.log(
						chalk.gray(
							`    (Warning: This is destructive. Use 'git clean -fdxn' for a dry run first.)`,
						),
					);
					if (!newCommitMadeByReleaseIt) {
						// If no commit was made by release-it, but package.json might have been changed by the bump
						console.log(
							chalk.magenta(
								'\n  If package.json was modified but not committed:',
							),
						);
						console.log(chalk.white('    git checkout -- package.json'));
					}
				}
				console.log(
					chalk.magenta(
						'\nAlways review your repository status with `git status` before running destructive commands.',
					),
				);
			} else {
				console.log(
					chalk.yellow(
						'Cleanup suggestions skipped. Please manually review and clean your repository.',
					),
				);
			}
		} catch (e) {
			console.error(
				chalk.red('Error during cleanup suggestion prompt:'),
				e instanceof Error ? e.message : e,
			);
		} finally {
			rl.close();
		}
	}
}

function setupCommandLine() {
	const program = new Command();
	program
		.name('release')
		.description('Major Release Process Tool (Orchestrated by release-it)')
		.version('1.0.0') // Version of this tool itself
		.option(
			'-d, --dry-run',
			'Run in dry-run mode (passed to release-it)',
			false,
		)
		.option('--major', 'Force a major version release', false)
		.option('--minor', 'Force a minor version release', false)
		.option('--patch', 'Force a patch version release', false)
		.option('--skip-git-check', 'Skip Git working directory check', false)
		.option('--skip-validation', 'Skip initial file validation', false)
		.action(async (cmdOptions) => {
			// Made action async to potentially await execute if needed
			let versionType: 'major' | 'minor' | 'patch' | undefined;
			if (cmdOptions.major) versionType = 'major';
			else if (cmdOptions.minor) versionType = 'minor';
			else if (cmdOptions.patch) versionType = 'patch';

			const releaseOptions: ReleaseOptions = {
				dryRun: cmdOptions.dryRun || false,
				versionType: versionType,
				skipGitCheck: cmdOptions.skipGitCheck || false,
				skipValidation: cmdOptions.skipValidation || false,
			};

			const orchestrator = new ReleaseOrchestrator(releaseOptions);
			// orchestrator.execute() will handle its own process.exit in case of error
			// so we don't strictly need to await it here if the script is meant to terminate.
			// However, if there were cleanup in setupCommandLine after execute, awaiting would be essential.
			try {
				await orchestrator.execute();
			} catch (e) {
				// This catch block in setupCommandLine might not be strictly necessary
				// if execute() handles all its errors and exits.
				// However, it's good practice for unhandled promise rejections.
				if (
					!(e instanceof Error && e.message.includes('process.exit called'))
				) {
					// Avoid double logging if execute already handled and exited.
					// This is a bit of a heuristic; a more robust way would be custom error types.
					console.error(
						chalk.red(
							'An unexpected error occurred in the release orchestrator.',
						),
						e,
					);
					process.exit(1);
				}
			}
		});
	program.parse(process.argv);
}

setupCommandLine();
