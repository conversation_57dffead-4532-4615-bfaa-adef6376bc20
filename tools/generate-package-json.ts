#!/usr/bin/env node

import * as fs from 'node:fs';
import * as path from 'node:path';

interface PackageJson {
	name: string;
	version: string;
	main?: string;
	dependencies?: Record<string, string>;
	devDependencies?: Record<string, string>;
	engines?: Record<string, string>;
}

interface MinimalPackageJson {
	name: string;
	version: string;
	main: string;
	scripts: Record<string, string>;
	dependencies: Record<string, string>;
	engines: Record<string, string>;
}

/**
 * Analyzes a built application's bundle and generates a minimal package.json
 * with only the external dependencies that are actually used.
 * @param distPath The path to the application's build output directory.
 * @param originalPackageJsonPath The path to the root package.json.
 */
async function generateMinimalPackageJson(
	distPath: string,
	originalPackageJsonPath: string,
): Promise<MinimalPackageJson> {
	// Read the original package.json
	const originalPackageJson: PackageJson = JSON.parse(
		await fs.promises.readFile(originalPackageJsonPath, 'utf8'),
	);

	// Find all JS files in the dist directory
	const jsFiles = await findJsFiles(distPath);

	// Find the main entry file (e.g., main.a1b2c3d4.js)
	const mainFile = jsFiles.find((file) => {
		const basename = path.basename(file);
		return basename.startsWith('main.') && basename.endsWith('.js');
	});

	if (!mainFile) {
		throw new Error(
			`Could not find main entry file in ${distPath}. Expected a file matching main.*.js pattern.`,
		);
	}

	const mainFileName = path.basename(mainFile);

	// Extract all require/import statements to find used dependencies
	const usedDependencies = new Set<string>();
	const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
	const importRegex = /from\s+['"`]([^'"`]+)['"`]/g;

	for (const filePath of jsFiles) {
		const content = await fs.promises.readFile(filePath, 'utf8');
		let match: any;

		// Find require() calls
		// biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
		while ((match = requireRegex.exec(content)) !== null) {
			const dep = match[1];
			if (isExternalDependency(dep)) {
				usedDependencies.add(getPackageName(dep));
			}
		}

		// Find from '...' statements
		// biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
		while ((match = importRegex.exec(content)) !== null) {
			const dep = match[1];
			if (isExternalDependency(dep)) {
				usedDependencies.add(getPackageName(dep));
			}
		}
	}

	// This is a curated list of dependencies that might be bundled or dynamically
	// loaded by frameworks like NestJS, so static analysis might miss them.
	// They are added to ensure the production build runs correctly.
	const essentialDependencies = [
		'date-fns',
		'reflect-metadata',
		'tslib',
		'@nestjs/platform-express',
		'@nestjs/microservices',
		'@nestjs/platform-ws',
		'@nestjs/testing',
		'express',
		'cors',
		'helmet',
		// Database drivers - required for TypeORM at runtime
		'pg',
		'@types/pg',
	];

	// Combine detected and essential dependencies
	const finalDependencies = new Set([
		// @ts-ignore
		...usedDependencies,
		...essentialDependencies,
	]);

	const allDependencies = {
		...originalPackageJson.dependencies,
		...originalPackageJson.devDependencies,
	};

	const minimalDependencies: Record<string, string> = {};

	// @ts-ignore
	for (const dep of finalDependencies) {
		if (allDependencies[dep]) {
			minimalDependencies[dep] = allDependencies[dep];
		}
	}

	// Create minimal package.json
	const minimalPackageJson: MinimalPackageJson = {
		name: `${originalPackageJson.name}-${path.basename(distPath)}`,
		version: originalPackageJson.version,
		main: mainFileName,
		scripts: {
			start: `bun ${mainFileName}`,
		},
		dependencies: minimalDependencies,
		engines: originalPackageJson.engines || {},
	};

	// Write the minimal package.json to the dist directory
	const outputPath = path.join(distPath, 'package.json');
	await fs.promises.writeFile(
		outputPath,
		JSON.stringify(minimalPackageJson, null, 2),
	);

	console.log(
		`✅ Generated minimal package.json for ${path.basename(distPath)} with ${Object.keys(minimalPackageJson.dependencies).length} dependencies.`,
	);
	console.log(
		`   📦 Dependencies: ${Object.keys(minimalPackageJson.dependencies).join(', ')}`,
	);

	return minimalPackageJson;
}

async function findJsFiles(dir: string): Promise<string[]> {
	const jsFiles: string[] = [];

	async function traverse(currentDir: string): Promise<void> {
		const files = await fs.promises.readdir(currentDir);

		for (const file of files) {
			const filePath = path.join(currentDir, file);
			const stat = await fs.promises.stat(filePath);

			if (stat.isDirectory()) {
				await traverse(filePath);
			} else if (file.endsWith('.js')) {
				jsFiles.push(filePath);
			}
		}
	}

	await traverse(dir);
	return jsFiles;
}

function isExternalDependency(dep: string): boolean {
	// It's an external dependency if it's not a relative/absolute path.
	return !dep.startsWith('.') && !dep.startsWith('/') && !path.isAbsolute(dep);
}

function getPackageName(dep: string): string {
	// Extracts the base package name.
	// e.g., '@nestjs/common/decorators' -> '@nestjs/common'
	// e.g., 'rxjs/operators' -> 'rxjs'
	if (dep.startsWith('@')) {
		const parts = dep.split('/');
		return `${parts[0]}/${parts[1]}`;
	}
	return dep.split('/')[0];
}

// --- CLI Usage ---
if (require.main === module) {
	const appName = process.argv[2];
	if (!appName) {
		console.error(
			'❌ Error: Please provide an application name as an argument.',
		);
		console.error('   Usage: bun tools/generate-package-json.ts <app-name>');
		process.exit(1);
	}

	const distPath = path.join('dist/apps', appName);
	const packageJsonPath = process.argv[3] || 'package.json';

	if (!fs.existsSync(distPath)) {
		console.error(`❌ Error: Dist path does not exist: ${distPath}`);
		process.exit(1);
	}

	if (!fs.existsSync(packageJsonPath)) {
		console.error(`❌ Error: package.json does not exist: ${packageJsonPath}`);
		process.exit(1);
	}

	generateMinimalPackageJson(distPath, packageJsonPath).catch((err) => {
		console.error(err);
		process.exit(1);
	});
}

export { generateMinimalPackageJson };
