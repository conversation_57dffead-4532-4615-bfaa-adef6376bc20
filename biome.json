{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "files": {"ignore": ["./package.json", "./package-lock.json"]}, "javascript": {"parser": {"unsafeParameterDecoratorsEnabled": true}, "formatter": {"quoteStyle": "single"}}, "linter": {"enabled": true, "rules": {"complexity": {"useLiteralKeys": "off", "noStaticOnlyClass": "off"}, "style": {"useImportType": "off", "noNonNullAssertion": "off"}, "suspicious": {"noExplicitAny": "off", "noFocusedTests": "off"}, "nursery": {"useSortedClasses": "error"}}}, "organizeImports": {"enabled": false}}