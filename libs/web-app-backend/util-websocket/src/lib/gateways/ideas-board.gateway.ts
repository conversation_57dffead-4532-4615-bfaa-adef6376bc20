import {
	Logger,
	NotFoundException,
	UnauthorizedException,
	UseGuards,
} from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import {
	MessageBody,
	SubscribeMessage,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { WebSocketEvent } from '@major/shared/util-websocket/types';
import { instanceToPlain } from 'class-transformer';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';
import { JwtWsUser } from 'libs/web-app-backend/util-websocket/src/lib/decorators/ws-user.decorator';
import type { CurrentWebSocketUser } from '@major/web-app-backend/util-websocket/types';
import { omit } from 'lodash';
import { IdeaEntity } from 'libs/web-app-backend/ideas/src/lib/entities/index';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { IdeaEntityService } from '@major/web-app-backend/ideas';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class IdeasBoardGateway {
	private logger = new Logger(IdeasBoardGateway.name);

	@WebSocketServer()
	server!: Server;

	constructor(
		@InjectDataSource() private dataSource: DataSource,
		private ideaEntityService: IdeaEntityService,
	) {}

	@SubscribeMessage(WebSocketEvent.IdeasBoardCreate)
	async ideasBoardCreateIdea(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: {
			songExternalId: string;
			text: string;
			isGenerated: boolean;
		},
	) {
		try {
			const idea = await this.ideaEntityService.create({
				uid: user.id,
				text: payload.text,
				isGenerated: payload.isGenerated,
				songExternalId: payload.songExternalId,
			});

			idea.currentUser = omit(user, 'client');

			this.logger.debug(`Created idea ${JSON.stringify(idea)}`);

			// now send to other clients
			this.server
				.to(`song:${payload.songExternalId}`)
				.emit(WebSocketEvent.IdeasBoardIdeaCreated, {
					data: instanceToPlain(idea),
				});
		} catch (e) {
			this.logger.error(e);
			throw e;
		}
	}

	@SubscribeMessage(WebSocketEvent.IdeasBoardRemove)
	async ideasBoardRemoveIdea(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: {
			id: string;
		},
	) {
		try {
			const idea = await this.ideaEntityService.findOne(payload.id);

			if (!idea) {
				throw new NotFoundException('Idea not found');
			}

			if (idea.owner.id !== user.id) {
				throw new UnauthorizedException(
					'Cannot delete other collaborators ideas',
				);
			}

			await this.ideaEntityService.remove(idea);

			// now send to other clients
			this.server
				.to(`song:${idea.song.externalId}`)
				.emit(WebSocketEvent.IdeasBoardIdeaRemoved, {
					data: idea.id,
				});
		} catch (e) {
			this.logger.error(e);
			throw e;
		}
	}

	@SubscribeMessage(WebSocketEvent.IdeasBoardAddReaction)
	async ideasBoardAddReaction(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { ideaId: string; emoji: string },
	) {
		const userId = user.userId;
		const { ideaId, emoji } = payload;

		const ideaRepo = this.dataSource.getRepository(IdeaEntity);
		const idea = await ideaRepo.findOneOrFail({
			where: { id: ideaId },
			relations: { song: true, owner: true },
		});

		idea.reactions.push({
			emoji,
			userId,
			createdAt: new Date(),
		});

		await ideaRepo.save(idea);

		user.client
			.to(`song:${idea.song.externalId}`)
			.emit(WebSocketEvent.IdeasBoardIdeaUpdated, {
				data: instanceToPlain(idea),
			});
	}

	@SubscribeMessage(WebSocketEvent.IdeasBoardRemoveReaction)
	async ideasBoardRemoveReaction(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { ideaId: string; emoji: string },
	) {
		const userId = user.userId;
		const { ideaId, emoji } = payload;

		const ideaRepo = this.dataSource.getRepository(IdeaEntity);
		const idea = await ideaRepo.findOneOrFail({
			where: { id: ideaId },
			relations: { song: true, owner: true },
		});

		idea.reactions = idea.reactions.filter(
			(r) => !(r.userId === userId && r.emoji === emoji),
		);

		await ideaRepo.save(idea);

		user.client
			.to(`song:${idea.song.externalId}`)
			.emit(WebSocketEvent.IdeasBoardIdeaUpdated, {
				data: instanceToPlain(idea),
			});
	}
}
