import { InviteEventHandlerBase } from './invite-event-handler.base';
import { OnEvent } from '@nestjs/event-emitter';
import { RefreshScope } from '@major/shared/models';
import { instanceToPlain } from 'class-transformer';
import { Injectable } from '@nestjs/common';
import { MissingRelationException } from '@major/shared/models/classes';
import { InviteAcceptedEvent } from 'domain-events';
import { captureException } from '@sentry/nestjs';

@Injectable()
export class InviteAcceptedHandler extends InviteEventHandlerBase {
	/**
	 * @param event
	 */
	@OnEvent(InviteAcceptedEvent.eventName)
	async onInviteAccepted(event: InviteAcceptedEvent) {
		const { invite } = event.payload;

		if (!invite.invitee) {
			captureException(
				new MissingRelationException(
					'"invite.invitee" relation not present - was invitee a non-user?',
				),
				{
					data: { event },
				},
			);
		}

		if (!invite.inviter) {
			throw new MissingRelationException('"inviter" relation required');
		}

		if (!invite.song.collaborations) {
			throw new MissingRelationException(
				'"invite.song.collaborations" relation required',
			);
		}

		await this.markInviteeNotificationAsRead(invite);

		const acceptedNotification =
			await this.notificationFactory.createInviteAcceptedNotification(invite);

		await this.notificationProducer.addDeliveryJob(acceptedNotification);

		//
		// Now sent UI refresh websocket messages to both the inviter user
		// plus all the other collaborators already present on the song
		//
		const group =
			await this.collaborationGroupFactory.createFromInviteSong(invite);

		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.INVITES_SENT,
			targetUserIds: [invite.inviter.id],
		});

		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.INVITES_BY_SONG,
			targetUserIds: [invite.inviter.id],
			context: {
				id: invite.song.externalId,
			},
		});

		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.SONG_DETAIL,
			targetUserIds: group.otherUserIds,
			context: {
				id: invite.song.externalId,
				update: {
					// only send the updated collaborations
					// as we don't want to update anything else
					// that the user may have mutated and not saved
					collaborations: instanceToPlain(invite.song.collaborations, {
						groups: ['detail'],
					}),
				},
			},
		});

		this.logger.debug(`Invite accepted id=${invite.id}`);
	}
}
