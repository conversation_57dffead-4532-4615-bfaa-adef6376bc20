import { Global, Module } from '@nestjs/common';
import { WebAppGateway } from './gateways/web-app.gateway';
import { UserPresenceService } from './services/user-presence.service';
import { RefreshMessageService } from './services/refresh-message.service';
import { EditorUserPresenceService } from './services/editor-user-presence.service';
import { WebAppBackendIdeasModule } from '@major/web-app-backend/ideas';
import { AllExceptionsFilter } from './filters/all-exceptions.filter';
import { VideoCallGateway } from './gateways/video-call.gateway';
import { InviteEventsGateway } from './gateways/invite-events.gateway';
import { SongDomainEventsGateway } from './gateways/song-domain-events.gateway';
import { EditorGateway } from './gateways/editor.gateway';
import { EditorChatGateway } from './gateways/editor-chat.gateway';
import { IdeasBoardGateway } from './gateways/ideas-board.gateway';
import { ImportGateway } from './gateways/import.gateway';
import { RecorderGateway } from './gateways/recorder.gateway';

@Global()
@Module({
	providers: [
		AllExceptionsFilter,
		EditorUserPresenceService,
		InviteEventsGateway,
		RefreshMessageService,
		UserPresenceService,

		EditorGateway,
		EditorChatGateway,
		IdeasBoardGateway,
		ImportGateway,
		InviteEventsGateway,
		RecorderGateway,
		SongDomainEventsGateway,
		VideoCallGateway,
		WebAppGateway,
	],
	imports: [WebAppBackendIdeasModule],
	exports: [
		EditorUserPresenceService,
		RefreshMessageService,
		UserPresenceService,
		WebAppGateway,
	],
})
export class WebAppBackendUtilWebsocketModule {}
