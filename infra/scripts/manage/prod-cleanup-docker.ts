#!/usr/bin/env bun

import { Command } from 'commander';
import chalk from 'chalk';
import readline from 'node:readline';
import {
  ensureDockerContext,
  getContainerUsageByImageName,
  getInUseImageDigests,
  getLocalImages,
  pruneDanglingImages,
  pruneStoppedContainers,
  removeImageById,
} from '../utils/docker-utils';
import { logError, logInfo, logStep, logSuccess, logWarning } from '../utils/log-utils';

// --- Configuration ---
const TARGET_CONTEXT = 'major-prod';
const SCRIPT_NAME = 'prod-cleanup-docker';
const IMAGE_REPO_FILTER = 'registry.gitlab.com/johngrimsey/major';
const GIT_COMMIT_HASH_REGEX = /^[a-f0-9]{7,12}$/;
const DEFAULT_IMAGES_TO_KEEP = 5;

const STACK_NAMES = [
  'app-backend-prod',
  'app-frontend-prod',
  'auth-prod',
  'data-prod',
  'traefik-prod',
];

// --- Helper Functions ---

function askForConfirmation(query: string): Promise<boolean> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// --- Main Script Logic ---

async function main() {
  const program = new Command();

  program
    .name(SCRIPT_NAME)
    .version('1.1.0') // Incremented version for new logic
    .description(
      chalk.blue(
        'Previews and removes old, unused Docker images from the production environment.',
      ),
    )
    .option(
      '--dry-run',
      'Show images that would be removed, but do not delete them.',
    )
    .option(
      '-y, --yes',
      'Automatically confirm and proceed with deletion without prompting.',
    )
    .option(
      '-k, --keep <count>',
      `Number of recent release images to keep, even if unused.`,
      (val) => parseInt(val, 10),
      DEFAULT_IMAGES_TO_KEEP,
    )
    .action(async (options) => {
      console.log(
        chalk.bold.cyan('\n🧹 Starting Production Docker Cleanup...'),
      );
      console.log('--------------------------------------------------');

      try {
        // 1. Ensure Correct Docker Context
        await ensureDockerContext(TARGET_CONTEXT, options.dryRun);
        console.log('--------------------------------------------------');

        // --- DATA GATHERING (for both real and dry runs) ---

        logStep('Gathering information about all images and containers...');

        // 2. Get all local images for the project and map names to digests
        const allImages = await getLocalImages(IMAGE_REPO_FILTER);
        const imageNameToDigestMap = new Map<string, string>();
        allImages.forEach((img) => {
          const fullName = `${img.repository}:${img.tag}`;
          if (img.digest && img.digest !== '<none>') {
            imageNameToDigestMap.set(fullName, img.digest);
          }
        });
        logInfo(`Found ${allImages.length} total images for this repository.`);

        // 3. Get image digests from active Swarm services
        const serviceDigests = new Set<string>();
        for (const stackName of STACK_NAMES) {
          const stackDigests = await getInUseImageDigests(stackName);
          stackDigests.forEach((digest) => serviceDigests.add(digest));
        }
        logInfo(
          `Found ${serviceDigests.size} unique image digests in use by active services.`,
        );

        // 4. Get image usage from all containers (running and stopped)
        const containerUsage = await getContainerUsageByImageName();
        const runningContainerDigests = new Set<string>();
        containerUsage.running.forEach((imageName) => {
          const digest = imageNameToDigestMap.get(imageName);
          if (digest) runningContainerDigests.add(digest);
        });
        logInfo(
          `Found ${runningContainerDigests.size} unique image digests used by RUNNING containers.`,
        );
        logInfo(
          `Found ${containerUsage.stopped.size} unique image names used by STOPPED containers.`,
        );
        console.log('--------------------------------------------------');

        // --- ANALYSIS (for both real and dry runs) ---

        // 5. Determine which images are truly in use (by services OR running containers)
        const inUseDigests = new Set([
          ...serviceDigests,
          ...runningContainerDigests,
        ]);
        logInfo(`Total unique image digests considered "in-use": ${inUseDigests.size}`);

        // 6. Identify old and unused release images
        logStep('Identifying old and unused release images for deletion...');
        const releaseImages = allImages.filter((img) =>
          GIT_COMMIT_HASH_REGEX.test(img.tag),
        );

        // An image is "unused" if its digest is not in the `inUseDigests` set.
        // This correctly includes images that are only used by STOPPED containers.
        const unusedImages = releaseImages.filter(
          (img) => !inUseDigests.has(img.digest),
        );

        // Sort by creation date, newest first
        unusedImages.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );

        const imagesToKeepCount = options.keep;
        const imagesToDelete =
          imagesToKeepCount > 0
            ? unusedImages.slice(imagesToKeepCount)
            : unusedImages;

        // --- PREVIEW & EXECUTION ---

        // 7. Preview Deletion
        if (imagesToDelete.length === 0) {
          logSuccess('✅ No old, unused release images found to delete.');
          console.log('--------------------------------------------------');
          return;
        }

        logWarning(
          `Found ${imagesToDelete.length} unused release images to delete.`,
        );
        if (imagesToKeepCount > 0) {
          logInfo(
            `Keeping the ${imagesToKeepCount} most recent unused release images.`,
          );
        }

        console.log('\nImages to be removed:');
        console.log(
          chalk.bold(
            `  ${'ID'.padEnd(15)} ${'Tag'.padEnd(15)} ${'Size'.padEnd(
              12,
            )} ${'Created'.padEnd(25)}`,
          ),
        );
        let totalSize = 0;
        for (const image of imagesToDelete) {
          totalSize += image.sizeBytes;
          console.log(
            `  ${image.id.padEnd(15)} ${image.tag.padEnd(
              15,
            )} ${formatBytes(image.sizeBytes).padEnd(12)} ${image.createdAt.padEnd(25)}`,
          );
        }
        console.log('--------------------------------------------------');
        logInfo(
          `Total space to be reclaimed: ${chalk.green(formatBytes(totalSize))}`,
        );
        console.log('--------------------------------------------------');

        // 8. Handle Dry Run
        if (options.dryRun) {
          logSuccess('[Dry Run] No containers or images will be deleted.');
          return;
        }

        // 9. Get Confirmation
        const confirmed =
          options.yes ||
          (await askForConfirmation(
            chalk.yellow(
              `\nAre you sure you want to permanently delete these ${imagesToDelete.length} images? (y/N) `,
            ),
          ));

        if (!confirmed) {
          logInfo('Operation cancelled by user.');
          return;
        }

        // --- EXECUTION (Real Run Only) ---

        // 10. Prune stopped containers and dangling images FIRST
        logStep(
          '\nPruning stopped containers and dangling images to free up old image references...',
        );
        await pruneStoppedContainers();
        await pruneDanglingImages();
        console.log('--------------------------------------------------');

        // 11. Delete Images
        logStep('Deleting identified unused images...');
        let deletedCount = 0;
        for (const image of imagesToDelete) {
          try {
            await removeImageById(image.id);
            logSuccess(`  Deleted ${image.id} (${image.tag})`);
            deletedCount++;
          } catch (error) {
            logError(`  Failed to delete ${image.id} (${image.tag})`);
          }
        }
        console.log('--------------------------------------------------');

        // 12. Final Summary
        logSuccess(
          `\n✨ Cleanup complete. Deleted ${deletedCount} of ${imagesToDelete.length} targeted images.`,
        );
      } catch (error: any) {
        logError(`\n❌ An error occurred during the cleanup process.`);
        logError(error.message);
        process.exit(1);
      }
    });

  await program.parseAsync(process.argv);
}

main();
