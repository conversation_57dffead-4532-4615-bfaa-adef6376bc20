import { Test, TestingModule } from '@nestjs/testing';
import { UserSubscriptionChangedHandler } from './user-subscription-changed.handler';
import { UserPlanTransitionService } from 'libs/web-app-backend/user-quotas/src/lib/services/user-plan-transition.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StripeSubscriptionEntity } from 'web-app-backend/payments/entities';
import { UserPlanName } from '@major/shared/models';
import { createMock } from '@golevelup/ts-jest';
import { Repository } from 'typeorm';
import { Logger, InternalServerErrorException } from '@nestjs/common';
import * as Sentry from '@sentry/nestjs';
import { UserSubscriptionChangedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-subscription-changed.event';

// Mock Sentry's captureException
jest.mock('@sentry/nestjs', () => ({
	captureException: jest.fn(),
}));

// Mock Logger
const mockLogger = {
	log: jest.fn(),
	error: jest.fn(),
	warn: jest.fn(),
	debug: jest.fn(),
};

describe('UserSubscriptionChangedHandler', () => {
	let handler: UserSubscriptionChangedHandler;
	let mockUpdateQuotasService: jest.Mocked<UserPlanTransitionService>;
	let mockStripeRepo: jest.Mocked<Repository<StripeSubscriptionEntity>>;

	const basePayload: StripeSubscriptionEntity = {
		id: 1,
		userId: 123,
		stripeCustomerId: 'cus_123',
		stripeSubscriptionId: 'sub_123',
		currency: 'usd',
		stripeProductId: 'prod_free',
		stripePriceId: 'price_free',
		status: 'active',
		planName: 'free', // This will be the old plan name in tests
		// Mocked nested properties if needed by planName transform
		product: { name: 'free' } as any,
		price: {} as any,
		cancelledAt: null,
		effectiveEndDate: null,
		createdAt: new Date(),
		updatedAt: new Date(),
		planFeatures: {} as any, // Mock if needed
	};

	const event = new UserSubscriptionChangedEvent({
		subscription: basePayload,
	});

	beforeEach(async () => {
		// Reset mocks before each test
		jest.clearAllMocks();

		// Create fresh mocks for each test to avoid interference
		mockUpdateQuotasService = createMock<UserPlanTransitionService>();
		mockStripeRepo = createMock<Repository<StripeSubscriptionEntity>>();

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				UserSubscriptionChangedHandler,
				{
					provide: UserPlanTransitionService,
					useValue: mockUpdateQuotasService,
				},
				{
					provide: getRepositoryToken(StripeSubscriptionEntity),
					useValue: mockStripeRepo,
				},
				{
					provide: Logger,
					useValue: mockLogger, // Use the globally mocked logger
				},
			],
		}).compile();

		handler = module.get<UserSubscriptionChangedHandler>(
			UserSubscriptionChangedHandler,
		);
		// Manually inject logger if not done via constructor for testing
		(handler as any).logger = mockLogger;
	});

	describe('onSubscriptionStatusChange()', () => {
		it('it must correctly transition quotas when plan changes from FREE to PLUS', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				id: 1,
				userId: 123,
				planName: 'free',
				product: { name: 'free' } as any,
			};

			const updatedSubscription: StripeSubscriptionEntity = {
				...basePayload,
				id: 1, // Same DB ID
				userId: 123,
				planName: 'plus',
				product: { name: 'plus' } as any,
			};

			mockStripeRepo.findOneBy.mockResolvedValue(updatedSubscription);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockStripeRepo.findOneBy).toHaveBeenCalledWith({
				id: oldPlanPayload.id,
			});

			expect(mockUpdateQuotasService.transitionUserPlan).toHaveBeenCalledWith(
				123,
				UserPlanName.PLUS,
				UserPlanName.FREE,
			);

			expect(mockLogger.log).toHaveBeenCalledWith(
				`User 123 plan changed from ${UserPlanName.FREE} to ${UserPlanName.PLUS}. Updating quotas.`,
			);

			expect(Sentry.captureException).not.toHaveBeenCalled();
		});

		it('it must not call transitionUserPlan if the plan name does not change', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				id: 2,
				userId: 456,
				planName: 'plus',
				product: { name: 'plus' } as any,
			};

			const updatedSubscription: StripeSubscriptionEntity = {
				...basePayload,
				id: 2,
				userId: 456,
				planName: 'plus',
				product: { name: 'plus' } as any,
			};

			mockStripeRepo.findOneBy.mockResolvedValue(updatedSubscription);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockStripeRepo.findOneBy).toHaveBeenCalledWith({
				id: oldPlanPayload.id,
			});

			expect(mockUpdateQuotasService.transitionUserPlan).not.toHaveBeenCalled();

			expect(mockLogger.log).toHaveBeenCalledWith(
				'User 456 plan unchanged (plus), no quota update needed.',
			);

			expect(Sentry.captureException).not.toHaveBeenCalled();
		});

		it('it must log error and capture exception if old plan name is invalid', async () => {
			const invalidOldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'invalid-plan-name',
				product: { name: 'invalid-plan-name' } as any,
			};

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: invalidOldPlanPayload,
				}),
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				`Could not derive OLD UserPlanName from StripeSubscriptionEntity.planName: invalid-plan-name for userId: ${basePayload.userId}`,
				JSON.stringify(invalidOldPlanPayload),
			);

			expect(Sentry.captureException).toHaveBeenCalledWith(
				expect.any(InternalServerErrorException),
				expect.anything(),
			);

			expect(mockStripeRepo.findOneBy).not.toHaveBeenCalled();

			expect(mockUpdateQuotasService.transitionUserPlan).not.toHaveBeenCalled();
		});

		it('it must log error and capture exception if updated subscription is not found', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'free',
				product: { name: 'free' } as any,
			};

			mockStripeRepo.findOneBy.mockResolvedValue(null);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				`Could not find updated StripeSubscriptionEntity with id: ${oldPlanPayload.id} for userId: ${basePayload.userId} after status change event.`,
			);

			expect(Sentry.captureException).toHaveBeenCalledWith(
				expect.any(InternalServerErrorException),
				expect.anything(),
			);

			expect(mockUpdateQuotasService.transitionUserPlan).not.toHaveBeenCalled();
		});

		it('it must log error and capture exception if fetching updated subscription fails', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'free',
				product: { name: 'free' } as any,
			};
			const fetchError = new Error('DB connection error');
			mockStripeRepo.findOneBy.mockRejectedValue(fetchError);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				`Error fetching updated StripeSubscriptionEntity for id: ${oldPlanPayload.id}, userId: ${basePayload.userId}`,
				fetchError,
			);

			expect(Sentry.captureException).toHaveBeenCalledWith(
				fetchError,
				expect.anything(),
			);

			expect(mockUpdateQuotasService.transitionUserPlan).not.toHaveBeenCalled();
		});

		it('it must log error and capture exception if new plan name is invalid', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'free',
				product: { name: 'free' } as any,
			};

			const updatedSubscriptionInvalidNew: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'invalid-new-plan',
				product: { name: 'invalid-new-plan' } as any,
			};

			mockStripeRepo.findOneBy.mockResolvedValue(updatedSubscriptionInvalidNew);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				`Could not derive NEW UserPlanName from updated StripeSubscriptionEntity.planName: invalid-new-plan for userId: ${basePayload.userId}`,
				JSON.stringify(updatedSubscriptionInvalidNew),
			);

			expect(Sentry.captureException).toHaveBeenCalledWith(
				expect.any(InternalServerErrorException),
				expect.anything(),
			);

			expect(mockUpdateQuotasService.transitionUserPlan).not.toHaveBeenCalled();
		});

		it('it must log error and capture exception if transitionUserPlan throws an error', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'free',
				product: { name: 'free' } as any,
			};

			const updatedSubscription: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'plus',
				product: { name: 'plus' } as any,
			};

			const transitionError = new Error('Quota update failed');

			mockStripeRepo.findOneBy.mockResolvedValue(updatedSubscription);

			mockUpdateQuotasService.transitionUserPlan.mockRejectedValue(
				transitionError,
			);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockUpdateQuotasService.transitionUserPlan).toHaveBeenCalledWith(
				basePayload.userId,
				UserPlanName.PLUS,
				UserPlanName.FREE,
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				`Error updating quotas for user ${basePayload.userId} transitioning from ${UserPlanName.FREE} to ${UserPlanName.PLUS}`,
				transitionError,
			);

			expect(Sentry.captureException).toHaveBeenCalledWith(
				transitionError,
				expect.anything(),
			);
		});

		it('it must correctly handle plan name casing (e.g., Free, Plus, Pro)', async () => {
			const oldPlanPayload: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'Free', // Mixed case
				product: { name: 'Free' } as any,
			};
			const updatedSubscription: StripeSubscriptionEntity = {
				...basePayload,
				planName: 'Plus', // Mixed case
				product: { name: 'Plus' } as any,
			};

			mockStripeRepo.findOneBy.mockResolvedValue(updatedSubscription);

			await handler.onSubscriptionStatusChange(
				new UserSubscriptionChangedEvent({
					subscription: oldPlanPayload,
				}),
			);

			expect(mockUpdateQuotasService.transitionUserPlan).toHaveBeenCalledWith(
				basePayload.userId,
				UserPlanName.PLUS,
				UserPlanName.FREE,
			);
		});
	});
});
