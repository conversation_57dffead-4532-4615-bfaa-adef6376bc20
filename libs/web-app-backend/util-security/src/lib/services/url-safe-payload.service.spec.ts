import { Test, TestingModule } from '@nestjs/testing';
import { UrlSafePayloadService } from 'libs/web-app-backend/util-security/src/lib/services/url-safe-payload.service';
import { InvitationStatus } from '@major/shared/models';
import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { createMock } from '@golevelup/ts-jest';
import { plainToInstance } from 'class-transformer';
import {
	INVITE_SECRET_KEY,
	WEB_APP_PATH_INVITE_LANDING,
} from 'web-app-backend/util-config';

describe('UrlSafePayloadService', () => {
	let service: UrlSafePayloadService;

	const data = {
		id: 1,
		song: {
			id: 1,
		},
		status: InvitationStatus.Pending,
		expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
		inviter: { id: 1 },
		inviteeEmail: '<EMAIL>',
	};

	const invite: InviteSongEntity = plainToInstance(InviteSongEntity, data);

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				UrlSafePayloadService,
				{
					provide: INVITE_SECRET_KEY,
					useValue: 'test',
				},
				{
					provide: WEB_APP_PATH_INVITE_LANDING,
					useValue: 'http://localhost/invites',
				},
				{
					provide: getRepositoryToken(InviteSongEntity),
					useValue: createMock<Repository<InviteSongEntity>>(),
				},
			],
		}).compile();

		service = module.get(UrlSafePayloadService);
	});

	it('should create', () => {
		expect(service).toBeInstanceOf(UrlSafePayloadService);
	});

	describe('createUrlSafePayload()', () => {
		it('must create invite URL', () => {
			const result = service.createUrlSafePayload(invite);
			expect(result).toMatch(/^[a-zA-Z0-9]+/);
		});
	});

	describe('validateUrlPayload()', () => {
		it('must correctly decode invite URL', () => {
			const expected = {
				id: 1,
				inviteeDisplayName: '<EMAIL>',
				song: { id: 1 },
				inviteeEmail: '<EMAIL>',
				inviter: { id: 1 },
				status: InvitationStatus.Pending,
			};

			const url = service.createUrlSafePayload(invite);
			const decoded = service.validateUrlPayload(url);

			expect(decoded).toMatchObject(expected);
		});
	});
});
