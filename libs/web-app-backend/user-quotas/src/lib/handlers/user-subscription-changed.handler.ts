import {
	Injectable,
	InternalServerErrorException,
	Logger,
} from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StripeSubscriptionEntity } from 'web-app-backend/payments/entities';
import { UserPlanName } from '@major/shared/models';
import { UserPlanTransitionService } from 'libs/web-app-backend/user-quotas/src/lib/services/user-plan-transition.service';
import { captureException } from '@sentry/nestjs';
import { UserSubscriptionChangedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-subscription-changed.event';

@Injectable()
export class UserSubscriptionChangedHandler {
	private logger = new Logger(UserSubscriptionChangedHandler.name);

	constructor(
		@InjectRepository(StripeSubscriptionEntity)
		private readonly stripeSubscriptionRepository: Repository<StripeSubscriptionEntity>,
		private readonly userPlanTransitionService: UserPlanTransitionService,
	) {}

	@OnEvent(UserSubscriptionChangedEvent.eventName)
	async onSubscriptionStatusChange(
		event: UserSubscriptionChangedEvent,
	): Promise<void> {
		const { subscription } = event.payload;

		const userId = subscription.userId;
		const oldStripePlanName = subscription.planName?.toLowerCase();

		let oldUserPlanName: UserPlanName;

		switch (oldStripePlanName) {
			case 'free':
				oldUserPlanName = UserPlanName.FREE;
				break;
			case 'plus':
				oldUserPlanName = UserPlanName.PLUS;
				break;
			case 'pro':
				oldUserPlanName = UserPlanName.PRO;
				break;
			default:
				this.logger.error(
					`Could not derive OLD UserPlanName from StripeSubscriptionEntity.planName: ${oldStripePlanName} for userId: ${userId}`,
					JSON.stringify(subscription),
				);
				captureException(
					new InternalServerErrorException(
						'Could not derive OLD UserPlanName from StripeSubscriptionEntity.planName',
					),
					{
						data: { subscription, userId },
					},
				);
				return;
		}

		let updatedSubscriptionEntity: StripeSubscriptionEntity | null;

		try {
			updatedSubscriptionEntity =
				await this.stripeSubscriptionRepository.findOneBy({
					id: subscription.id,
				});

			if (!updatedSubscriptionEntity) {
				this.logger.error(
					`Could not find updated StripeSubscriptionEntity with id: ${subscription.id} for userId: ${userId} after status change event.`,
				);
				captureException(
					new InternalServerErrorException(
						'Updated StripeSubscriptionEntity not found after status change event.',
					),
					{
						data: { subscriptionId: subscription.id, userId },
					},
				);
				return;
			}
		} catch (error) {
			this.logger.error(
				`Error fetching updated StripeSubscriptionEntity for id: ${subscription.id}, userId: ${userId}`,
				error,
			);
			captureException(error, {
				data: {
					subscriptionId: subscription.id,
					userId,
					context: 'Fetching updated subscription',
				},
			});
			return;
		}

		const newStripePlanName = updatedSubscriptionEntity.planName?.toLowerCase();
		let newUserPlanName: UserPlanName;

		switch (newStripePlanName) {
			case 'free':
				newUserPlanName = UserPlanName.FREE;
				break;
			case 'plus':
				newUserPlanName = UserPlanName.PLUS;
				break;
			case 'pro':
				newUserPlanName = UserPlanName.PRO;
				break;
			default:
				this.logger.error(
					`Could not derive NEW UserPlanName from updated StripeSubscriptionEntity.planName: ${newStripePlanName} for userId: ${userId}`,
					JSON.stringify(updatedSubscriptionEntity),
				);
				captureException(
					new InternalServerErrorException(
						'Could not derive NEW UserPlanName from updated StripeSubscriptionEntity.planName',
					),
					{
						data: { updatedSubscription: updatedSubscriptionEntity, userId },
					},
				);
				return;
		}

		if (newUserPlanName === oldUserPlanName) {
			this.logger.log(
				`User ${userId} plan unchanged (${newUserPlanName}), no quota update needed.`,
			);
			return;
		}

		this.logger.log(
			`User ${userId} plan changed from ${oldUserPlanName} to ${newUserPlanName}. Updating quotas.`,
		);

		try {
			await this.userPlanTransitionService.transitionUserPlan(
				userId,
				newUserPlanName,
				oldUserPlanName,
			);
			this.logger.log(
				`Successfully updated quotas for user ${userId} transitioning from ${oldUserPlanName} to ${newUserPlanName}.`,
			);
		} catch (error) {
			this.logger.error(
				`Error updating quotas for user ${userId} transitioning from ${oldUserPlanName} to ${newUserPlanName}`,
				error,
			);
			captureException(error, {
				data: {
					userId,
					newUserPlanName,
					oldUserPlanName,
					context: 'Transitioning user quotas',
				},
			});
		}
	}
}
