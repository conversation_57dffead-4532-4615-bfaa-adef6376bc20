import { Controller, Get, InternalServerErrorException, Logger } from '@nestjs/common';
import { UserQuotaType } from '@major/shared/models';
import { type CurrentJwtUser, JwtUser } from '@major/web-app-backend/util-auth';
import { UserQuotaService } from '../services/user-quota.service';
import { instanceToPlain } from 'class-transformer';
import { InjectRepository } from '@nestjs/typeorm';
import { UserQuotaEntity } from 'libs/web-app-backend/user-quotas/src/lib/types/entities/user-quota.entity';
import { Repository } from 'typeorm';

@Controller('users/quota')
export class UserQuotaController {
	private logger = new Logger(UserQuotaController.name);

	constructor(
		@InjectRepository(UserQuotaEntity)
		private readonly quotaRepository: Repository<UserQuotaEntity>,
		private readonly quotaService: UserQuotaService,
	) {}

	@Get()
	async getAllForUser(@JwtUser() user: CurrentJwtUser) {
		const docs = await this.quotaRepository.findBy({ userId: user.id });

		return instanceToPlain(docs, {
      excludeExtraneousValues: true
    });
	}

	/**
	 * Get the current quota status for the authenticated user
	 */
	@Get('import')
	async getImportQuotaStatus(@JwtUser() user: CurrentJwtUser) {
		try {
			const quota = await this.quotaService.getQuotaStatus(
				user.id,
				UserQuotaType.IMPORTS,
			);
			return instanceToPlain(quota);
		} catch (e) {
			this.logger.debug(
				`User ${user.id} quota not set, calling initializeUserQuota()`,
			);
			// user not provisioned with initial quota
			const quota = await this.quotaService.initializeUserQuotas(user.id);
			return instanceToPlain(quota);
		}
	}

	/**
	 * Get the current storage quota status for the authenticated user
	 */
	@Get('storage')
	async getStorageQuotaStatus(@JwtUser() user: CurrentJwtUser) {
		try {
			const quota = await this.quotaService.getQuotaStatus(
				user.id,
				UserQuotaType.STORAGE,
			);

			return instanceToPlain(quota);
		} catch (e) {
			this.logger.debug(
				`User ${user.id} quota not set, calling initializeUserQuota()`,
			);
			// user not provisioned with initial quota
			const quotas = await this.quotaService.initializeUserQuotas(user.id);
			return instanceToPlain(
				quotas.find((q) => q.type === UserQuotaType.STORAGE),
			);
		}
	}

	/**
	 * Get the current storage quota status for the authenticated user
	 */
	@Get('suggestions')
	async getAssistantQuotaStatus(@JwtUser() user: CurrentJwtUser) {
		try {
			const quota = await this.quotaService.getQuotaStatus(
				user.id,
				UserQuotaType.ASSISTANT_SUGGESTIONS,
			);

			return instanceToPlain(quota);
		} catch (e) {
			this.logger.debug(
				`User ${user.id} "suggestions" quota not set, calling initializeUserQuota()`,
			);
      throw new InternalServerErrorException('Quota not found');
			// user not provisioned with initial quota
			// const quotas = await this.quotaService.initializeUserQuotas(user.id);
			// return instanceToPlain(
			// 	quotas.find((q) => q.type === UserQuotaType.ASSISTANT_SUGGESTIONS),
			// );
		}
	}
}
