import { Injectable, Logger } from '@nestjs/common';
import { PostHogNodeService } from 'libs/web-app-backend/util-posthog-node/src/lib/services/posthog-node.service';
import { OnEvent } from '@nestjs/event-emitter';
import { LogtoWebhookEvent } from '@major/web-app-backend/webhooks';
import type { PostSignInPayload } from 'libs/web-app-backend/users/src/lib/handlers/logto-post-sign-in-event-handler.service';
import { WebAppBackendEvents } from 'web-app-backend/types';
import type { LlmEventData } from 'libs/web-app-backend/assistant/src/lib/types/interfaces/llm-event-data.interface';
import { UserCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-created.event';

@Injectable()
export class PosthogListener {
	private logger = new Logger(PosthogListener.name);

	constructor(private postHogService: PostHogNodeService) {}

	@OnEvent(UserCreatedEvent.eventName)
	async onUserCreated(event: UserCreatedEvent) {
		const { user, source } = event.payload;

		const context = source === 'api' ? 'api_signup' : 'self_signup';

		this.postHogService.captureEvent(user.userId, 'user signed up', {
			context,
		});
	}
	/**
	 * Capture a PostHog event for user sign in
	 *
	 * @param payload {PostSignInPayload}
	 */
	@OnEvent(LogtoWebhookEvent.PostSignIn)
	async onPostSignIn(payload: PostSignInPayload) {
		const userExternalId = payload.user.customData.externalId;

		this.postHogService.captureEvent(userExternalId, 'user signed in', {
			email: payload.user.primaryEmail,
		});

		this.logger.debug('Sent "user signed in" event to PostHog');
	}

	/**
	 * Capture a PostHog event for LLM response
	 * TODO migrate to new event model
	 *
	 * @param payload {LlmEventData}
	 */
	@OnEvent(WebAppBackendEvents.LLM.RESPONSE)
	async onPostResponse(payload: LlmEventData) {
		this.postHogService.captureLlmGenerationEvent(payload);
	}
}
