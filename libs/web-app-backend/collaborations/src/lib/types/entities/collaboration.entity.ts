import {
	Column,
	CreateDateColumn,
	Entity,
	Index,
	JoinColumn,
	ManyToOne,
	OneToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { CollaborationPermissionLevel } from '../enum/permission-level.enum';
import { CollaborationStatus } from '@major/shared/models';
import { Exclude, Expose, Transform } from 'class-transformer';
import { collaboratorName } from '../../util/collaborator-name';
import { collaborationIsOwner } from '../../util/collaboration-is-owner';
import { collaboratorEmail } from '../../util/collaborator-email';
import { collaborationIsCurrentUser } from '../../util/collaboration-is-current-user';
import { CurrentJwtUser } from '@major/web-app-backend/util-auth';
import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { columnTypeTimestamp } from '@major/web-app-backend/util-typeorm';
import { collaboratorPhotoUrl } from '../../util/collaborator-photo-url';
import { collaboratorDisplayName } from '../../util/collaborator-display-name';

@Entity('collaboration')
// Add indexes for common queries
@Index(['songId', 'inviteeEmail'])
@Index(['songId', 'inviteeId', 'status'])
@Index(['inviteeEmail', 'status'])
@Index(['inviteeId', 'status'])
@Index(['songId', 'status']) // Added for query GET /users/collaborators and /songs
export class CollaborationEntity {
	@Expose()
	@PrimaryGeneratedColumn()
	id!: number;

	@Expose({ groups: ['list', 'detail'] })
	@OneToOne(
		() => InviteSongEntity,
		(invite) => invite.collaboration,
		{ nullable: true },
	)
	@JoinColumn({ name: 'inviteId' })
	invite!: InviteSongEntity;

	/**
	 * Set this property in the controller with the value supplied
	 * by JwtUser() decorator
	 */
	@Exclude()
	currentUser!: CurrentJwtUser;

	@Expose({ groups: ['detail' + ''] })
	@Transform(({ obj }) => collaborationIsOwner(obj))
	isOwner?: boolean;

	@Expose({ groups: ['list', 'detail'] })
	@Transform(({ obj }) => collaborationIsCurrentUser(obj))
	isCurrentUser?: boolean;

	@Expose()
	@Transform(({ obj }) => collaboratorName(obj))
	name!: string;

	@Expose()
	@Transform(({ obj }) => collaboratorEmail(obj))
	email!: string;

	@Expose()
	@Transform(({ obj }) => collaboratorPhotoUrl(obj))
	photoUrl!: string | null;

	@Expose()
	@Transform(({ obj }) => collaboratorDisplayName(obj))
	displayName!: string | null;

	/**
	 * @deprecated
	 */
	@Expose({ groups: ['detail'] })
	@Column({ type: 'varchar', length: 64, nullable: true })
	inviteeName!: string | null; // For inviting non-registered users

	/**
	 * @deprecated
	 */
	@Expose({ groups: ['detail'] })
	@Column({ type: 'varchar', length: 255, nullable: true })
	@Index()
	inviteeEmail!: string | null; // For inviting non-registered users

	@Expose({ groups: ['detail', 'created', 'list'] })
	@Index()
	@Column({
		type: 'simple-enum',
		enum: CollaborationStatus,
		default: CollaborationStatus.Pending,
	})
	status!: CollaborationStatus;

	@Expose({ groups: ['detail'] })
	@Column({ type: columnTypeTimestamp(), nullable: true })
	removedAt!: Date | null;

	@Expose({ groups: ['detail'] })
	@Column({ type: 'text', nullable: true })
	removalNote!: string | null;

	@Expose({ groups: ['detail'] })
	@Column({ type: 'boolean', default: true })
	retainContent!: boolean;

	@Exclude()
	@Column({
		type: 'simple-enum',
		enum: CollaborationPermissionLevel,
		default: CollaborationPermissionLevel.ReadOnly,
	})
	permissionLevel!: CollaborationPermissionLevel;

	@Exclude()
	@Column({ nullable: false, type: 'int' })
	songId!: number;

	@Exclude()
	@Column({ nullable: true, type: 'int' })
	inviteeId!: number;

	@Exclude()
	@Column({ nullable: true, type: 'int' })
	inviterId!: number;

	@Expose({ groups: ['list', 'detail'] })
	@ManyToOne(
		() => SongEntity,
		(document) => document.collaborations,
		{
			onDelete: 'CASCADE',
		},
	)
	@JoinColumn({ name: 'songId' })
	song!: SongEntity;

	@Expose({ groups: ['list', 'detail'] })
	@ManyToOne(
		() => UserEntity,
		(user) => user.invitationsSent,
	)
	@JoinColumn({ name: 'inviterId' })
	inviter!: UserEntity;

	@Expose({ groups: ['list', 'detail'] })
	@ManyToOne(
		() => UserEntity,
		(user) => user.invitationsReceived,
		{ nullable: true },
	)
	@JoinColumn({ name: 'inviteeId' })
	invitee!: UserEntity;

	@Expose({ groups: ['list', 'detail'] })
	@CreateDateColumn()
	createdAt!: Date;
}
