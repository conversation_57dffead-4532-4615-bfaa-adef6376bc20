# Major

A monorepo project built with [Nx](https://nx.dev).

## 📚 Table of Contents

- [Development Setup](#development-setup)
  - [Prerequisites](#prerequisites)
  - [Local Environment](#local-environment)
  - [SSL Certificates](#ssl-certificates)
- [Architecture](#architecture)
  - [Tech Stack](#tech-stack)
  - [Applications](#applications)
- [Development](#development)
  - [Local Development](#local-development)
  - [Building](#building)
  - [Testing](#testing)
  - [Code Quality](#code-quality)
  - [Database](#database)
  - [Email Development](#email-development)
- [Rspack Configuration](#rspack-configuration)
  - [TypeORM Entity Handling](#typeorm-entity-handling)
  - [Minimal Package.json Generation](#minimal-packagejson-generation)
  - [Docker Build Process](#docker-build-process)
- [Deployment](#deployment)
  - [Docker](#docker)
    - [Building Docker Images](#building-docker-images)
    - [Automated Deployment](#automated-deployment)
    - [Environment Variables and Docker Secrets Management](#environment-variables-and-docker-secrets-management)
- [Release Process](#release-process)
  - [Version Management](#version-management)
  - [Creating a Release](#creating-a-release)
  - [Changelog Curation](#changelog-curation)
  - [Release Automation](#release-automation)
- [Infrastructure](#infrastructure)
  - [Server Configuration](#server-configuration)
  - [Docker Swarm Labels](#docker-swarm-labels)
  - [Service Placement](#service-placement)
- [Additional Resources](#additional-resources)
  - [Node on Rosetta (M1/M2 Macs)](#node-on-rosetta-m1m2-macs)

---

## Quick reference

### Generate a beta invite link

CLI tool to generate beta signup URL which will prefill the form. Payload is base64 encoded.

### Beta code notes:

* Beta codes are not restricted by email address
* Beta codes can only be used once

### Examples

```bash
  # production (will generate new code to insert into beta_codes table)
  bun run generate:invite -e <EMAIL_ADDRESS>
  
  # with existing code
  bun run generate:invite -c <CODE> -e <EMAIL_ADDRESS>

  # dev (code must exist in db!)
  bun run generate:invite -e <EMAIL_ADDRESS>  -b https://web.dev.major.app/signup/beta
```

---

## Development Setup

### Prerequisites

```bash
# Install required global packages
bun install -g @nrwl/nx

# Install Docker Desktop
# https://www.docker.com/products/docker-desktop
```

### Local Environment

1. Install dependencies:

```bash
bun install
```

2. Add the following to `/etc/hosts`:

```
127.0.0.1       dev.major.app
127.0.0.1       web.dev.major.app
127.0.0.1       api.dev.major.app
127.0.0.1       admin.dev.major.app
127.0.0.1       auth.dev.major.app
127.0.0.1       logto.dev.major.app
127.0.0.1       peerjs.dev.major.app
```

### SSL Certificates

The project uses Traefik as a reverse proxy for local development with SSL support:

1. Place your SSL certificates in `tools/certs/`:

   - `dev.major.app.pem` (certificate)
   - `dev.major.app-key.pem` (private key)

2. Start the development environment:

```bash
# bring up docker-compose.dev.yml using docker compose command in watch mode
docker context use default && docker compose -f docker-compose.dev.yml watch
```

Traefik will automatically handle SSL termination for all services using these certificates.

> **Note:** Access Traefik dashboard at http://localhost:8081 to monitor routing and services

---

## Architecture

### Tech Stack

- **Framework**: [Angular](http://angular.io)
- **Build System**: [Nx](http://nx.dev)
- **Styling**: [Tailwind CSS](http://tailwindcss.com)
- **Testing**:
  - [Cypress](http://cypress.io)
  - [Jest](http://jestj.io)
- **Infrastructure**:
  - Docker & Docker Compose
  - Traefik (reverse proxy)
  - PostgreSQL
  - Redis
  - Logto (authentication)
  - PeerJS (WebRTC)
  - Ngrok (webhook forwarding)
  - Cloudflare R2 (object storage)
    - S3-compatible API
    - Presigned URLs for secure file uploads/downloads
    - Organized storage by user and file category
    - File metadata management
  - CloudConvert
    - File conversion service for imports
    - Converts various formats to text:
      - Documents: PDF, Word (doc/docx), Pages, HTML
    - Uses job-based processing with upload, convert, and export tasks
    - Webhook integration for conversion status updates

### Applications

| App                   | Local URL                    | Internal Port | Description                                       |
| --------------------- | ---------------------------- | ------------- | ------------------------------------------------- |
| `web-app`             | https://web.dev.major.app    | 4200          | Main Angular application                          |
| `web-app-backend`     | https://api.dev.major.app    | 3000          | NestJS backend API                                |
| `marketing`           | https://dev.major.app        | 4200          | Marketing website                                 |
| `admin-app`           | https://admin.dev.major.app  | 4200          | Admin dashboard                                   |
| `logto`               | https://auth.dev.major.app   | 3001          | Authentication service                            |
| `logto-admin`         | https://logto.dev.major.app  | 3002          | Logto admin panel                                 |
| `peerjs`              | https://peerjs.dev.major.app | 9000          | WebRTC server                                     |
| `email-editor`        | -                            | -             | HTML email editor ([Unlayer](http://unlayer.com)) |
| `email-editor-server` | -                            | -             | Email editor API service                          |

> **Note:** Traefik handles port mapping, so you don't need to include ports in the URLs when accessing services

---

## Development

### Local Development

All services run in Docker containers orchestrated by Docker Compose:

```bash
# Start the development environment with file watching
docker compose -f docker-compose.dev.yml watch

# Or start without file watching
docker compose -f docker-compose.dev.yml up
```

This will start all services including:

- Angular applications (web-app, marketing, admin)
- Backend API
- PostgreSQL database
- Redis
- Logto authentication
- PeerJS server
- Ngrok for webhook forwarding
- Traefik reverse proxy

### Building

```bash
# Build all apps for production
bun run build:apps

# Individual production builds
bun run build:web-app:prod
bun run build:marketing-app:prod
bun run build:web-app-backend:prod

# Staging builds
bun run build:web-app:staging
```

### Testing

```bash
# Run all unit tests
bun run test

# Run specific app tests
bun run test:web-app
bun run test:web-app-backend

# Run e2e tests for affected apps
bun nx affected:e2e
```

### Code Quality

```bash
# Run linter
bun run lint

# Format code
bun run format:write

# Check formatting
bun run format:check

# Generate dependency graph
bun run dep-graph
```

### Database

Database migrations are managed through TypeORM:

```bash
# Generate a new migration
bun run migrate:generate [migration-name]

# Run TypeORM commands
bun run typeorm [command]
```

### Email Development

#### Service Providers

| Provider          | Purpose                        |
|-------------------| ------------------------------ |
| Mailersend (SMTP) | Transactional emails           |

#### Email Template Editor

The email editor is part of the Docker Compose setup and will be available when the development environment is running.

---

## Rspack Configuration

The web-app-backend uses Rspack for bundling instead of webpack. While Rspack is webpack-compatible, several workarounds were needed to handle NestJS and TypeORM correctly.

### Minimal Package.json Generation

Since Rspack doesn't support webpack's `generatePackageJson` option, we created a custom script to generate minimal package.json files for Docker builds of `web-app-backend` and `migrations-runner`.

**Script: `tools/generate-package-json.ts`**

The script:
1. Scans the built bundle for `require()` and `import` statements
2. Extracts external dependencies that are actually used
3. Adds essential runtime dependencies that might be bundled but still needed
4. Generates a minimal package.json with only required dependencies
5. Handles hashed output filenames (e.g., `main.f5ecc2069dc844ee.js`)

**Key Features:**
- Automatic main file detection with pattern matching `main.*.js`
- Essential dependencies list for runtime requirements:
  ```typescript
  const essentialDependencies = [
    'date-fns',
    'reflect-metadata',
    'tslib',
    '@nestjs/platform-express',
    '@nestjs/microservices',
    '@nestjs/platform-ws',
    '@nestjs/testing',
    'express',
    'cors',
    'helmet',
  ];
  ```
- Generates `start` script pointing to the correct hashed main file

**Usage:**
```bash
# After building the application
bun nx build web-app-backend --configuration=production

# Generate minimal package.json for web-app-backend app
bun tools/generate-package-json.ts web-app-backend

# Or, generate for migrations-runner app
bun tools/generate-package-json.ts migrations-runner
```

### Docker Build Process

The Docker build process was updated to work with hashed output filenames and minimal package.json generation.

**Updated Dockerfile (`apps/web-app-backend/Dockerfile`):**
```dockerfile
# Use the start script instead of hardcoded main.js
CMD ["dumb-init", "bun", "run", "start"]
```

**Build Process:**
1. Build the application with Rspack: `bun nx build web-app-backend --configuration=production`
2. Generate minimal package.json: `bun tools/generate-package-json.ts`
3. Build Docker image: `docker build -f ./apps/web-app-backend/Dockerfile -t major-backend .`

**Key Improvements:**
- Handles hashed output filenames automatically
- Minimal package.json reduces Docker image size
- Proper dependency resolution for runtime requirements
- Preserves TypeORM entity metadata and NestJS decorators

---

## Deployment

### Deploy `web-app-backend` to prod

    # compile the app
    bun build:web-app-backend:prod

    # build images, deploy `web-app-backend` and `migrations-runner` to prod 
    bun web-app-backend:build-push-deploy:prod 

### Deploy `web-app` to prod

    # compile the app
    bun build:web-app:prod

    # build images, deploy `web-app` to prod 
    bun web-app:build-push-deploy:prod

---

### Environment Variables and Docker Secrets Management

The project uses a structured approach to managing environment variables across development and production environments:

1. **Local Development**: Variables stored in `.env` files (`.env.dev`, `.env.prod`, `.env.staging`)
2. **Production Deployment**: Variables converted to Docker secrets for secure production use

#### Service-Specific Naming Patterns

The project enforces clear separation between services by using distinct naming patterns:

- **Web App Backend**: Uses `DATABASE_*` environment variables that map to `database_*` Docker secrets
- **Logto Service**: Uses `DB_*` environment variables that map to `db_*` Docker secrets
- **Postgres Service**: Uses `POSTGRES_*` environment variables that map to `postgres_*` Docker secrets

Where services share the same underlying database, corresponding variables in `.env.prod` will have the same values but maintain their distinct naming patterns.

#### Environment Variable Workflow

1. **Define Variables**: Add environment variables to the appropriate `.env` file, following the service-specific naming patterns
2. **Reference in Stack**: Add references to these variables in the appropriate stack file (e.g., `major-stack.prod.yml`)
3. **Validate Configuration**: Run the validation tool to ensure all variables are properly referenced
4. **Create/Update Secrets**: Execute the secret creation command to sync `.env` values to Docker secrets

```bash
# Validate environment variables in stack file
bun run docker:validate-env:prod           # Validate that env vars are properly referenced in stack file

# Create or update Docker secrets from .env values
bun run docker:create-secrets:prod         # Create/update secrets from .env.prod
bun run docker:validate-env:staging        # Validate for staging environment
bun run docker:create-secrets:staging      # Create/update secrets for staging
```

The tool output provides a clear view of all secrets, their usage in services, and validation status:

```
Secret Validation Results:

📋 Status of All Secrets:
 ✅ db_password: Correctly configured, used by: web-app-backend, db, logto
 ✅ redis_password: Correctly configured, used by: web-app-backend, redis
 ✅ cloudflare_r2_secret_key: Correctly configured, used by: web-app-backend

📊 Summary:
 Total secrets analyzed: 47
 All required secrets are defined ✓
 Unused secrets: 0

🏁 Validation Result:
 ✅ PASSED: All required secrets are correctly configured.
```

> **Note:** Always run validation before deploying to ensure all required environment variables are properly configured in your Docker stack file and that secrets exist for each referenced variable.

#### Adding New Environment Variables

When adding a new environment variable to your project:

1. Add the variable and its value to the appropriate `.env` file, following the correct service-specific naming pattern:

  - For web-app-backend: `DATABASE_*` for database variables
  - For logto service: `DB_*` for database variables
  - For postgres service: `POSTGRES_*` for database configuration
  - For other services: Follow existing patterns

2. Reference the variable in your stack file as a Docker secret (using lowercase name):

   ```yaml
   # Define the secret
   secrets:
     your_variable_name: # Lowercase version of the environment variable
       external: true

   # Reference the secret in your service
   services:
     web-app-backend:
       secrets:
         - your_variable_name
   ```

3. Run the validation and secret creation tools to deploy the new variable

> **Important:** Variables defined in `.env` files but not referenced in the stack file will NOT be converted to Docker secrets automatically. This is by design to ensure only required variables are deployed.

#### Automatic Environment Variable Mapping

The project uses a custom entrypoint script that automatically maps Docker secrets to environment variables:

1. Docker secrets are mounted at `/run/secrets/secret_name`
2. The entrypoint script reads each secret file and sets a corresponding environment variable
3. Variable names are converted to UPPERCASE (e.g., `database_password` secret becomes `DATABASE_PASSWORD` environment variable in the web-app-backend container)

This approach provides several benefits:

- Simplified stack configuration (no need for `_FILE` environment variables)
- Applications can use standard environment variables without code changes

---

## Release Process

### Version Management

The project follows [Semantic Versioning](https://semver.org/) for all releases:

- **Major version (X.0.0)**: Breaking changes and major feature releases
- **Minor version (0.X.0)**: New features with backward compatibility
- **Patch version (0.0.X)**: Bug fixes and minor improvements

### Creating a Release

A release script automates the process of creating new releases:

```bash
# Create a release based on conventional commits
bun run release

# Force a specific version type
bun run release -- --major
bun run release -- --minor
bun run release -- --patch

# Dry run (no changes)
bun run release -- --dry-run
```

### Changelog Curation

The release process includes an AI-powered changelog curation step:

1. **Standard CHANGELOG.md**: Automatically generated from commit history
2. **CHANGELOG-CURATED.md**: A user-friendly version created using Google Gemini AI
   - Transforms technical commit messages into clear release notes
   - Organizes changes into categories for better readability
   - Requires `GOOGLE_GEMINI_API_KEY` in your .env file

### Release Automation

The release process is orchestrated by [release-it](https://github.com/release-it/release-it) and handles:

1. **Version Bumping**: Updates version in package.json and other files
2. **Changelog Generation**: Creates/updates CHANGELOG.md based on commits
3. **Changelog Curation**: Generates user-friendly CHANGELOG-CURATED.md
4. **File Updates**: Updates version.ts, changelog.json, deployment-info.json
5. **Git Operations**: Creates commits, tags, and pushes changes
6. **GitLab Release**: Creates a GitLab release with release notes

### Release Frequency

- Create releases when significant features or fixes are completed
- Coordinate releases with deployments for predictable rollout

---

## Infrastructure

### Server Configuration

> **Note:** VPS2 currently runs Dokploy and is not part of swarm with VPS1 and VPS3

| Server | Purpose              | IP Address     | OS           | Hostname              | Resources       | Storage | Notes                                                            |
| ------ | -------------------- | -------------- | ------------ | --------------------- | --------------- | ------- | ---------------------------------------------------------------- |
| VPS1   | Docker Swarm Worker  | ************** | Debian 12    | vps-d73ef5ff          | 4 CPU, 8GB RAM  | 160GB   | Primary container orchestration                                  |
| VPS2   | Dokploy              | *************  | Ubuntu 20.04 | vps-bbeab5ea          | 1 CPU, 2GB RAM  | 20GB    | Dokploy URL: http://*************:3000 / https://dokploy.jng.dev |
| VPS3   | Docker Swarm Manager | ************   | Ubuntu 24.04 | srv651406.hstgr.cloud | 4 CPU, 16GB RAM | 200GB   | Hostinger hosted, 16TB bandwidth                                 |

### Docker Swarm Labels

| Server | Label           |
| ------ | --------------- |
| VPS1   | standard-memory |
| VPS2   | -               |
| VPS3   | high-memory     |

### Service Placement

Based on constraints in `major-stack.prod.yml`, services are distributed across servers as follows:

| Service         | Server | Constraint                              | Replicas | Notes                                      |
| --------------- | ------ | --------------------------------------- | -------- | ------------------------------------------ |
| web-app         | VPS1   | `node.labels.server == standard-memory` | 2        | Frontend application with load balancing   |
| web-app-backend | VPS3   | `node.labels.server == high-memory`     | 2        | API services with max 1 replica per node   |
| redis           | VPS3   | `node.labels.server == high-memory`     | 1        | In-memory data store (1GB memory limit)    |
| db              | VPS3   | `node.labels.server == high-memory`     | 1        | PostgreSQL database (6GB memory limit)     |
| logto           | VPS3   | `node.labels.server == high-memory`     | 1        | Authentication service                     |
| peerjs          | VPS1   | `node.labels.server == standard-memory` | 1        | WebRTC server for peer-to-peer connections |

> **Note:** VPS2 is not part of the Docker Swarm and runs Dokploy separately.

---

## Additional Resources

- **Analytics Events**: [Google Sheet](https://docs.google.com/spreadsheets/d/1XiYyWJFLJJVSQG7Tn-74sjzlOPdcSRn5FE_PvYmZA6c/edit#gid=0)
- **reCAPTCHA Admin**: [Console](https://www.google.com/u/4/recaptcha/admin/site/430477613) (<NAME_EMAIL>)
- **Dependencies Graph**: Run `nx dep-graph`
- **Nx Cloud Token**: Stored in 1Password

### Node on Rosetta (M1/M2 Macs)

```bash
# Use Intel architecture
env /usr/bin/arch -x86_64 /bin/zsh --login

# Check current architecture
node -p 'process.arch'

# Install Node with x86 if needed
arch -x86_64 nvm install 20.13.1
```
---

## Troubleshooting

### Finding circular imports

Find all (there's a lot):

    bunx madge -c --ts-config tsconfig.base.json --extensions ts .

It's good to narrow the scope:

    bunx madge -c --ts-config tsconfig.base.json --extensions ts .libs/path/to/lib
