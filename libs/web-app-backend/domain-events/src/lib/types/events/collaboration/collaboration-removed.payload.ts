import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';

export interface CollaborationRemovedPayload {
	songId: number;
	collaboration: CollaborationEntity & {
		invitee: UserEntity;
		inviter: UserEntity;
		song: SongEntity;
	};
	actionUserId: number;
	timestamp: number;
}
