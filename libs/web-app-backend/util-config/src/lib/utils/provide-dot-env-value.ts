import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { repeat } from 'lodash';

export const provideDotEnvValue = (symbolName: symbol, envVarName: string) => ({
	provide: symbolName,
	useFactory: (configService: ConfigService) => {
		const value = configService.getOrThrow(envVarName);
		let valueToLog = value;

		if (
			envVarName.toLowerCase().indexOf('password') !== -1 ||
			envVarName.toLowerCase().indexOf('secret') !== -1 ||
			envVarName.toLowerCase().indexOf('token') !== -1 ||
			envVarName.toLowerCase().indexOf('key') !== -1
		) {
			valueToLog = repeat('*', Math.min(value.length, 8));
		}

		const logger = new Logger('provideDotEnvValue()');
		logger.debug(`${envVarName}=${valueToLog}`);

		return value;
	},
	inject: [ConfigService],
});
