import { CreateNaturalSortingAndSearchIndexes1689712345678 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1689712345678-song-indexes';
import { SetSearchPath1714855552000 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1714855552000-set-search-path';
import type { MixedList } from 'typeorm';
import { AddSuggestionsQuota1747800773660 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1747800773660-add-suggestions-quota';
import { Entities1689712345677 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1689712345677-entities';
import { CorrectUUIDColumnTypes1748725057429 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1748725057429-correct-uuid-column-types';
import { BetaCodes1748957706653 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1748957706653-beta-codes';
import { DropUniqueUserDisplayNameIndex1748985813503 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1748985813503-drop-unique-display-name';
import { ActivityEntity1749557149633 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1749557149633-activity-entity';
import { SongCreatedWithInvitesActivityType1750283104894 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1750283104894-song-created-with-invites-activity-type';
import { ActivityTypeEnumUpdate1750779372859 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1750779372859-activity-type-enum-update';
import { ActivityTypes1750853721505 } from './1750853721505-activity-types';
import { UserLoggedInAt1750877153827 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1750877153827-user-logged-in-at';
import { PreviouslyLoggedInAt1750878871962 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1750878871962-previously-logged-in-at';
import { InviteCancelledActivityEnum1750879745085 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1750879745085-invite-cancelled-activity-enum';
import { ActivityTypeFileCreated1751376360951 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1751376360951-activity-type-file-created';
import { InviteeIdNullable1751904138731 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1751904138731-invitee-id-nullable';
import { ActivityEnumSongCopySent1751966124472 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1751966124472-activity-enum-song-copy-sent';
import { SongTitleEntity1753361702071 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1753361702071-changelist';
import { SongTitleCount1753432294841 } from './1753432294841-song-title-count';
import { TrashedArchiveSong1754472592164 } from 'libs/web-app-backend/database-migrations/src/lib/changelist/1754472592164-trashed-archive-song';

// biome-ignore lint/complexity/noBannedTypes: <explanation>
export const migrations: MixedList<Function | string> = [
	Entities1689712345677,
	CreateNaturalSortingAndSearchIndexes1689712345678,
	SetSearchPath1714855552000,
	AddSuggestionsQuota1747800773660,
	CorrectUUIDColumnTypes1748725057429,
	BetaCodes1748957706653,
	DropUniqueUserDisplayNameIndex1748985813503,
	ActivityEntity1749557149633,
	SongCreatedWithInvitesActivityType1750283104894,
	ActivityTypeEnumUpdate1750779372859,
	ActivityTypes1750853721505,
	UserLoggedInAt1750877153827,
	PreviouslyLoggedInAt1750878871962,
	InviteCancelledActivityEnum1750879745085,
	ActivityTypeFileCreated1751376360951,
	InviteeIdNullable1751904138731,
	ActivityEnumSongCopySent1751966124472,
	SongTitleEntity1753361702071,
	SongTitleCount1753432294841,
	TrashedArchiveSong1754472592164,
];
