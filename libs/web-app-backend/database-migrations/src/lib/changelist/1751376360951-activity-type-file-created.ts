import { MigrationInterface, QueryRunner } from 'typeorm';

export class ActivityTypeFileCreated1751376360951
	implements MigrationInterface
{
	name = 'ActivityTypeFileCreated1751376360951';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TYPE "app"."activity_type_enum" RENAME TO "activity_type_enum_old"`,
		);
		await queryRunner.query(
			`CREATE TYPE "app"."activity_type_enum" AS ENUM('import.complete', 'song.created', 'song.created_with_invites', 'song.edited', 'song.renamed', 'song.archived', 'song.restored', 'song.lyrics.updated', 'song.recording.added', 'song.idea.created', 'invite.created', 'invite.accepted', 'invite.declined', 'invite.cancelled', 'file-meta-data.created', 'project.created', 'project.renamed', 'collaborator.invitation.sent', 'collaborator.left', 'collaborator.removed', 'collaborator.invited.to.entity', 'user.membership.plan.changed', 'user.profile.updated', 'release.created', 'release.scheduled', 'release.published')`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."activity" ALTER COLUMN "type" TYPE "app"."activity_type_enum" USING "type"::"text"::"app"."activity_type_enum"`,
		);
		await queryRunner.query(`DROP TYPE "app"."activity_type_enum_old"`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`CREATE TYPE "app"."activity_type_enum_old" AS ENUM('collaborator.invitation.sent', 'collaborator.invited.to.entity', 'collaborator.left', 'collaborator.removed', 'import.complete', 'invite.accepted', 'invite.cancelled', 'invite.created', 'invite.declined', 'project.created', 'project.renamed', 'release.created', 'release.published', 'release.scheduled', 'song.archived', 'song.created', 'song.created_with_invites', 'song.edited', 'song.idea.created', 'song.lyrics.updated', 'song.recording.added', 'song.renamed', 'song.restored', 'user.membership.plan.changed', 'user.profile.updated')`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."activity" ALTER COLUMN "type" TYPE "app"."activity_type_enum_old" USING "type"::"text"::"app"."activity_type_enum_old"`,
		);
		await queryRunner.query(`DROP TYPE "app"."activity_type_enum"`);
		await queryRunner.query(
			`ALTER TYPE "app"."activity_type_enum_old" RENAME TO "activity_type_enum"`,
		);
	}
}
