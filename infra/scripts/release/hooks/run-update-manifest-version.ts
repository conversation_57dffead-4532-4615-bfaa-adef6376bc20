import chalk from 'chalk';
import * as fs from 'node:fs';
import * as path from 'node:path';
import { getRootDir } from './get-root-dir';

/**
 * Hook invoked by release-it to update the PWA manifest version.
 *
 * @remarks
 * This hook is invoked by release-it after the version bump to sync
 * the manifest.webmanifest version field with the package.json version.
 */
export function runUpdateManifestVersion() {
	const rootDir = getRootDir();

	console.log(chalk.blue('Hook: Updating PWA manifest version...'));

	const packageJsonPath = path.join(rootDir, 'package.json');
	const manifestPath = path.join(
		rootDir,
		'apps/web-app/src/manifest.webmanifest',
	);

	try {
		// Read the current package.json version
		const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
		const newVersion = packageJson.version;

		if (!newVersion) {
			throw new Error('Could not find version in package.json');
		}

		// Read the manifest file
		const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));

		// Update the version
		const oldVersion = manifest.version || '(none)';
		manifest.version = newVersion;

		// Write the updated manifest back
		fs.writeFileSync(manifestPath, `${JSON.stringify(manifest, null, 2)}\n`);

		console.log(
			chalk.green(
				`✅ Hook: Manifest version updated from ${oldVersion} to ${newVersion}`,
			),
		);
	} catch (error) {
		console.error(chalk.red('❌ Hook: Failed to update manifest version:'));
		console.error(
			chalk.red(`   ${error instanceof Error ? error.message : error}`),
		);
		throw error; // Re-throw to halt release-it
	}
}
