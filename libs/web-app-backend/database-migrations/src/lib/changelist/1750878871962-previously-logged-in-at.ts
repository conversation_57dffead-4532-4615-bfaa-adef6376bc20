import { MigrationInterface, QueryRunner } from "typeorm";

export class PreviouslyLoggedInAt1750878871962 implements MigrationInterface {
    name = 'PreviouslyLoggedInAt1750878871962'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "app"."user" ADD "previouslyLoggedInAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "app"."user" ALTER COLUMN "loggedInAt" SET DEFAULT CURRENT_TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "app"."user" ALTER COLUMN "loggedInAt" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "app"."user" DROP COLUMN "previouslyLoggedInAt"`);
    }

}
