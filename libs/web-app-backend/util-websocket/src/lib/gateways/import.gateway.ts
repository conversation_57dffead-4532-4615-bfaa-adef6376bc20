import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import {
	MessageBody,
	SubscribeMessage,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { WebSocketEvent } from '@major/shared/util-websocket/types';
import { JwtWsUser } from 'libs/web-app-backend/util-websocket/src/lib/decorators/ws-user.decorator';
import type { CurrentWebSocketUser } from '@major/web-app-backend/util-websocket/types';
import { CloudConvertJobEntity } from 'web-app-backend/cloud-convert/entities';
import { getUserRoomName } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-user-room-name';
import { UserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/user-presence.service';
import { OnEvent } from '@nestjs/event-emitter';
import { CloudConvertJobUpdatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/import/cloud-convert-job-updated.event';
import { DomainEventEmitter } from 'domain-events';
import { ImportCompleteEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/import/import-complete.event';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class ImportGateway {
	private logger = new Logger(ImportGateway.name);

	@WebSocketServer()
	server!: Server;

	constructor(
		@InjectDataSource() private dataSource: DataSource,
		private userPresenceService: UserPresenceService,
		private domainEventEmitter: DomainEventEmitter,
	) {}

	@SubscribeMessage(WebSocketEvent.ImportListRequest)
	async handleImportListRequest(@JwtWsUser() user: CurrentWebSocketUser) {
		const imports = await this.dataSource
			.getRepository(CloudConvertJobEntity)
			.find({
				where: { userId: user.id },
				order: { createdAt: 'DESC' },
				withDeleted: false,
				take: 50,
			});

		this.server
			.to(getUserRoomName(user.id))
			.emit(WebSocketEvent.ImportListResponse, {
				data: imports,
			});

		await this.userPresenceService.updateLastSeen(user.id);
	}

	@SubscribeMessage(WebSocketEvent.ImportComplete)
	async onImportResult(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody() payload: {
			numberOfFilesImported: number;
		},
	) {
		this.domainEventEmitter.emitEvent(
			new ImportCompleteEvent({
				userId: user.id,
				numberOfFilesImported: payload.numberOfFilesImported,
			}),
		);
	}

	@OnEvent(CloudConvertJobUpdatedEvent.eventName)
	public async onJobUpdated(event: CloudConvertJobUpdatedEvent) {
		const { userId, job } = event.payload;

		this.server
			.to(getUserRoomName(userId))
			.emit(WebSocketEvent.ImportStatusResponse, {
				data: job,
			});
	}
}
