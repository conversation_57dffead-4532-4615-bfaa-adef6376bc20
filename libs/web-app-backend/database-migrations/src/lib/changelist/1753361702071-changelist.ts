import { MigrationInterface, QueryRunner } from 'typeorm';

export class SongTitleEntity1753361702071 implements MigrationInterface {
	name = 'SongTitleEntity1753361702071';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`CREATE TABLE "app"."song_title" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ownerId" integer NOT NULL, "title" character varying NOT NULL, "isStarred" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_d16c259f2db878b5b6029642df4" PRIMARY KEY ("id"))`,
		);
		await queryRunner.query(
			`CREATE INDEX "IDX_2ae00aa9e3b403bb51db4ea74d" ON "app"."song_title" ("ownerId") `,
		);
		await queryRunner.query(
			`CREATE INDEX "IDX_b8ace539e8fedd69acc1502125" ON "app"."song_title" ("updatedAt") `,
		);
		await queryRunner.query(
			`CREATE INDEX "IDX_10ff3b01029d88a05b0824112c" ON "app"."song_title" ("deletedAt") `,
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`DROP INDEX "app"."IDX_10ff3b01029d88a05b0824112c"`,
		);
		await queryRunner.query(
			`DROP INDEX "app"."IDX_b8ace539e8fedd69acc1502125"`,
		);
		await queryRunner.query(
			`DROP INDEX "app"."IDX_2ae00aa9e3b403bb51db4ea74d"`,
		);
		await queryRunner.query(`DROP TABLE "app"."song_title"`);
	}
}
