import { InviteEventHandlerBase } from './invite-event-handler.base';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { RefreshScope } from '@major/shared/models';
import { InviteCancelledEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-cancelled.event';

@Injectable()
export class InviteCancelledHandler extends InviteEventHandlerBase {
	/**
	 * @param event
	 */
	@OnEvent(InviteCancelledEvent.eventName)
	async onInviteCancelled(event: InviteCancelledEvent) {
		const { invite } = event.payload;

		// Only proceed with UI refresh if invitee is a registered user
		if (!invite.invitee) {
			this.logger.debug(
				`Invite cancelled for non-registered user, skipping UI refresh. Invite id=${invite.id}, email=${invite.inviteeEmail}`,
			);
			return;
		}

		await this.markInviteeNotificationAsRead(invite);

		const userId = invite.invitee.id;

		// invitee's U<PERSON> must refresh to remove the cancelled invite
		const refreshPayload1 = {
			scope: RefreshScope.NOTIFICATIONS,
			targetUserIds: [userId],
		};

		// invitee's UI must also refresh their notifications as
		// the original invitation notification must be removed
		const refreshPayload2 = {
			scope: RefreshScope.INVITES_RECEIVED,
			targetUserIds: [userId],
		};

		this.refreshMessageService.notifyRefreshNeeded(refreshPayload1);
		this.refreshMessageService.notifyRefreshNeeded(refreshPayload2);

		this.logger.debug(`Invite cancelled id=${invite.id}`);
	}
}
