# GEMINI.MD: AI Collaboration Guide

This document provides essential context for AI models interacting with this project. Adhering to these guidelines will ensure consistency and maintain code quality.

## 1. Project Overview & Purpose

* **Primary Goal:** This is a monorepo for "Major.app", a web application focused on music creation and collaboration. The project includes a main web application, a backend API, a marketing website, and an admin dashboard. Key features appear to involve song editing, audio file management, and collaborative workspaces.
* **Business Domain:** Music Technology, Creative Collaboration Tools.

## 2. Core Technologies & Stack

* **Languages:** TypeScript
* **Frameworks & Runtimes:**
    *   **Frontend:** Angular, NgRx (for state management)
    *   **Backend:** NestJS, Express.js
    *   **Build System:** Nx (monorepo management)
    *   **Runtime:** Node.js
* **Databases:**
    *   **Primary:** PostgreSQL (managed with TypeORM)
    *   **Caching:** Redis
    *   **Queues:** BullMQ
* **Key Libraries/Dependencies:**
    *   **Frontend:** RxJS, TailwindCSS, TipTap (for text editing), Wavesurfer.js (for audio visualization)
    *   **Backend:** TypeORM, Socket.io (for real-time communication), Stripe (for payments), Sentry (for error tracking), PostHog (for analytics)
    *   **Authentication:** Logto
* **Package Manager(s):** The project uses `bun` as the primary package manager, as indicated by the `bun.lock` file and scripts in `package.json`. `npm` is also used for specific scripts like `typeorm`.

## 3. Architectural Patterns

* **Overall Architecture:** The project is a **Monorepo** managed by Nx. It employs a decoupled architecture with distinct frontend and backend applications. The presence of services like Logto and PeerJS suggests a move towards a **Microservices** or service-oriented approach for specific functionalities.
* **Directory Structure Philosophy:** The structure is based on the Nx workspace philosophy:
    *   `/apps`: Contains the main, deployable applications (`web-app`, `web-app-backend`, `marketing-app`).
    *   `/libs`: Contains the majority of the code, organized into reusable libraries. This promotes code sharing and modularity. Libraries are categorized by scope (e.g., `shared`, `web-app`, `marketing`) and type (e.g., `feature`, `ui`, `data-access`, `util`).
    *   `/infra`: Contains Infrastructure as Code (IaC) scripts for deployment and management.
    *   `/tools`: Holds utility scripts for development, builds, and other automation.
    *   `/docs`: Contains project documentation.

## 4. Coding Conventions & Style Guide

* **Formatting:** Code formatting is enforced by **Prettier** and **Biome**. Configuration can be found in `.prettierrc`, `.prettierignore`, and `biome.json`.
* **Naming Conventions:**
    *   **Files:** kebab-case (e.g., `my-component.ts`).
    *   **TypeScript Libraries:** Scoped paths using `@major/` prefix (e.g., `@major/shared/ui-icon`), defined in `tsconfig.base.json`.
    *   **Components/Classes:** PascalCase (`MyComponent`).
    *   **Variables/Functions:** camelCase (`myVariable`).
* **API Design:** The backend API is built with NestJS, which favors a modular, controller-based approach, likely resulting in a RESTful or RPC-style API.
* **Error Handling:** The use of Sentry on both the frontend and backend indicates a structured approach to error tracking. In the code, expect to see standard `try...catch` blocks and NestJS's built-in exception filters.

## 5. Key Files & Entrypoints

* **Main Entrypoint(s):**
    *   **Web App (Angular):** `apps/web-app/src/main.ts`
    *   **Backend (NestJS):** `apps/web-app-backend/src/main.ts`
* **Configuration:**
    *   **Project Structure:** `nx.json`
    *   **TypeScript:** `tsconfig.base.json`
    *   **Environment:** `.env.*` files for different environments (development, staging, production).
    *   **Deployment:** `major-stack.prod.yml`, `docker-compose.dev.yml`
* **CI/CD Pipeline:** `.gitlab-ci.yml` indicates that GitLab CI/CD is used for automation.

## 6. Development & Testing Workflow

* **Local Development Environment:** The local setup is fully containerized using Docker. To run the development environment, use the command: `docker-compose -f docker-compose.dev.yml watch`. This starts all services, including the database, reverse proxy (Traefik), and applications with hot-reloading.
* **Testing:**
    *   **Frameworks:** Jest for unit tests, Playwright for end-to-end tests.
    *   **Run Tests:** `bun run test` to execute all unit tests across the monorepo.
    *   **Test-Specific Scripts:** `test:web-app` and `test:web-app-backend` are available for running tests on specific applications.
* **CI/CD Process:** The `.gitlab-ci.yml` file defines the pipeline, which likely includes steps for linting, testing, building Docker images, and deploying to various environments.

## 7. Specific Instructions for AI Collaboration

* **Contribution Guidelines:** While there is no formal `CONTRIBUTING.md`, the `README.md` provides extensive setup and workflow instructions. Follow the existing patterns and scripts for any new development.
* **Infrastructure (IaC):** The `/infra` directory contains scripts for managing Docker Swarm deployments. **Do not modify files in this directory without explicit instruction.** Changes can impact production infrastructure.
* **Security:** Be mindful of security. Do not hardcode secrets or keys. Environment variables are managed via `.env` files and converted to Docker Secrets for production. Follow the documented procedure in the `README.md` for adding new secrets.
* **Dependencies:** To add a new dependency, use `bun install <package-name>`. For adding a dev dependency, use `bun install -D <package-name>`.
* **Commit Messages:** The project uses **Conventional Commits**. A `commitlint.config.js` is present to enforce this. All commit messages should follow the format: `type(scope): subject` (e.g., `feat(web-app): add new component`).

## 8. Coding style

Please adhere to these prettier rules:

{
"singleQuote": true,
"useTabs": false,
"tabWidth": 2,
"trailingComma": "all",
"printWidth": 95
}

In addition and these are all IMPORTANT:

- Always use braces with control flow statements
- Control flow statements should have a blank line above and below
- Try to group related statements together (like variable assignment) with blank lines inbetween groups
- Assign the outcome of slightly longer expressions to variables to aid readability
- Create loosely coupled code which has the highest standard of design and testability, but don't over-engineer

## 9. Circular Dependency Issues

When there are circular dependency issues, refer to libs/web-app/editor/feature-song-editor/src/lib/web-app-editor-feature-song-editor.module.ts as this is importing some components using relative paths, not ts config paths. If these have been changed they may cause an issue. e.g. the import:
import { SongMetaComponent } from '../../../../songs/feature-detail/src/lib/components/song-meta/song-meta.component';
is fine, but the ts path import import { SongMetaComponent } from '@major/web-app/songs/feature-detail'; causes circular dependency issues. 
You should never perform refactors in order to correct circular dependency issues.

## 10. Testing

When writing unit tests please use the createTestApp() utility function to create a test app in a hook, probably beforeAll(). createTestApp() makes a realistic nest app instance. It runs testcontainers of postgres and redis via the global setup script, which closely mirrors production. Please also use createMock() as provided by @golevelup/ts-jest package for creating mocks. Then for each test you can just use jest.spyOn() to create the desired behaviour for the specific test.

### Naming tests

Please use the prefix 'it must' or 'it must not' to indicate whether the test should pass or fail. e.g. 'it must return the correct result' or 'it must not return the correct result'. For describe blocks please include parantheses to mark a function e.g. describe('functionName()'...)

## 11. Dependencies

Also you should never install new dependencies. My package.json is off-limits for changes UNLESS explicitally state otherwise.

## 12. Documentation

When modifying the code to a library, please update the README.md file in the same directory. README files should not be too long but should describe the library in a concise manner and how the library is used in the repo.


## 13. Project-specific rules that must be followed

* NotifyService is for showing toasts in the UI only (via the info() method)
* All shared interfaces/enums/etc. should go into the shared-models nx library. Other types should live in the library that consumes them in sub directories, e.g.: types/entities, types/enums etc
* In the UI, HTTP requests go in 'data' services, and angular-query queries and mutations
* must always live in a dedicated 'query' service that uses the appropriate 'data' service
* Propose changes to the UI (web-app) as well as the backend nestjs service (web-app-backend) when appropriate
* Angular component templates should always be in a separate HTML file named \*.component.html
* Angular Material is only used for dialog, icons and menus. Everything else is DaisyUI and Tailwind only.
* Favour signals over RxJs except for business side-effects
* Use firstValueFrom and lastValueFrom instead of toPromise
* Group related lines of code, separate by blank lines
* Add comments to explain what is happening in the code. Reference any related changes that were made.
* The UI has many common components created in libs/web-app/ui please check there and try use them where applicable
* You may not use any code inside the libs/web-app when making changes inside the libs/web-app-backend and vica versa (because UI and node code cannot be mixed) - shared types are fine, these are located in the libs/shared nx library
* NestJS should be idiomatic
* Angular code should be idiomatic
* Use lodash-es in the UI when it makes for simpler code
* Use lodash (not lodash-es) in the backend when it makes for simpler code
* The SongEntity on the backend mainly works with the primary key column (id) but when sending to the frontend the value of the externalId column is mapped to the 'id' property. Please keep this in mind. The UI only deals with song externalIds to prevent leaking primary keys. This is the same for UserEntity, except the property is named UserEntity.userId. When a payload is received from the UI, the value of 'id' parameters will for these entities be externalId strings. Controllers will often then resolve an entity from this. Then when calling a service with the entity, the primary key column will be used. So that means when you have an instance of an entity on the backend, you should never assume the 'id' field is an externalId string in these cases.
* For web-app features, I like to place data and query services in a dedicated "domain" nx lib, e.g. libs/web-app/feature-projects/src/lib/domain/data/project-data.service.ts - but I never create these libs without using the nx cli to create them
* Always use the JwtUser() inside controller methods that require the current user's id - never put a user id (external or primary key) into a URL
* Do not put lots of logic into Angular templates - when you need to check state in a specific way, favour computed signals instead
* Ensure Angular standalone components are correctly importing all directives, pipes, etc.
* when executing npm commands please use bun as your first choice and only use npm as a fallback. do not use yarn.
* when working on tooling and writing scripts, please favour typescript over bash.
* Create string templates when variable substitution is required, but string literals otherwise
* When import node packages always use the node: prefix e.g. "import * as path from 'node:path'"
