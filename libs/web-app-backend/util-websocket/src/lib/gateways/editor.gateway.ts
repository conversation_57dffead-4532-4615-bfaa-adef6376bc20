import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import {
	ConnectedSocket,
	MessageBody,
	SubscribeMessage,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';
import { Server, Socket } from 'socket.io';
import { WebSocketEvent } from '@major/shared/util-websocket/types';
import { JwtWsUser } from 'libs/web-app-backend/util-websocket/src/lib/decorators/ws-user.decorator';
import type { CurrentWebSocketUser } from '@major/web-app-backend/util-websocket/types';
import { WebSocketAck } from 'libs/shared/util-websocket/src/lib/types/interfaces/web-socket-ack.interface';
import {
	logColorPrimary,
	logColorSecondary,
	logColorSuccess,
} from 'libs/web-app-backend/util-logging/src/lib/utils/cli-colors/index';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { getUserRoomName } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-user-room-name';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { EditorUserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/editor-user-presence.service';
import type { CurrentJwtUser } from '@major/web-app-backend/util-auth';
import { omit } from 'lodash';
import { UserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/user-presence.service';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class EditorGateway {
	private logger = new Logger(EditorGateway.name);

	@WebSocketServer()
	server!: Server;

	constructor(
		@InjectDataSource() private dataSource: DataSource,
		private userPresenceService: UserPresenceService,
		private editorUserPresenceService: EditorUserPresenceService,
	) {}

	private async emitUserPresence(songExternalId: string) {
		const users =
			await this.editorUserPresenceService.getUsersIdsOnlineForSong(
				songExternalId,
			);

		this.server
			.to(`song:${songExternalId}`)
			.emit(WebSocketEvent.EditorPresence, { data: users });

		this.logger.debug(
			`${logColorPrimary(WebSocketEvent.EditorPresence)} users=${users}`,
		);
	}

	@SubscribeMessage(WebSocketEvent.EditorJoin)
	async editorJoin(
		@ConnectedSocket() client: Socket,
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody() payload: { songId: string },
	) {
		this.logger.debug(logColorPrimary('Editor join started'));

		try {
			client.join(`song:${payload.songId}`);

			// Notify other users in the room about the new user
			client
				.to(`song:${payload.songId}`)
				.emit(WebSocketEvent.EditorUserJoined, {
					data: { userId: user.userId },
				});

			// Update user presence
			await this.editorUserPresenceService.setUserPresent(
				payload.songId,
				user.userId,
			);

			// Emit presence to all users
			await this.emitUserPresence(payload.songId);

			this.logger.debug(
				`${logColorPrimary(WebSocketEvent.EditorJoin)} ${logColorSecondary(user.email)} songId=${
					payload.songId
				}`,
			);

			this.logger.debug(logColorSuccess('Editor join completed successfully'));

			return {
				status: 'success',
				message: `Joined session ${payload.songId}`,
			};
		} catch (error: any) {
			this.logger.error(`Error in editorJoin: ${error}`);

			return {
				status: 'error',
				message: `Failed to join session: ${error.message || 'Unknown error'}`,
			};
		}
	}

	@SubscribeMessage(WebSocketEvent.EditorHeartbeat)
	async editorHeartbeat(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { songId: string },
		@ConnectedSocket() client: Socket,
	) {
		// First, update song-specific presence
		try {
			await this.editorUserPresenceService.setUserPresent(
				payload.songId,
				user.userId,
			);
			// this.logger.debug(
			// 	`Updated editor presence for user ${user.email} in song ${payload.songId}`,
			// );
		} catch (error: any) {
			this.logger.warn(
				`Failed to update editor user presence: ${error.message}`,
				error.stack,
			);
		}

		// Then, update general user presence with recovery logic
		try {
			const updated = await this.userPresenceService.updateLastSeen(user.id);

			// If the update failed (method returns false), we need to re-add the user
			if (!updated) {
				this.logger.warn(
					`User ${user.email} (ID: ${user.id}) not found in Redis, attempting to recover...`,
				);

				// Recovery logic: If user is not found, re-add them with current socket ID
				try {
					// Create a jwtUser object from the CurrentWebSocketUser
					const jwtUser: CurrentJwtUser = omit(user, 'client');

					// Re-add the user to the UserPresenceService with the CURRENT socket ID
					await this.userPresenceService.addUser(client.id, jwtUser);
					this.logger.log(
						`Successfully re-added user ${user.email} to presence tracking system with socket ${client.id}`,
					);
				} catch (recoveryError: any) {
					this.logger.error(
						`Failed to recover user presence for ${user.email}: ${recoveryError.message}`,
						recoveryError.stack,
					);
				}
			}
		} catch (error: any) {
			this.logger.error(
				`Critical error in heartbeat processing: ${error.message}`,
				error.stack,
			);
		}

		// Log at debug level - only visible when debug logging is enabled
		this.logger.debug(`Heartbeat ping ${user.email} songId=${payload.songId}`);
	}

	@SubscribeMessage(WebSocketEvent.EditorLeave)
	async editorLeave(
		@ConnectedSocket() client: Socket,
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody() payload: { songId: string },
	) {
		try {
			client.leave(`song:${payload.songId}`);

			await this.editorUserPresenceService.removeUserFromSong(
				payload.songId,
				user.userId,
			);
			await this.emitUserPresence(payload.songId);

			client
				.to(`song:${payload.songId}`)
				.emit(WebSocketEvent.EditorLeave, { data: { userId: user.userId } });

			this.logger.debug(
				`${logColorPrimary(WebSocketEvent.EditorLeave)} ${logColorSecondary(
					user.email,
				)} songId=${payload.songId}`,
			);

			return {
				status: 'success',
				message: `Left session ${payload.songId}`,
			};
		} catch (error: any) {
			this.logger.error(error);

			return {
				status: 'error',
				message: `Failed to leave session: ${error.message || 'Unknown error'}`,
			};
		}
	}

	@SubscribeMessage(WebSocketEvent.EditorLyricsUpdated)
	async editorLyricsUpdate(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { songId: string; changes: never },
	) {
		user.client
			.to(`song:${payload.songId}`)
			.emit(WebSocketEvent.EditorLyricsUpdated, {
				data: payload.changes,
			});
	}

	@SubscribeMessage(WebSocketEvent.EditorTransaction)
	async editorTransaction(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { steps: never; songId: string },
	) {
		user.client
			.to(`song:${payload.songId}`)
			.emit(WebSocketEvent.EditorTransaction, {
				data: payload.steps,
			});
	}

	@SubscribeMessage(WebSocketEvent.EditorTitleUpdated)
	async editorTitleUpdate(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody() payload: { songId: string; title: string },
	) {
		user.client
			.to(`song:${payload.songId}`)
			.emit(WebSocketEvent.EditorTitleUpdated, { data: payload.title });
	}

	@SubscribeMessage(WebSocketEvent.EditorRequestSessionLead)
	async editorRequestLead(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { songId: string },
	): Promise<WebSocketAck> {
		if (!payload.songId) {
			this.logger.debug(
				`${logColorPrimary(WebSocketEvent.EditorRequestSessionLead)} "songId" missing`,
			);
		}

		const songRepo = this.dataSource.getRepository(SongEntity);

		// this won't be doing custom hydration for the song
		// so properties like 'currentUserIsLeader' and 'currentUserIsOwner' won't be available
		const song = await songRepo.findOneOrFail({
			where: { externalId: payload.songId },
			relations: ['sessionLeader', 'owner'],
		});

		this.server
			// if there is no session leader, then the song owner
			// will be the default leader
			.to(getUserRoomName(song.sessionLeader ?? song.owner))
			.emit(WebSocketEvent.EditorRequestSessionLead, {
				data: { userId: user.userId },
			});

		return { status: 'success', message: 'Request sent' };
	}

	@SubscribeMessage(WebSocketEvent.EditorAssignSessionLead)
	async editorAssignLead(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { songId: string; userId: string },
	): Promise<WebSocketAck> {
		const songRepo = this.dataSource.getRepository(SongEntity);

		// this won't be doing custom hydration for the song
		// so properties like 'currentUserIsLeader' and 'currentUserIsOwner' won't be available
		const song = await songRepo.findOne({
			where: { externalId: payload.songId },
			relations: ['sessionLeader', 'owner'],
		});

		if (!song) {
			return { status: 'error', message: 'Song not found' };
		}

		// check the acting user is the leader or the owner
		// If a session leader has been assigned (it might be null)
		if (song.sessionLeader && song.sessionLeader.id !== user.id) {
			return { status: 'error', message: 'Only leader can assign new lead' };
		}

		if (!song.sessionLeader && song.owner.id !== user.id) {
			return { status: 'error', message: 'Only owner can assign new lead' };
		}

		const userRepo = this.dataSource.getRepository(UserEntity);
		const newLeadUser = await userRepo.findOneBy({ userId: payload.userId });

		if (!newLeadUser) {
			return { status: 'error', message: 'User not found' };
		}

		try {
			await songRepo.update(song.id, {
				sessionLeader: {
					id: newLeadUser.id,
				},
			});
		} catch (e) {
			console.error(e);
			return { status: 'error', message: 'Failed to assign session lead' };
		}

		// just return the userId, the user's profile will already be in the UI
		// state, so the UI can be responsible for fetching that
		this.server
			.to(`song:${payload.songId}`)
			.emit(WebSocketEvent.EditorAssignSessionLeadSuccess, {
				data: { userId: newLeadUser.userId },
			});

		return { status: 'success', message: 'Session lead assigned' };
	}
}
