import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from 'web-app-backend/redis';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class EditorUserPresenceService {
	private logger = new Logger(EditorUserPresenceService.name);

	constructor(private redisService: RedisService) {}

	/**
	 * Using Redis, set the given user as present for a given song
	 *
	 * tore their lastSeen timestamp
	 *
	 * @param songId
	 * @param userId
	 */
	setUserPresent(songId: string, userId: string) {
		return this.redisService
			.getClient()
			.hset(`editor:song:${songId}`, userId, Date.now());
	}

	removeUserFromSong(songId: string, userId: string) {
		return this.redisService.getClient().hdel(`editor:song:${songId}`, userId);
	}

	/**
	 * @param songId
	 * @return string[] User external ids who are online
	 */
	getUsersIdsOnlineForSong(songId: string) {
		return this.redisService.getClient().hkeys(`editor:song:${songId}`);
	}

	/**
	 * Check if a user is currently present in a specific song editor
	 *
	 * @param songId The external ID of the song
	 * @param userId The external ID of the user
	 * @returns boolean True if the user is present in the song
	 */
	async isUserPresentInSong(songId: string, userId: string): Promise<boolean> {
		const lastSeen = await this.redisService
			.getClient()
			.hget(`editor:song:${songId}`, userId);
		return !!lastSeen;
	}

	/**
	 * Remove users who haven't been seen in the last 10 minutes
	 */
	@Cron(CronExpression.EVERY_30_SECONDS)
	async cleanupStaleUsers(): Promise<void> {
		const now = Date.now();
		const tenMinutesAgo = now - 1000 * 60 * 10;
		const thirtySecondsAgo = now - 1000 * 30;

		// Get all song keys
		const keys = await this.redisService.keys('editor:song:*');
		let staleUsersRemoved = 0;

		// Process each song key
		for (const songKey of keys) {
			const songData = await this.redisService.getClient().hgetall(songKey);
			if (!songData) continue;

			// Find stale users for this song
			const staleUsers = Object.entries(songData)
				.filter(([_, lastSeenStr]) => {
					const lastSeen = Number.parseInt(lastSeenStr as string, 10);

					// users in the editor send a heartbeat every 15 seconds
					// so if a user was last seen over 30 sceonds ago
					// then let's consider them stale and get rid
					return lastSeen < thirtySecondsAgo;
					// return lastSeen < tenMinutesAgo;
				})
				.map(([userId]) => userId);

			// Remove stale users from this song
			if (staleUsers.length > 0) {
				if (staleUsers.length === Object.keys(songData).length) {
					// All users are stale, delete the entire key
					await this.redisService.getClient().del(songKey);
					this.logger.debug(
						`Removed entire song key ${songKey} as all users are stale`,
					);
				} else {
					// Remove only stale users
					for (const userId of staleUsers) {
						await this.redisService.getClient().hdel(songKey, userId);
					}

					if (staleUsers.length > 0) {
						this.logger.debug(
							`Removed ${staleUsers.length} stale users from ${songKey}`,
						);
					}
				}

				staleUsersRemoved += staleUsers.length;
			}
		}

		if (staleUsersRemoved > 0) {
			this.logger.debug(
				`Removed ${staleUsersRemoved} stale users from song editor sessions`,
			);
		}
	}
}
