import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { NotificationEntity } from 'web-app-backend/notifications/entities';
import { Repository } from 'typeorm';
import {
	NotificationFactory,
	NotificationsProducer,
} from 'web-app-backend/notifications';
import { RefreshMessageService } from '@major/web-app-backend/util-websocket';
import { NotificationType } from '@major/shared/models';
import { CollaborationGroupFactory } from 'web-app-backend/collaborations-events';
import { InviteSongEntity } from 'web-app-backend/invites/entities';

@Injectable()
export class InviteEventHandlerBase {
	protected logger = new Logger(this.constructor.name);

	constructor(
		@InjectRepository(NotificationEntity)
		protected notificationRepository: Repository<NotificationEntity>,
		protected notificationProducer: NotificationsProducer,
		protected refreshMessageService: RefreshMessageService,
		protected notificationFactory: NotificationFactory,
		protected collaborationGroupFactory: CollaborationGroupFactory,
	) {}

	/**
	 * Fetch the notification associated with thie given event
	 * and mark it as read
	 *
	 * @param event
	 * @private
	 */
	protected async markInviteeNotificationAsRead(invite: InviteSongEntity) {
		//
		// Mark the original invitation as read
		// (the one seen by the invitee)
		// Do this in a transaction so the subscribers will have finished
		// before continuing
		//
		await this.notificationRepository.manager.transaction(
			async (transactionalEntityManager) => {
				try {
					const notification = await transactionalEntityManager.findOneByOrFail(
						NotificationEntity,
						{
							userId: invite.invitee!.id,
							referenceIdString: invite.id,
							type: NotificationType.SONG_INVITE,
						},
					);

					notification.readAt = new Date();
					await transactionalEntityManager.save(notification);
				} catch (e) {
					// there was no notification to mark as read
					// probably due to the fact the invitee was a non-user
					// and no notification was sent to them in the first place
				}
			},
		);
	}
}
