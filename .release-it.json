{"git": {"commitMessage": "chore(release): ${version}", "tagName": "${version}", "push": true, "requireCleanWorkingDir": false}, "npm": {"publish": false}, "gitlab": {"release": true, "releaseName": "Release ${version}", "tokenRef": "GITLAB_TOKEN"}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}, "hooks": {"after:bump": ["bun run ./infra/scripts/release/release-hook-runner.ts generateVersionFiles", "bun run ./infra/scripts/release/release-hook-runner.ts updateManifestVersion", "bun run ./infra/scripts/release/release-hook-runner.ts checkAndStageGeneratedFiles"], "after:git:commit": ["bun run ./infra/scripts/release/release-hook-runner.ts generateDeploymentInfoAndAmend"], "after:release": "bun run ./infra/scripts/release/release-hook-runner.ts generateCustomerChangelog"}}