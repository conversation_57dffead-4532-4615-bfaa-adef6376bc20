import { Inject, Injectable } from '@nestjs/common';
import {
	createCipheriv,
	createDecipheriv,
	randomBytes,
	scryptSync,
} from 'node:crypto';
import { INVITE_SECRET_KEY } from 'web-app-backend/util-config';

@Injectable()
export class UrlSafePayloadService {
	private readonly algorithm = 'aes-256-gcm';

	constructor(@Inject(INVITE_SECRET_KEY) private readonly secretKey: string) {
		if (!this.secretKey) {
			throw new Error('Secret key is required');
		}
	}

	/**
	 * @description
	 *
	 * The `UrlSafePayloadService` takes a payload and encrypts it using AES-256-GCM
	 * with a secret key. The payload is first stringified and then encrypted. The
	 * IV and authTag are prepended to the encrypted payload, and the resulting
	 * string is base64url encoded.
	 *
	 * The algorithm uses the following key sizes:
	 * - Secret key: 32 bytes
	 * - IV: 12 bytes
	 * - Auth tag: 16 bytes
	 *
	 * The resulting URL-safe payload is a base64url encoded string of the format:
	 * `${ivHex}:${encrypted}:${authTagHex}`
	 *
	 * @example
	 * const payload = { foo: 'bar' };
	 * const urlSafePayload = service.createurlSafePayload(payload);
	 * console.log(urlSafePayload);
	 * // Output: 3a2c9f35f3b8:7a7e4e3f3b7e4e3f:7a7e4e3f3b7e4e3f
	 */
	createUrlSafePayload(payload: unknown): string {
		const iv = randomBytes(12);
		const key = scryptSync(this.secretKey, 'salt', 32);
		const cipher = createCipheriv(this.algorithm, key, iv);

		let encrypted = cipher.update(JSON.stringify(payload), 'utf8', 'hex');
		encrypted += cipher.final('hex');

		const authTag = cipher.getAuthTag();

		const urlSafePayload = Buffer.from(
			`${iv.toString('hex')}:${encrypted}:${authTag.toString('hex')}`,
		).toString('base64url');

		return urlSafePayload;
	}

	/**
	 * @description
	 *
	 * The `UrlSafePayloadService` takes a payload and encrypts it using AES-256-GCM
	 * with a secret key. The payload is first stringified and then encrypted. The
	 * IV and authTag are prepended to the encrypted payload, and the resulting
	 * string is base64url encoded.
	 *
	 * The algorithm uses the following key sizes:
	 * - Secret key: 32 bytes
	 * - IV: 12 bytes
	 * - Auth tag: 16 bytes
	 *
	 * The resulting URL-safe payload is a base64url encoded string of the format:
	 * `${ivHex}:${encrypted}:${authTagHex}`
	 *
	 * @example
	 * const payload = { foo: 'bar' };
	 * const urlSafePayload = service.createurlSafePayload(payload);
	 * console.log(urlSafePayload);
	 * // Output: 3a2c9f35f3b8:7a7e4e3f3b7e4e3f:7a7e4e3f3b7e4e3f
	 */
	validateUrlPayload<T = any>(urlPayload: string): T {
		const [ivHex, encrypted, authTagHex] = Buffer.from(urlPayload, 'base64url')
			.toString()
			.split(':');

		const key = scryptSync(this.secretKey, 'salt', 32);
		const iv = Buffer.from(ivHex, 'hex');
		const decipher = createDecipheriv(this.algorithm, key, iv);

		decipher.setAuthTag(Buffer.from(authTagHex, 'hex'));

		let decrypted = decipher.update(encrypted, 'hex', 'utf8');
		decrypted += decipher.final('utf8');

		const payload: T = JSON.parse(decrypted);

		return payload;
	}
}
