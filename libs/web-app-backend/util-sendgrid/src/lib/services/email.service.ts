import { Inject, Injectable, Logger } from '@nestjs/common';
import { SENDGRID_CLIENT } from '../symbols';
import { MailService } from '@sendgrid/mail/src/mail';
import { SendGridSendOptions } from '../types/send-grid-send-options.interface';
import { MailDataRequired } from '@sendgrid/helpers/classes/mail';
import { EMAIL_FROM_ADDRESS, EMAIL_FROM_NAME } from 'mailer';

/**
 * @deprecated
 */
@Injectable()
export class EmailService {
	private readonly logger = new Logger(EmailService.name);

	constructor(
		@Inject(EMAIL_FROM_NAME) private readonly emailFromName: string,
		@Inject(EMAIL_FROM_ADDRESS) private readonly emailFromAddress: string,
		@Inject(SENDGRID_CLIENT) private readonly sgMail: MailService,
	) {}

	/**
	 * @deprecated
	 * @param options
	 */
	async send(options: SendGridSendOptions) {
		try {
			const builtOptions: MailDataRequired = {
				to: options.recipient,
				from: options.from ?? {
					email: this.emailFromAddress,
					name: this.emailFromName,
				},
				templateId: options.templateId,
				trackingSettings: {
					clickTracking: {
						enable: false,
					},
				},
				dynamicTemplateData: options.templateData,
			};

			if (options.subject) {
				builtOptions.subject = options.subject;
			}

			await this.sgMail.send(builtOptions);

			this.logger.log(
				`Email sent ${JSON.stringify(options.recipient)}, template:${options.templateId}`,
			);
		} catch (error) {
			this.logger.error(
				`Email error: ${JSON.stringify(options.recipient)}, template:${
					options.templateId
				}`,
			);

			throw error;
		}
	}
}
