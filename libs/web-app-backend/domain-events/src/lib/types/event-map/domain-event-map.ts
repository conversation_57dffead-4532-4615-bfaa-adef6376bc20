import {
  InviteAcceptedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-accepted.event';
import {
  InviteCreatedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-created.event';
import {
  InviteDeclinedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-declined.event';
import {
  InviteCancelledEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-cancelled.event';
import { SongUpdatedEvent } from '../events/song/song-updated.event';
import { UserCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-created.event';
import {
  CollaborationMessageCreatedEvent,
} from '../events/collaboration-messaging/collaboration-message-created.event';
import { FileMetaDataCreatedEvent } from '../events/file-meta-data/file-meta-data-created.event';
import {
  FileMetaDataUpdatedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/file-meta-data/file-meta-data-updated.event';
import { IdeaCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/idea/idea-created.event';
import { FileMetaDataRemovedEvent } from '../events/file-meta-data/file-meta-data-removed.event';
import { CloudConvertJobUpdatedEvent } from '../events/import/cloud-convert-job-updated.event';
import {
  ImportCompleteEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/import/import-complete.event';
import { ProjectCreatedEvent } from '../events/project/project-created.event';
import {
  CollaborationRemovedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-removed.event';
import { CollaborationLeftEvent } from '../events/collaboration/collaboration-left.event';
import {
  SongCreatedWithInvitesEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-created-with-invites.event';
import { SongCreatedEvent } from '../events/song/song-created.event';
import { StripeSubscriptionCreatedEvent } from '../events/payments/stripe-subscription-created.event';
import {
  UserSubscriptionChangedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-subscription-changed.event';
import { StripeSubscriptionUpdatedEvent } from '../events/payments/stripe-subscription-updated.event';
import { StripePriceUpdatedEvent } from '../events/payments/stripe-price-updated.event';
import {
  StripeProductUpdatedEvent,
} from 'libs/web-app-backend/domain-events/src/lib/types/events/payments/stripe-product-updated.event';
import { SongCopySentEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-copy-sent.event';

export type DomainEventMap = {
	//
	// Song
	//
	[SongUpdatedEvent.eventName]: SongUpdatedEvent;
	[SongCreatedEvent.eventName]: SongCreatedEvent;
	[SongCreatedWithInvitesEvent.eventName]: SongCreatedWithInvitesEvent;
	[SongCopySentEvent.eventName]: SongCopySentEvent;

	//
	// User
	//
	[UserCreatedEvent.eventName]: UserCreatedEvent;
	[UserSubscriptionChangedEvent.eventName]: UserSubscriptionChangedEvent;

	//
	// Collaboration
	//
	[CollaborationRemovedEvent.eventName]: CollaborationRemovedEvent;
	[CollaborationLeftEvent.eventName]: CollaborationLeftEvent;

	//
	// Collaboration Message
	//
	[CollaborationMessageCreatedEvent.eventName]: CollaborationMessageCreatedEvent;

	//
	// Cloud Convert Job
	//
	[CloudConvertJobUpdatedEvent.eventName]: CloudConvertJobUpdatedEvent;

	//
	// Import
	//
	[ImportCompleteEvent.eventName]: ImportCompleteEvent;

	//
	// Idea
	//
	[IdeaCreatedEvent.eventName]: IdeaCreatedEvent;

	//
	// FileMetaData
	//
	[FileMetaDataCreatedEvent.eventName]: FileMetaDataCreatedEvent;
	[FileMetaDataUpdatedEvent.eventName]: FileMetaDataUpdatedEvent;
	[FileMetaDataRemovedEvent.eventName]: FileMetaDataRemovedEvent;

	//
	// InviteSong
	//
	[InviteCreatedEvent.eventName]: InviteCreatedEvent;
	[InviteAcceptedEvent.eventName]: InviteAcceptedEvent;
	[InviteDeclinedEvent.eventName]: InviteDeclinedEvent;
	[InviteCancelledEvent.eventName]: InviteCancelledEvent;

	//
	// Project
	//
	[ProjectCreatedEvent.eventName]: ProjectCreatedEvent;

	//
	// Stripe
	//
	[StripeSubscriptionCreatedEvent.eventName]: StripeSubscriptionCreatedEvent;
	[StripeSubscriptionUpdatedEvent.eventName]: StripeSubscriptionUpdatedEvent;
	[StripePriceUpdatedEvent.eventName]: StripePriceUpdatedEvent;
	[StripeProductUpdatedEvent.eventName]: StripeProductUpdatedEvent;
};

export type DomainEvent = DomainEventMap[keyof DomainEventMap];
