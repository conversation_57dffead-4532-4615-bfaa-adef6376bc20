import { <PERSON><PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';
import CloudConvert from 'cloudconvert';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { CloudConvertEntityService } from './services/cloud-convert-entity.service';
import { CloudConvertWebhookController } from './controllers/cloud-convert-webhook.controller';
import { CloudConvertJobEntity } from './types/entities/cloud-convert-job.entity';
import { ConversionConsumer } from './queue/conversion.consumer';
import { NODE_ENV, provideConfigValue } from 'web-app-backend/util-config';
import {
	CLOUD_CONVERT_ACCESS_TOKEN,
	CLOUD_CONVERT_WEBHOOK_SECRET_JOB_CREATED,
	CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FAILED,
	CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FINISHED,
} from './types/symbols';
import { CloudConvertController } from './controllers/cloud-convert.controller';
import { ConversionProducer } from './queue/conversion.producer';
import { WebAppBackendUtilSecurityModule } from 'web-app-backend/util-security';
import { CloudConvertSdkService } from './services/cloud-convert-sdk.service';
import { WebAppBackendStorageModule } from '@major/web-app-backend/storage';
import { ResultProducer } from './queue/result.producer';
import { HttpModule } from '@nestjs/axios';
import { WebAppBackendSongsModule } from '@major/web-app-backend/songs';
import { ResultConsumer } from './queue/result.consumer';
import { CloudConvertEventEmitter } from 'libs/web-app-backend/cloud-convert/src/services/cloud-convert-event-emitter.service';
import { WebAppBackendUserQuotasModule } from 'web-app-backend/user-quotas';

const logger = new Logger('WebAppBackendCloudConvertModule');

@Module({
	imports: [
		HttpModule,
		BullModule.registerQueue({
			name: 'cloud-convert',
			defaultJobOptions: {
				removeOnComplete: 100,
				removeOnFail: 100,
			},
		}),
		BullModule.registerQueue({
			name: 'cloud-convert-result',
			defaultJobOptions: {
				removeOnComplete: 100,
				removeOnFail: 100,
			},
		}),
		TypeOrmModule.forFeature([CloudConvertJobEntity]),
		WebAppBackendStorageModule,
		WebAppBackendUtilSecurityModule,
		WebAppBackendSongsModule,
		WebAppBackendUserQuotasModule,
	],
	controllers: [CloudConvertController, CloudConvertWebhookController],
	providers: [
		CloudConvertEntityService,
		ConversionConsumer,
		ConversionProducer,
		ResultProducer,
		ResultConsumer,
		CloudConvertSdkService,
		CloudConvertEventEmitter,
		{
			provide: 'CloudConvert',
			useFactory: (apiKey: string, nodeEnv: string) => {
				const useSandbox = ['e2e', 'development'].includes(nodeEnv);

				if (useSandbox) {
					logger.debug('CloudConvert sandbox mode');
				}

				return new CloudConvert(apiKey, useSandbox);
			},
			inject: [CLOUD_CONVERT_ACCESS_TOKEN, NODE_ENV],
		},
		provideConfigValue(
			CLOUD_CONVERT_ACCESS_TOKEN,
			'CLOUD_CONVERT_ACCESS_TOKEN',
		),
		provideConfigValue(
			CLOUD_CONVERT_WEBHOOK_SECRET_JOB_CREATED,
			'CLOUD_CONVERT_WEBHOOK_SECRET_JOB_CREATED',
		),
		provideConfigValue(
			CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FINISHED,
			'CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FINISHED',
		),
		provideConfigValue(
			CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FAILED,
			'CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FAILED',
		),
	],
	exports: [
		CloudConvertEntityService,
		CloudConvertSdkService,
		CloudConvertEventEmitter,
	],
})
export class WebAppBackendCloudConvertModule {}
