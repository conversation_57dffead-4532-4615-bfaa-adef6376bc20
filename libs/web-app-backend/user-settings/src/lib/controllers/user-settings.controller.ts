import {
	<PERSON>,
	Get,
	Logger,
	NotFoundException,
	Patch,
} from '@nestjs/common';
import { type CurrentJwtUser, JwtUser } from '@major/web-app-backend/util-auth';
import { instanceToPlain } from 'class-transformer';
import { InjectRepository } from '@nestjs/typeorm';
import { UserSettingsEntity } from 'libs/web-app-backend/user-settings/src/lib/types/entities/user-settings.entity';
import { Repository } from 'typeorm';
import { MessageBody } from '@nestjs/websockets';
import { UpdateSettingsDto } from 'libs/web-app-backend/user-settings/src/lib/types/dto/update-settings.dto';

@Controller('users/settings')
export class UserSettingsController {
	private logger = new Logger(UserSettingsController.name);

	constructor(
		@InjectRepository(UserSettingsEntity)
		private repo: Repository<UserSettingsEntity>,
	) {}

	/**
	 * Get the current quota status for the authenticated user
	 */
	@Get()
	async getAll(@JwtUser() user: CurrentJwtUser) {
		try {
			const settings = await this.repo.findOneOrFail({
				where: { userId: user.id },
				relations: [],
			});

			return instanceToPlain(settings);
		} catch (e) {
			this.logger.debug(`User ${user.id} settings not found`);

			throw new NotFoundException(`User ${user.userId} settings not found`);
		}
	}

	@Patch()
	async update(
		@JwtUser() user: CurrentJwtUser,
		@MessageBody() changes: UpdateSettingsDto,
	) {
		try {
			const settings = await this.repo.findOneOrFail({
				where: { userId: user.id },
				relations: [],
			});

			settings.songActivitySummary = changes.songActivitySummary;

			return instanceToPlain(await this.repo.save(settings));
		} catch (e) {
			this.logger.debug(`User ${user.id} settings not found`);

			throw new NotFoundException(`User ${user.userId} settings not found`);
		}
	}
}
