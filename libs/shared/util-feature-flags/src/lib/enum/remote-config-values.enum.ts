// noinspection SpellCheckingInspection
export enum FeatureFlag {
	beta_signup_enabled = 'beta_signup_enabled',
	convert_song_idea = 'convert_song_idea',
	editor_calls_enabled = 'editor_calls_enabled',
	feature_friends_enabled = 'feature_friends_enabled',
	feature_scratchpad_enabled = 'feature_scratchpad_enabled',
	feature_memberships_enabled = 'feature_memberships_enabled',
	feature_messages_enabled = 'feature_messages_enabled',
	feature_player_enabled = 'feature_player_enabled',
	feature_quotas_enabled = 'features_quotas_enabled',
	feature_search_enabled = 'feature_search_enabled',
	feature_sharing_enabled = 'feature_sharing_enabled',
	feature_split_sheet_enabled = 'feature_split_sheet_enabled',
	feature_starred_enabled = 'feature_starred_enabled',
	feature_subscribe_enabled = 'feature_subscribe_enabled',
	features_recordings_enabled = 'features_recordings_enabled',
	features_tasks_enabled = 'features_tasks_enabled',
	labels_enabled = 'feature_labels_enabled',
	organise_enabled = 'organise_enabled',
	pseudonyms_enabled = 'pseudonyms_enabled',
	purchase_enabled = 'purchase_enabled',
	signup_enabled = 'signup_enabled',
	song_activity_enabled = 'song_activity_enabled',
	usernames_enabled = 'usernames_enabled',
}
