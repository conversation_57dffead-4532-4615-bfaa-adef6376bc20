import { MigrationInterface, QueryRunner } from 'typeorm';

export class InviteeIdNullable1751904138731 implements MigrationInterface {
	name = 'InviteeIdNullable1751904138731';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" DROP CONSTRAINT "FK_1e1842aa3640e5345a22098970a"`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ALTER COLUMN "inviteeId" DROP NOT NULL`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ADD CONSTRAINT "FK_1e1842aa3640e5345a22098970a" FOREIGN KEY ("inviteeId") REFERENCES "app"."user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" DROP CONSTRAINT "FK_1e1842aa3640e5345a22098970a"`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ALTER COLUMN "inviteeId" SET NOT NULL`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ADD CONSTRAINT "FK_1e1842aa3640e5345a22098970a" FOREIGN KEY ("inviteeId") REFERENCES "app"."user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
		);
	}
}
