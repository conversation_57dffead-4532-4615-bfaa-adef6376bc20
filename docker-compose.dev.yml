networks:
  major:
    driver: bridge

name: Major dev

services:
  web-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: web-app
    develop:
      watch:
        - path: ./
          target: /usr/src/app
          action: sync
          ignore:
            - db
            - .run
            - node_modules/
            - dist/
            - .git/
            - apps/marketing-app
    depends_on:
      - reverse-proxy
      - web-app-backend
      - logto
      - peerjs
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=major'
      - 'traefik.http.routers.web-app.rule=Host(`web.dev.major.app`)'
      - 'traefik.http.routers.web-app.entrypoints=websecure'
      - 'traefik.http.services.web-app.loadbalancer.server.port=4200'
      - 'traefik.http.routers.web-app.tls=true'
    volumes:
      - /usr/src/app/node_modules
    restart: on-failure
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4200']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - major

  reverse-proxy:
    image: traefik:v2.11
    command:
      - '--api.insecure=true'
      - '--providers.docker=true'
      - '--entrypoints.web.address=:80' # remove this for production
      - '--entrypoints.websecure.address=:443'
      - '--entrypoints.websecure.http.tls=true'
      - '--providers.file.directory=/etc/traefik/dynamic_conf'
      - '--log.level=WARNING'
    ports:
      - '80:80'
      - '443:443'
      - '8081:8080'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./tools/letsencrypt:/letsencrypt
      - ./tools/certs:/certs
      - ./tools/traefik/config.yml:/etc/traefik/dynamic_conf/conf.yml:ro
    networks:
      major:
        aliases:
          - auth.dev.major.app
          - logto.dev.major.app

  ngrok:
    image: ngrok/ngrok:latest
    command:
      - "http"
      - "https://host.docker.internal"
      - "--host-header=api.dev.major.app"
      - "--url=casual-quickly-newt.ngrok-free.app"
    environment:
      NGROK_AUTHTOKEN: ${NGROK_AUTHTOKEN}

  stripe-cli:
    image: stripe/stripe-cli:latest
    command: listen --api-key ${STRIPE_SECRET_KEY} --forward-to http://web-app-backend:3000/payments/webhook
    stdin_open: true
    tty: true
    networks:
      - major

  logto:
    deploy:
      restart_policy:
        condition: on-failure
    image: 'ghcr.io/logto-io/logto:1.27'
    depends_on:
      - reverse-proxy
      - db
    entrypoint: ['sh', '-c', 'npm run cli db seed -- --swe && npm start']
    environment:
      - TRUST_PROXY_HEADER=1
      - 'DB_URL=************************************/logto'
      - ENDPOINT=https://auth.dev.major.app
      - ADMIN_ENDPOINT=https://logto.dev.major.app
      - NODE_EXTRA_CA_CERTS=/etc/certs/ca.crt
    volumes:
      - ./tools/certs/rootCA.pem:/etc/certs/ca.crt:ro
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=major'
      - 'traefik.http.routers.logto.rule=Host(`auth.dev.major.app`)'
      - 'traefik.http.routers.logto.entrypoints=websecure'
      - 'traefik.http.routers.logto.service=logto'
      - 'traefik.http.services.logto.loadbalancer.server.port=3001'
      - 'traefik.http.routers.logto.tls=true'
      - 'traefik.http.routers.logto-admin.rule=Host(`logto.dev.major.app`)'
      - 'traefik.http.routers.logto-admin.entrypoints=websecure'
      - 'traefik.http.routers.logto-admin.service=logto-admin'
      - 'traefik.http.services.logto-admin.loadbalancer.server.port=3002'
      - 'traefik.http.routers.logto-admin.tls=true'
    networks:
      - major

  db:
    image: postgres:16-alpine
    container_name: postgres
    mem_limit: 8g
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=major
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - ./db/data:/var/lib/postgresql/data
      - ./db/init:/docker-entrypoint-initdb.d/
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready']
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - major

  migrations-runner:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: migrations-runner
    develop:
      watch:
        - path: ./apps/migrations-runner
          target: /usr/src/app/apps/migrations-runner
          action: sync+restart
          ignore:
            - db
            - infra
            - .run
            - node_modules/
            - dist/
            - .git/
        - path: ./libs/web-app-backend/database-migrations
          target: /usr/src/app/libs/web-app-backend/database-migrations
          action: sync+restart
          ignore:
            - db
            - infra
            - .run
            - node_modules/
            - dist/
            - .git/
    depends_on:
      - db
    labels:
      - 'traefik.enable=false'
    networks:
      - major

  redis:
    image: redis:alpine
    ports:
      - '6379:6379'
    networks:
      - major
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  peerjs:
    image: peerjs/peerjs-server
    command: peerjs --port 9000 --proxied true --key ${PEERJS_KEY} --path /
    depends_on:
      - reverse-proxy
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.peerjs.rule=Host(`peerjs.dev.major.app`)'
      - 'traefik.http.routers.peerjs.entrypoints=websecure'
      - 'traefik.http.services.peerjs.loadbalancer.server.port=9000'
      - 'traefik.http.routers.peerjs.tls=true'
    healthcheck:
      test: ['CMD', 'wget', '--spider', '-q', 'http://localhost:9000']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    networks:
      - major

  web-app-backend:
    platform: linux/arm64
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: web-app-backend
    deploy:
      restart_policy:
        condition: on-failure
        max_attempts: 3
    depends_on:
      - db
      - logto
      - redis
      - ngrok
      - stripe-cli
      - migrations-runner
    develop:
      watch:
        - path: ./
          target: /usr/src/app
          action: sync
          ignore:
            - db
            - infra
            - .run
            - node_modules/
            - dist/
            - .git/
    env_file:
      - .env
    volumes:
#      - .:/usr/src/app
      - /usr/src/app/node_modules
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=major'
      - 'traefik.http.routers.web-app-backend.rule=Host(`api.dev.major.app`)'
      - 'traefik.http.routers.web-app-backend.entrypoints=websecure'
      - 'traefik.http.services.web-app-backend.loadbalancer.server.port=3000'
      - 'traefik.http.routers.web-app-backend.tls=true'
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - major

  marketing:
    build:
      target: marketing
      context: .
      dockerfile: Dockerfile.dev
    develop:
      watch:
        - path: ./
          target: /usr/src/app
          action: sync
          ignore:
            - db
            - .run
            - infra
            - node_modules/
            - dist/
            - .git/
            - apps/web-app
            - apps/web-app-backend
            - libs/web-app-backend
    labels:
      - 'traefik.enable=true'
      - 'traefik.docker.network=major'
      - 'traefik.http.routers.marketing.rule=Host(`dev.major.app`)'
      - 'traefik.http.routers.marketing.entrypoints=websecure'
      - 'traefik.http.services.marketing.loadbalancer.server.port=4200'
      - 'traefik.http.routers.marketing.tls=true'
    volumes:
      - /usr/src/app/node_modules
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4200']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - major

  mailpit:
    image: axllent/mailpit
    container_name: mailpit
    restart: unless-stopped
    volumes:
      - ./infra/docker/volumes/mailpit/mailpit.db:/data/mailpit.db
    ports:
      - "8025:8025"
      - "1025:1025"
    environment:
      MP_MAX_MESSAGES: 5000
      MP_DATABASE: /data/mailpit.db
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    networks:
      - major
