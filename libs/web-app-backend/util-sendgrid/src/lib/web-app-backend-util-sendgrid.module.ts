import { Modu<PERSON> } from '@nestjs/common';
import sgMail from '@sendgrid/mail';
import { SENDGRID_API_KEY, SENDGRID_CLIENT } from './symbols';
import { MailService } from '@sendgrid/mail/src/mail';
import {
	INVITE_SECRET_KEY,
	provideConfigValue,
	WEB_APP_PATH_INVITE_LANDING,
} from 'web-app-backend/util-config';
import { EmailService } from './services';
import { InviteReceivedEmail, SendGridOptionsFactory } from './emails';
import { EMAIL_FROM_ADDRESS, EMAIL_FROM_NAME } from 'mailer';

/**
 * @deprecated
 */
@Module({
	controllers: [],
	providers: [
		SendGridOptionsFactory,
		EmailService,
		InviteReceivedEmail,
		provideConfigValue(SENDGRID_API_KEY, 'SENDGRID_API_KEY'),
		provideConfigValue(EMAIL_FROM_ADDRESS, 'EMAIL_FROM_ADDRESS'),
		provideConfigValue(EMAIL_FROM_NAME, 'EMAIL_FROM_NAME'),
		provideConfigValue(INVITE_SECRET_KEY, 'INVITE_SECRET_KEY'),
		provideConfigValue(
			WEB_APP_PATH_INVITE_LANDING,
			'WEB_APP_PATH_INVITE_LANDING',
		),
		provideConfigValue(
			WEB_APP_PATH_INVITE_LANDING,
			'WEB_APP_PATH_INVITE_LANDING',
		),
		{
			provide: SENDGRID_CLIENT,
			useFactory: (sendGridApiKey: string): MailService => {
				sgMail.setApiKey(sendGridApiKey);
				return sgMail;
			},
			inject: [SENDGRID_API_KEY],
		},
	],
	exports: [EmailService, SendGridOptionsFactory],
})
export class WebAppBackendUtilSendgridModule {}
