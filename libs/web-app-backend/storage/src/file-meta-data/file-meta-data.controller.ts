import {
	BadRequestException,
	Body,
	Controller,
	Delete,
	Get,
	Logger,
	NotFoundException,
	Param,
	Patch,
	Put,
} from '@nestjs/common';
import { FileMetaDataService } from './file-meta-data.service';
import { CreateFileMetaDataDto } from '@major/shared/models/interfaces';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { FileMetaDataEntity } from './entities/file-meta-data.entity';
import { FileCategory } from '@major/shared/models';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntityService } from '@major/web-app-backend/songs';
import { CurrentJwtUser, JwtUser } from '@major/web-app-backend/util-auth';
import { InjectRepository } from '@nestjs/typeorm';
import { FileOrderEntity } from '@major/web-app-backend/songs/entities';
import { Repository } from 'typeorm';
import { sortSongFiles } from '../../../songs/src/lib/utils/sort-song-files';
import dayjs from 'dayjs';
import { S3BucketService } from '../lib/services/s3-bucket.service';
import { HmacService } from 'web-app-backend/util-security';
import { UserQuotaService } from 'web-app-backend/user-quotas';
import { RenameFileMetaDataDto } from './dto/rename-file-meta-data.dto';
import { DomainEventEmitter } from 'domain-events';
import { FileMetaDataCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/file-meta-data/file-meta-data-created.event';
import { FileMetaDataRemovedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/file-meta-data/file-meta-data-removed.event';

@Controller('file-meta-data')
export class FileMetaDataController {
	private logger = new Logger('FileMetaDataController');

	constructor(
		@InjectRepository(FileOrderEntity)
		private fileOrderRepository: Repository<FileOrderEntity>,
		@InjectRepository(FileMetaDataEntity)
		private readonly fileMetaDataRepository: Repository<FileMetaDataEntity>,
		private fileMetaDataService: FileMetaDataService,
		private hmacService: HmacService,
		private s3BucketService: S3BucketService,
		private songService: SongEntityService,
		private userQuotaService: UserQuotaService,
		private domainEventEmitter: DomainEventEmitter,
	) {}

	/**
	 * Create a new file metadata entry after a successful upload to R2.
	 * The HMAC token must be valid, this proves the file was uploaded
	 * via the presigned URL and not tampered with.
	 */
	@Put('audio')
	async create(
		@Body() { dto, token }: { dto: CreateFileMetaDataDto; token: string },
		@JwtUser() user: CurrentJwtUser,
	) {
		this.logger.debug(`Received dto ${JSON.stringify(dto)}`);

		// token is created in S3BucketController
		const tokenIsValid = this.hmacService.verifyHmacToken(
			token,
			dto.key,
			dto.size,
		);

		if (!tokenIsValid) {
			this.logger.warn(
				`User id=${user.id} attempted to create "${dto.key}" with invalid HMAC token`,
			);

			const key = dto.key;

			try {
				await this.s3BucketService.removeObjectByKey(key);
				this.logger.log(`"${key}" was removed from bucket`);
			} catch (error) {
				this.logger.error(`Failed to remove "${key}" from bucket`);
			}

			throw new BadRequestException('Unexpected data encountered');
		}

		// Decrement user's storage quota
		const quotaDecremented = await this.userQuotaService.decrementStorage(
			user.id,
			dto.size,
		);

		if (!quotaDecremented) {
			// If quota couldn't be decremented, remove the file and throw error
			try {
				await this.s3BucketService.removeObjectByKey(dto.key);
			} catch (error) {
				this.logger.error(
					`Failed to remove "${dto.key}" from bucket after quota check failed`,
				);
			}
			throw new BadRequestException('Storage quota exceeded');
		}

		const doc = plainToInstance(FileMetaDataEntity, {
			...dto,
			category: FileCategory.Audio,
			owner: { id: user.id },
		});

		const song = await this.songService.findOne(dto.songId, user.id);

		this.logger.debug(
			`Fetch song with songId=${dto.songId} and userId=${user.id}: ${JSON.stringify(song)}`,
		);

		if (!song) {
			// Rollback quota decrement if song not found
			await this.userQuotaService.incrementStorage(user.id, dto.size);
			throw new NotFoundException(`Song externalId=${dto.songId} not found`);
		}

		doc.song = song;
		doc.owner = new UserEntity();
		doc.owner.id = user.id;

		try {
			const fileMetaData = await this.fileMetaDataService.create(doc);

			return instanceToPlain(fileMetaData);
		} catch (error) {
			// Rollback quota decrement if file metadata creation fails
			await this.userQuotaService.incrementStorage(user.id, dto.size);
			throw error;
		}
	}

	@Get()
	findAll(@JwtUser() user: CurrentJwtUser) {
		return instanceToPlain(this.fileMetaDataService.findAll(user.id));
	}

	@Get(':id')
	findOne(@Param('id') id: string, @JwtUser() user: CurrentJwtUser) {
		return this.fileMetaDataService.findOneByOwnerOrCollaborator(id, user.id);
	}

	@Get('song/:songExternalId')
	async findBySongId(
		@Param('songExternalId') songExternalId: string,
		@JwtUser() user: CurrentJwtUser,
	) {
		// TODO order these by user's order if available
		let docs = await this.fileMetaDataService.findBySongId(
			songExternalId,
			user.id,
		);

		const fileOrder = await this.fileOrderRepository.findOne({
			where: { song: { externalId: songExternalId }, userId: user.id },
		});

		if (fileOrder) {
			docs = sortSongFiles(docs, fileOrder.fileOrder);
		}

		return instanceToPlain(docs, { groups: ['list'] });
	}

	// @Patch(':id')
	// update(
	// 	@Param('id') id: string,
	// 	@Body() updateFileMetaDatumDto: UpdateFileMetaDatumDto,
	// ) {
	// return this.fileMetaDataService.update(id, updateFileMetaDatumDto);
	// }

	@Patch(':id/rename')
	async rename(
		@Param('id') id: string,
		@Body() { displayName }: RenameFileMetaDataDto,
		@JwtUser() user: CurrentJwtUser,
	) {
		const file = await this.fileMetaDataService.findOneByOwnerOrCollaborator(
			id,
			user.id,
		);

		if (!file) {
			throw new NotFoundException('File not found or permission denied');
		}

		await this.fileMetaDataRepository.update(id, { displayName });
	}

	@Delete(':id')
	async remove(@Param('id') id: string, @JwtUser() user: CurrentJwtUser) {
		const file = await this.fileMetaDataService.findOneById(id);

		// user must own the file to delete it
		// if owner is NULL then that's corrupt data, just remove
		if (
			!file ||
			(file.owner !== null && file.owner?.id !== user.id) ||
			file.owner === null
		) {
			throw new NotFoundException();
		}

		const fileOrder = await this.fileOrderRepository.findOne({
			where: { songId: file.song.id, userId: user.id },
		});

		if (fileOrder) {
			const newOrder = fileOrder.fileOrder.filter((f) => f !== file.id);

			try {
				await this.fileOrderRepository.update(fileOrder.id, {
					fileOrder: newOrder,
				});
			} catch (e) {
				console.error(
					`Failed to remove fileOrder when deleting FileMetaData id=${id}`,
				);
			}
		}

		try {
			await this.fileMetaDataService.remove(id, user.id);

			// Increment user's storage quota after successful file deletion
			await this.userQuotaService.incrementStorage(user.id, file.size);

			this.domainEventEmitter.emitEvent(new FileMetaDataRemovedEvent({ file }));
		} catch (e) {
			console.error('Failed to remove fileMetaData id=', id);
		}
	}

	@Get('get-download-url/:id')
	async getDownloadUrl(
		@Param('id') fileId: string,
		@JwtUser() user: CurrentJwtUser,
	) {
		const fileMetaDataEntity =
			await this.fileMetaDataService.findOneByOwnerOrCollaborator(
				fileId,
				user.id,
			);

		if (!fileMetaDataEntity) {
			throw new NotFoundException(`File id=${fileId} not found`);
		}

		// existing URL still valid?
		if (
			fileMetaDataEntity.downloadUrl &&
			fileMetaDataEntity.downloadUrlExpiresAt &&
			fileMetaDataEntity.downloadUrlExpiresAt > new Date()
		) {
			this.logger.debug(
				`Using existing URL, expires ${fileMetaDataEntity.downloadUrlExpiresAt}`,
			);
			return { url: fileMetaDataEntity.downloadUrl };
		}

		const downloadUrlExpiresAt = dayjs().add(86_400, 'seconds').toDate();

		const url = await this.s3BucketService.getPresignedDownloadUrl(
			fileMetaDataEntity.key,
			86_400,
		);

		const changes: Partial<FileMetaDataEntity> = {
			downloadUrl: url,
			downloadUrlExpiresAt,
		};

		this.logger.debug(`Saving file ${JSON.stringify(fileMetaDataEntity)}`);

		await this.fileMetaDataRepository.update(fileId, changes);

		return { url };
	}
}
