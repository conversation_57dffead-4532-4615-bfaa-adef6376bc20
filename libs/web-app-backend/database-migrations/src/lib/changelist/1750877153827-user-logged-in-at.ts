import { MigrationInterface, QueryRunner } from "typeorm";

export class UserLoggedInAt1750877153827 implements MigrationInterface {
    name = 'UserLoggedInAt1750877153827'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "app"."user" ADD "loggedInAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "app"."user" DROP COLUMN "loggedInAt"`);
    }

}
