import {
	<PERSON>umn,
	CreateDate<PERSON><PERSON>umn,
	<PERSON><PERSON><PERSON>,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn,
} from 'typeorm';
import { Exclude, Expose } from 'class-transformer';
import { FileCategory } from '@major/shared/models';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { columnTypeTimestamp } from '@major/web-app-backend/util-typeorm';
import { UserEntity } from '@major/web-app-backend/users/entities';

@Entity('file_meta')
export class FileMetaDataEntity {
	@Expose()
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Expose()
	@Column({ type: 'varchar', length: 256, nullable: false })
	displayName!: string;

	@Exclude({ toPlainOnly: true })
	@Column({ type: 'simple-enum', enum: FileCategory, nullable: false })
	category!: FileCategory;

	@Expose()
	ownerExternalId!: string;

	@Expose()
	ownerPhotoUrl!: string;

	@Expose()
	ownerDisplayName!: string;

	@Expose()
	@Column({ type: 'varchar', length: 128 })
	mimeType!: string;

	@Expose()
	@Column({ type: 'numeric', nullable: false, precision: 8, scale: 3 })
	durationSeconds!: number;

	@Expose()
	@Column({ type: 'int', nullable: false })
	size!: number;

	@Exclude({ toPlainOnly: true })
	@Column({ type: 'varchar', length: 256, nullable: false })
	fileName!: string;

	@Expose({ groups: ['detail'] })
	@ManyToOne(() => UserEntity)
	@JoinColumn({ name: 'ownerId' })
	owner!: UserEntity;

	@Expose({ groups: ['detail', 'list'] })
	currentUserIsOwner!: boolean;

	@Expose({ groups: ['detail'] })
	@ManyToOne(
		() => SongEntity,
		(song) => song.files,
	)
	@JoinColumn({ name: 'songId' })
	song!: SongEntity;

	@Exclude({ toPlainOnly: true })
	@Column({ type: 'text', unique: true, nullable: false })
	key!: string;

	@Expose()
	@Column({ type: 'text', nullable: true })
	downloadUrl!: string;

	@Exclude()
	@Column({ type: columnTypeTimestamp(), nullable: true })
	downloadUrlExpiresAt!: Date | null;

	@Expose()
	@Column({ type: columnTypeTimestamp(), nullable: true })
	fileCreatedAt!: Date | null;

	@Expose()
	@Column({ type: 'boolean', default: false })
	retainedAfterCollaboratorRemoval!: boolean;

	@Expose()
	@Column({ type: columnTypeTimestamp(), nullable: true })
	archivedAt!: Date | null;

	@Expose()
	@UpdateDateColumn()
	updatedAt!: Date;

	@Expose()
	@CreateDateColumn()
	createdAt!: Date;
}
