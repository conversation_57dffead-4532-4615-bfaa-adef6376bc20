import {
	Body,
	Controller,
	Headers,
	Inject,
	Logger,
	Post,
	RawBodyRequest,
	Req,
	UnauthorizedException,
} from '@nestjs/common';
import { CloudConvertEntityService } from '../services/cloud-convert-entity.service';
import { Request } from 'express';
import {
	CLOUD_CONVERT_WEBHOOK_SECRET_JOB_CREATED,
	CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FAILED,
	CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FINISHED,
} from '../types/symbols';
import { CloudConvertJobStatus } from '@major/shared/models';
import { HmacService } from 'web-app-backend/util-security';
import { Public } from '@major/web-app-backend/util-auth';
import { JobEvent } from '../types/interfaces/job-event.interface';
import { ConversionProducer } from '../queue/conversion.producer';
import { ResultProducer } from '../queue/result.producer';
import { CloudConvertEventEmitter } from 'libs/web-app-backend/cloud-convert/src/services/cloud-convert-event-emitter.service';

@Controller('cloud-convert/webhooks')
export class CloudConvertWebhookController {
	private logger = new Logger(CloudConvertWebhookController.name);

	constructor(
		@Inject(CLOUD_CONVERT_WEBHOOK_SECRET_JOB_CREATED)
		private readonly secretJobCreated: string,
		@Inject(CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FAILED)
		private readonly secretJobFailed: string,
		@Inject(CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FINISHED)
		private readonly secretJobFinished: string,
		private readonly cloudConvertEntityService: CloudConvertEntityService,
		private readonly hmacService: HmacService,
		private readonly producer: ConversionProducer,
		private readonly resultProducer: ResultProducer,
		private readonly notificationService: CloudConvertEventEmitter,
	) {}

	/**
	 * @param req
	 * @param signature
	 * @param jobEvent
	 */
	@Public()
	@Post('job-created')
	async onJobCreated(
		@Req() req: RawBodyRequest<Request>,
		@Headers('CloudConvert-Signature') signature: string,
		@Body() jobEvent: JobEvent,
	) {
		if (!signature || !req.rawBody) {
			throw new UnauthorizedException('Missing signature or body');
		}

		const isValid = this.hmacService.verifyHmacSignature(
			this.secretJobCreated,
			req.rawBody.toString('utf8'),
			signature,
		);

		if (!isValid) {
			throw new UnauthorizedException('Invalid signature');
		}

		const cloudConvertJobId = jobEvent.job.id;

		// First check if the job exists and what its current status is
		try {
			const existingJob =
				await this.cloudConvertEntityService.findOneByCCIdOrFail(
					cloudConvertJobId,
				);

			// Don't override Failed status with Created
			if (existingJob.status === CloudConvertJobStatus.Failed) {
				this.logger.debug(
					`Job ${cloudConvertJobId} is already marked as failed, not updating to created`,
				);
				return { success: true };
			}
		} catch (error) {
			// Job doesn't exist yet, proceed with creation
		}

		const cloudConvertJobEntity =
			await this.cloudConvertEntityService.updateJobStatus(
				cloudConvertJobId,
				CloudConvertJobStatus.Created,
			);

		this.logger.debug(`Notify user of job "${cloudConvertJobEntity}" created`);
		await this.notificationService.notifyJobUpdate(cloudConvertJobEntity);

		return { success: true };
	}

	/**
	 * @param request
	 * @param signature
	 * @param payload
	 */
	@Public()
	@Post('job-finished')
	async onJobFinished(
		@Req() request: RawBodyRequest<Request>,
		@Headers('CloudConvert-Signature') signature: string,
		@Body() payload: JobEvent,
	) {
		if (!signature || !request.rawBody) {
			throw new UnauthorizedException('Missing signature or body');
		}

		const isValid = this.hmacService.verifyHmacSignature(
			this.secretJobFinished,
			request.rawBody.toString('utf8'),
			signature,
		);

		if (!isValid) {
			throw new UnauthorizedException('Invalid signature');
		}

		const { job } = payload;

		const cloudConvertJobEntity =
			await this.cloudConvertEntityService.findOneByCCIdOrFail(job.id);

		await this.resultProducer.addJob(cloudConvertJobEntity, job);

		return { success: true };
	}

	/**
	 * @param request
	 * @param signature
	 * @param payload
	 */
	@Public()
	@Post('job-failed')
	async onJobFailed(
		@Req() request: RawBodyRequest<Request>,
		@Headers('CloudConvert-Signature') signature: string,
		@Body() payload: JobEvent,
	) {
		if (!signature || !request.rawBody) {
			throw new UnauthorizedException('Missing signature or body');
		}

		const isValid = this.hmacService.verifyHmacSignature(
			this.secretJobFailed,
			request.rawBody.toString('utf8'),
			signature,
		);

		if (!isValid) {
			throw new UnauthorizedException('Invalid signature');
		}

		const { job } = payload;
		const updatedJob = await this.cloudConvertEntityService.setJobFailed(
			job.id,
			payload,
		);
		await this.notificationService.notifyJobUpdate(updatedJob);

		return { success: true };
	}
}
