import {
	BadRequestException,
	Body,
	Controller,
	forwardRef,
	Get,
	HttpCode,
	Inject,
	InternalServerErrorException,
	Logger,
	NotFoundException,
	Param,
	Patch,
	Post,
	Query,
	Version,
} from '@nestjs/common';
import { SongEntityService } from '../services/song-entity.service';
import { CreateSongDto } from '../dto/create-song.dto';
import { UpdateSongDto } from '../dto/update-song.dto';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { SongCollaborationService } from '../services/song-collaboration.service';
import { FindAllSongsParams } from '../types/params/find-all-songs.params';
import { defaults, uniqBy } from 'lodash';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { CurrentJwtUser, JwtUser } from '@major/web-app-backend/util-auth';
import { OnEvent } from '@nestjs/event-emitter';
import { logColorPrimary } from 'libs/web-app-backend/util-logging/src/lib/utils/cli-colors/index';
import { CacheTTL } from '@nestjs/cache-manager';
import {
	getPlanFeatures,
	StripeSubscriptionEntityService,
} from 'web-app-backend/payments';
import { sortSongFiles } from 'libs/web-app-backend/songs/src/lib/utils/sort-song-files';
import { InviteCreationService } from 'web-app-backend/invites';
import { DomainEventEmitter } from 'domain-events';
import { SongUpdatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-updated.event';
import { UserCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/user/user-created.event';
import { SongCreatedWithInvitesEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-created-with-invites.event';
import { SongCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/song/song-created.event';

@Controller('songs')
export class SongController {
	private logger = new Logger(SongController.name);

	constructor(
		@Inject(forwardRef(() => SongCollaborationService))
		private readonly songCollaborationService: SongCollaborationService,
		private readonly songEntityService: SongEntityService,
		private stripeSubscriptionEntityService: StripeSubscriptionEntityService,
		private inviteCreationService: InviteCreationService,
		private domainEventEmitter: DomainEventEmitter,
	) {}

	// TODO move into a new user-provision library/handler
	@OnEvent(UserCreatedEvent.eventName)
	async onUserCreated(event: UserCreatedEvent) {
		const { user } = event.payload;

		this.logger.debug('Handling "user.created" event');

		// TODO refactor into ProvisionExampleContentService
		const demoSong: CreateSongDto = plainToInstance(CreateSongDto, {
			title: 'Welcome to Major!',
			lyrics: 'This a demo song',
			authors: '',
			createdAt: new Date(),
		});

		try {
			await this.songCollaborationService.create(user.id, demoSong);
			this.logger.debug(`"welcome" song created for userId=${user.id}`);
		} catch (e) {
			this.logger.error('Error creating "welcome" song', e);
		}
	}

	@OnEvent('cloudconvert.job-finished')
	async createFromEvent() {
		this.logger.warn(
			logColorPrimary('cloudconvert.job-finished event received, not handled'),
		);
	}

	@HttpCode(201)
	@Post()
	async create(
		@Body() createSongDto: CreateSongDto,
		@JwtUser() user: CurrentJwtUser,
	) {
		this.logger.debug(
			`Creating song from payload ${JSON.stringify(createSongDto, null, 2)}`,
		);

		try {
			const song = await this.songCollaborationService.create(
				user.id,
				createSongDto,
			);

			song.currentUserIsOwner = song.owner.id === user.id;

			const songDoc = await this.getOneByExternalIdV2(
				song.externalId,
				'',
				'',
				user,
			);

			const collaboratorsToAdd = (createSongDto.users ?? []).filter(
				(u) => u.email !== user.email,
			);

			if (collaboratorsToAdd.length > 0) {
				this.logger.log(
					`${user.email} is inviting collaborators + ${collaboratorsToAdd.map((c) => c.email).join(', ')}`,
				);

				await this.inviteCreationService.createMany(
					user.id,
					song,
					collaboratorsToAdd.map((c) => c.email),
				);

				this.domainEventEmitter.emitEvent(
					new SongCreatedWithInvitesEvent({
						userId: user.id,
						actorUserId: user.id,
						songExternalId: song.externalId,
						songTitle: song.title ?? '- Untitled -',
						users: collaboratorsToAdd,
					}),
				);
			} else {
				this.logger.log('Created song with no extra invitations');

				this.domainEventEmitter.emitEvent(
					new SongCreatedEvent({
						userId: user.id,
						song,
					}),
				);
			}

			return songDoc;
		} catch (error) {
			console.error(error);
			throw new InternalServerErrorException('Could not create song');
		}
	}

	@Get('recent')
	@CacheTTL(300)
	async getRecent(
		@JwtUser() user: CurrentJwtUser,
		@Query('limit') limit?: number,
	) {
		const params: FindAllSongsParams = {
			sortDirection: 'desc',
			limit: Math.min(limit ?? 25, 100),
			sortField: 'updatedAt',
			withRecordings: false,
			query: '',
		};

		const result = (await this.getAll(user, params)) as any[];
		return result.slice(0, params.limit);

		// // Get IDs from Redis
		// const songIds = await this.redisClient.zrevrange(
		// 	`user:${user.id}:recent_songs`,
		// 	0,
		// 	2,
		// );
		//
		// if (!songIds.length) {
		// 	return [];
		// }
		//
		// // Fetch full data for these IDs
		// return this.songEntityService.findByIds(songIds);
	}

	/**
	 * Will not return songs that are archived
	 *
	 * @param user
	 * @param params
	 */
	@Get()
	async getAll(
		@JwtUser() user: CurrentJwtUser,
		@Query() params: FindAllSongsParams,
	) {
		const { id } = user;

		const paramDefaults: FindAllSongsParams = {
			query: '',
			sortDirection:
				params.sortField === 'title' && !params.sortDirection ? 'asc' : 'desc',
			sortField: 'updatedAt',
			limit: 1000,
			withRecordings: false,
			collaboratorIds: [],
		};

		defaults(params, paramDefaults);

		let docs: SongEntity[];

		if (params.query !== '') {
			try {
				docs = await this.songEntityService.findAllBySearchTerm(
					params.query,
					id,
				);
				docs = uniqBy(docs, (d) => d.id);
			} catch (e) {
				throw new InternalServerErrorException('Could not search songs');
			}
		} else {
			docs = await this.songEntityService.findAllByOwner(id, params);
		}

		// TODO this will add overhead - needs optimising somehow
		for (const doc of docs) {
			// ensure each CollaborationEntity has a currentUser
			// this is used to determine if the current user is the owner of the collaboration
			// when converting the instance to a plain object
			for (const c of doc.collaborations) {
				c.currentUser = user;
			}

			doc.currentUserIsOwner = doc.owner.id === user.id;
		}

		const result = instanceToPlain(docs, {
			groups: ['list'],
			excludeExtraneousValues: true,
		});

		return result;
	}

	@Get('archived')
	async getAllRemoved(@JwtUser() { id }: CurrentJwtUser) {
		const docs = await this.songEntityService.findAllArchived(id);

		if (!docs) {
			throw new NotFoundException('No trashed songs found');
		}

		return instanceToPlain(docs, {
			groups: ['list'],
		});
	}

	/**
	 * @deprecated
	 */
	@Get(':externalId')
	async getOneByExternalId(
		@Param('externalId') externalId: string,
		@JwtUser() user: CurrentJwtUser,
	) {
		const doc = await this.songEntityService.findOne(externalId, user.id);

		if (!doc) {
			throw new NotFoundException(
				`Song externalId=${externalId} for user ${user.userId} not found`,
			);
		}

		// fetch any saved file orders for the current user
		const fileOrders = (doc.fileOrders ?? []).find(
			(fileOrder) => fileOrder.userId === user.id,
		);

		// now ensure all related files are sorted according to user's saved order
		if (fileOrders) {
			const fileIdsInOrder = fileOrders.fileOrder;
			doc.files = sortSongFiles(doc.files, fileIdsInOrder);
		}

		// ensure each CollaborationEntity has a currentUser
		// this is used to determine if the current user is the owner of the collaboration
		// when converting the instance to a plain object
		// TODO Write a TypeORM custom transformer to perform this
		for (const c of doc.collaborations) {
			c.currentUser = user;
		}

		doc.currentUserIsOwner = doc.owner.id === user.id;

		doc.ownerName = doc.owner.displayName ?? doc.owner.email;
		doc.ownerPhotoUrl = doc.owner.photoUrl ?? '';

		const ownerPlanName =
			await this.stripeSubscriptionEntityService.getCurrentPlanName(
				doc.owner.id,
			);
		const { maxCollaborators, storagePerSong } = getPlanFeatures(ownerPlanName);

		doc.maxCollaborators = maxCollaborators;
		doc.storagePerSong = storagePerSong;

		return instanceToPlain(doc, {
			groups: ['detail'],
		});
	}

	@Get(':externalId')
	@Version('2')
	async getOneByExternalIdV2(
		@Param('externalId') externalId: string,
		@Query('expand') expandQuery: string, // e.g., "owner,files"
		@Query('include') includeQuery: string, // e.g., "permissions,archiveStatus"
		@JwtUser() user: CurrentJwtUser,
	) {
		const expand = expandQuery ? expandQuery.split(',') : [];
		const include = includeQuery ? includeQuery.split(',') : [];

		const doc = await this.songEntityService.findOneNew(
			externalId,
			user,
			expand,
			include,
		);

		if (!doc) {
			throw new NotFoundException(
				`Song externalId=${externalId} for user ${user.userId} not found`,
			);
		}

		return instanceToPlain(doc, {
			groups: ['detail'],
			excludeExtraneousValues: true,
		});
	}

	@Patch(':externalId')
	async update(
		@Param('externalId') externalId: string,
		@Body() updateSongDto: UpdateSongDto,
		@JwtUser() user: CurrentJwtUser,
	) {
		if (!externalId || externalId === 'undefined') {
			console.log('Song externalId was empty', updateSongDto, user);
			throw new BadRequestException('Song externalId was empty');
		}

		const oldTitle =
			await this.songEntityService.getTitleByExternalId(externalId);

		// this will perform necessary permission checks before doing update
		const doc = await this.songEntityService.update(
			externalId,
			user.id,
			updateSongDto,
		);

		if (!doc) {
			throw new BadRequestException(
				`Update song failed externalId=${externalId} ${JSON.stringify(updateSongDto)}`,
			);
		}

		const event = new SongUpdatedEvent({
			externalId,
			oldTitle,
			updatedSong: doc,
			userId: user.id,
			updateSongDto,
			changedFields: [],
		});

		this.domainEventEmitter.emitEvent(event);

		// sometimes we will want the 'list' groups to be used
		// as the transformation basis
		return instanceToPlain(doc, {
			groups: ['detail'],
		});
	}

	@Patch('restore/:id')
	async restoreOne(
		@Param('id') externalId: string,
		@JwtUser() user: CurrentJwtUser,
	) {
		const result = await this.songEntityService.restoreOne(externalId, user.id);

		if (result.affected !== 1) {
			throw new InternalServerErrorException('Could not restore one song');
		}

		const doc = this.songEntityService.findOne(externalId, user.id);
		return instanceToPlain(doc, {
			groups: ['detail'],
		});
	}

	@Patch('restore')
	async restoreMany(
		@Body('id') externalIds: string[],
		@JwtUser() user: CurrentJwtUser,
	) {
		const result = await this.songEntityService.restoreMany(
			externalIds,
			user.id,
		);
		return result.affected === 1;
	}
}
