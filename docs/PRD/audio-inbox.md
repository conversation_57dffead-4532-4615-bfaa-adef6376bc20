# PRD: Audio Inbox

* **Author:** Gemini Engineering
* **Status:** In Review
* **Date:** 8 August 2025
* **Version:** 1.2

---

## 1. Overview

This PRD contains a lot of technical implementation details. Be sure that they are all covered when creating tasks. Create sub-tasks if you need.

### 1.1. The Problem
Modern songwriters, particularly younger artists, increasingly use their phones' voice note apps to capture fleeting ideas. This results in hundreds of unorganised audio files (`voicememo-238.m4a`, `new riff FINAL.mp3`, etc.) with little context. Our current songwriting app is tailored to the Nashville style (lyrics + melody in-app) and doesn't cater to this "ideas-first" workflow, creating a barrier to entry and utility for a large user segment.

### 1.2. The Solution: Audio Inbox
The **Audio Inbox** will be a new, dedicated feature area within the application that allows users to bulk-upload their raw audio recordings. It will provide intelligent tools to automatically group, manually organise, triage, and annotate these ideas. The ultimate goal is to bridge the gap between a chaotic folder of voice notes and a structured, in-progress song, making our app an indispensable tool from the very first spark of inspiration.

User Stories: Audio Inbox

This document outlines the user stories for the Audio Inbox feature, broken down by user persona and the core value they need to get from the application.
Persona 1: The Solo Songwriter (Core User)

This user's primary goal is to capture, organize, and develop their own ideas efficiently.
Uploading & Initial Organisation

    As a solo songwriter, I want to bulk-upload all the audio files from my phone in one action, so that I can get all my raw ideas into the app quickly without manual, one-by-one uploads.

    As a solo songwriter, I want the app to automatically group my uploaded files by their creation time, so that recordings from the same writing session are instantly clustered together for me.

    As a solo songwriter, I want to see a persistent, non-blocking upload manager while my files are being sent, so that I can continue using the app and trust that the upload is progressing without being stuck on a loading screen.

    As a solo songwriter, I want to see individual file progress and receive clear feedback if a specific file fails to upload, so that I can retry failed files without the entire batch being cancelled.

Annotation & Triaging Ideas

    As a solo songwriter, I want to play any audio file directly from its card, so that I can quickly audition my ideas.

    As a solo songwriter, I want to assign a 1-5 star rating to each audio file, so that I can visually identify my strongest ideas at a glance.

    As a solo songwriter, I want to add searchable tags (like #chorus, #riff) to my audio files, so that I can categorise and find specific types of ideas later.

    As a solo songwriter, I want to write short, private notes on each audio file, so that I can capture important context like tuning, tempo, or lyrical snippets.

Organisation at Scale

    As a solo songwriter with hundreds of ideas, I want to switch to a compact list view, so that I can scan through many idea clusters quickly.

    As a solo songwriter in list view, I want to expand and collapse a cluster, so that I can inspect its contents without leaving the main list and losing my place.

    As a solo songwriter, I want to use a sidebar to search my entire inbox by keywords, tags, or star rating, so that I can find a specific idea no matter how old it is.

    As a solo songwriter, I want to pin my most important or active idea clusters, so that they always appear at the top of my list for easy access.

    As a solo songwriter, I want to drag and drop an audio file from one cluster to another (or to a new cluster), so that I can manually reorganise my ideas as they evolve.

Idea Promotion & Lifecycle

    As a solo songwriter, I want to select the best files from a cluster and click a "Create Song" button, so that I can formally promote my curated ideas into a new song project within the app.

    As a solo songwriter, I want to move old or irrelevant clusters to an "Archive", so that I can declutter my main workspace without permanently deleting them.

    As a solo songwriter approaching my storage limit, I want to move clusters to a "Trash" that auto-deletes after 30 days, so that I can safely free up space.

Persona 2: The Collaborator (Bandmate / Co-writer)

This user's goal is to effectively give and receive feedback on shared ideas.
Sharing & Inviting

    As a cluster owner, I want to invite another user to my idea cluster by their email address, so that I can get their feedback on my initial ideas.

    As a cluster owner, I want to assign either "Editor" or "Viewer" permissions to collaborators, so that I can control who can modify the ideas and who can only listen.

    As a cluster owner, I want to see who has access to my cluster and be able to change their permissions or remove them at any time, so that I remain in control of my intellectual property.

Receiving & Contributing to Shared Ideas

    As a collaborator, I want to see a dedicated "Shared with me" section in my sidebar, so that I can easily find all the ideas others have sent me in one place.

    As a collaborator with "Viewer" permissions, I want to be able to listen to all audio files and see existing notes and tags, so that I can understand the idea without being able to change it.

    As a collaborator with "Editor" permissions, I want to be able to add my own ratings, tags, and notes to the audio files, so that I can contribute my feedback directly to the project.

    As a collaborator, I want to clearly see who the owner of a shared cluster is and who else is collaborating on it, so that I have full context of the creative team.---

## 2. Goals & Success Metrics

### 2.1. Goals
* **User Acquisition:** Attract and retain songwriters who use a voice-note-centric workflow.
* **User Engagement:** Increase the frequency and duration of user sessions by making the app useful at the earliest stage of the creative process.
* **Collaboration:** Foster in-app collaboration between songwriters at the idea stage, leveraging network effects.
* **Product Stickiness:** Become the central hub for a song's entire lifecycle, from raw idea to finished lyric sheet.
* **Monetisation:** Create a clear value proposition for paid tiers based on storage limits.

### 2.2. Success Metrics
* **Adoption Rate:** % of new and existing users who upload at least one file to the Audio Inbox within 30 days of launch.
* **Conversion Rate:** % of "Idea Clusters" that are successfully converted into a formal "Song" project.
* **Collaboration Rate:** % of active clusters shared with at least one collaborator.
* **Engagement:** Average number of annotations (ratings, tags, notes) per user per week.
* **Storage Utilisation:** Average storage used per free-tier user. Track upgrades from users hitting their storage limit.

---

## 3. Scope & Phasing

This feature will be delivered in three distinct phases to allow for iterative development and feedback.

### Phase 1: MVP - The Core Single-User Workflow
*Goal: Enable a user to upload files, see them clustered, annotate them, and create a song.*
- [ ] **UI:** Non-blocking upload manager for queuing files.
- [ ] **Backend:** Automatic grouping of uploaded `FileMetaData` into `IdeaCluster`s based on creation timestamps.
- [ ] **UI:** A simple grid view to display the `IdeaCluster`s.
- [ ] **UI & Backend:** Functionality to rate, tag, and add notes to individual recordings.
- [ ] **UI & Backend:** A "Create Song" flow that promotes selected files from a cluster to a new Song entity.

### Phase 2: Collaboration & Scalability
*Goal: Introduce sharing and ensure the feature remains usable as users accumulate hundreds of clusters.*
- [ ] **Backend & UI:** Implement in-app sharing of clusters with other registered users, with 'Editor' and 'Viewer' permissions.
- [ ] **UI:** A dedicated "Shared with me" filter in the sidebar.
- [ ] **UI:** A `ShareModalComponent` for managing collaborators on a cluster.
- [ ] **UI:** Visual indicators (avatars) on clusters to show they are shared.
- [ ] **UI:** A dedicated filter sidebar for searching by text, tags, and rating.
- [ ] **UI:** Advanced sorting options (by date, rating, etc.).
- [ ] **UI:** A compact "List View" with expand/collapse functionality.
- [ ] **UI & Backend:** Ability to "Pin" important clusters to the top.
- [ ] **Backend:** A simple in-app notification when a cluster is shared with a user.

### Phase 3: Storage Lifecycle & Monetisation
*Goal: Provide users with tools to manage their storage limits and handle the full lifecycle of an idea.*
- [ ] **UI & Backend:** A persistent storage meter, visible in the app layout.
- [ ] **UI & Backend:** An "Archive" state for clusters to hide them from the main view.
- [ ] **UI & Backend:** A two-stage deletion process: "Move to Trash".
- [ ] **Backend:** An automated 30-day purge for items in the Trash.
- [ ] **UI:** A dedicated Storage Management view for restoring or permanently deleting trashed items.

---

## 4. Technical Implementation Plan

**Tech Stack:**
* **Workspace:** Nx
* **Frontend:** Angular, DaisyUI v4, Tailwind v3, Signals
* **Backend:** NestJS, TypeORM, Postgres
* **Storage:** Cloudflare R2 (pre-configured)

### 4.0. Architectural Decisions & Conventions (v1.2)

- External IDs
  - All API ids are externalId UUIDs; internal PKs are not exposed. Controllers resolve externalId → PK and use JwtUser().
  - IdeaCluster includes `externalId` (UUID, unique) and the API maps this as `id` to the UI.

- Entity model
  - Reuse `FileMetaData` for inbox: add nullable `clusterId`; make `songId` nullable. DB CHECK ensures not both are set. Inbox ingestion may set neither until grouping assigns.
  - `IdeaCluster`: `id` (PK), `externalId` (UUID unique), `name`, `pinned` (default false), `status` ('active'|'archived'|'trashed'), `trashedAt?`, `createdAt`, `updatedAt`.
  - `IdeaClusterCollaborator`: `permission` ('editor'|'viewer'), `UNIQUE(clusterId,userId)`, optional `status: 'pending'` for invited non-users.

- Automatic grouping
  - Session window 90 minutes based on `fileCreatedAt` (fallback: upload time), compare/store in UTC.
  - De-dupe identical `key`, or same (`fileName`,`size`) within 5 minutes.

- Tags & search
  - `tags` column is Postgres `text[]` with GIN index. Notes full‑text can come later; MVP filters by tags/minRating and simple text.
  - Query params: `q`, `tags[]`, `minRating`, `pinned`, `view=my|shared|archived|trashed`, `sort=(createdAt|updatedAt|rating):(asc|desc)`, `page`, `limit`.
  - Defaults: grid 24/page; list 100/page; server‑driven pagination.

- Frontend layering (Nx + Angular Query)
  - Routable UI lib: `libs/web-app/feature-audio-inbox`.
  - Domain libs: `libs/web-app/audio-inbox/domain/data` (HTTP only) and `libs/web-app/audio-inbox/domain/query` (@tanstack/angular-query).
  - Use Signals for view state; keep server state in Angular Query. Use MatDialog as the dialog shell for ShareModal with DaisyUI content.

- Upload details
  - Max concurrency 1 (configurable). Duration via HTMLAudioElement or `music-metadata-browser`.
  - Retry with exponential backoff (up to 3). Surface failures via `NotifyService.info()`.

- Purge & lifecycle
  - Per‑file trash purge after 30 days via a daily BullMQ cron worker; delete R2 objects and reverse quotas. Remove clusters when empty.

- DB indices & constraints
  - IdeaCluster: indexes on (ownerId), (status), (pinned), (trashedAt), (createdAt); unique(externalId).
  - FileMetaData: indexes on (clusterId), (rating), GIN(tags), (trashedAt), (createdAt); CHECK rating 0–5; CHECK NOT (songId IS NOT NULL AND clusterId IS NOT NULL).
  - Collaborator: UNIQUE(clusterId,userId); index on (userId) for "Shared with me".

- Sharing & invites
  - Reuse the existing invites flow. Owner can add/remove/update; editors annotate; viewers read‑only. Non‑users become `pending` until they register.

- Nx workspace & library generation (required)
  - Frontend Nx libraries live in `libs/web-app/`. Generate with: `bunx nx g lib libs/web-app/FEATURE-NAME/library-name` and select `@nx/angular` as the project type with Jest (tests) and ESLint (linter). For routable feature libs, prefer `--routing --lazy --standalone` and wire into `apps/web-app/src/app/config/web-app.routes.ts`.
  - Backend Nx libraries live in `libs/web-app-backend/` and are NestJS projects. Generate with: `bunx nx g lib libs/web-app-backend/MODEL-NAMESPACE/library-name` and select the NestJS project type (`@nx/nest`) with Jest and ESLint. Backend libraries are namespaced by model type.
  - Frontend domain libs: create a dedicated `domain` library per feature to house services and feature-specific types. For this feature, keep HTTP-only data services in `libs/web-app/audio-inbox/domain/data` and Angular Query code in `libs/web-app/audio-inbox/domain/query`.
  - Backend entities: place entity classes under `types/entities` within their respective backend library. Provide a corresponding UI-facing interface in the existing shared models library at `libs/shared/models/src/lib/interfaces` (no new shared library needed).
  - Always use the Nx CLI (via `bunx nx ...`) and do not use the Angular `ng` executable in this workspace.

  - Shared models import aliases (tsconfig.base.json):
    ```json
    {
      "@major/shared/models": ["libs/shared/models/src/index.ts"],
      "@major/shared/models/classes": ["libs/shared/models/src/lib/classes/index.ts"],
      "@major/shared/models/enums": ["libs/shared/models/src/lib/enum/index.ts"],
      "@major/shared/models/interfaces": ["libs/shared/models/src/lib/interfaces/index.ts"],
      "@major/shared/models/literals": ["libs/shared/models/src/lib/literals/index.ts"]
    }
    ```
    Prefer these aliases for all shared types.

  - Backend namespacing for Audio Inbox:
    - Use a single backend library for clusters domain logic: `libs/web-app-backend/idea-clusters`.
    - This library owns CRUD/services/controllers for clusters and collaborators and the grouping job.
    - Storage remains separated and reusable via existing modules:
      - `libs/web-app-backend/storage/src/file-meta-data/file-meta-data.module.ts`
      - `libs/web-app-backend/storage/src/lib/web-app-backend-storage.module.ts`
    - Do not tightly couple cluster logic to storage or file-meta; depend on their public modules/services only.

  - Frontend naming & routing for Audio Inbox:
    - Domain library (services and types): `libs/web-app/audio-inbox/domain`
      - Place HTTP data services and Angular Query services here (no services in the feature lib).
    - Feature library (routable UI): `libs/web-app/audio-inbox/feature-audio-inbox`
      - Container/presentational components for the inbox live here.
      - Route path: `/inbox` (feature-flag gated) wired in `apps/web-app/src/app/config/web-app.routes.ts`.

  - Example generation commands:
    - Frontend domain lib:
      - `bunx nx g lib libs/web-app/audio-inbox/domain`
      - Select `@nx/angular`, Jest, ESLint
    - Frontend feature lib (routable, standalone):
      - `bunx nx g lib libs/web-app/audio-inbox/feature-audio-inbox --routing --lazy --standalone`
      - Select `@nx/angular`, Jest, ESLint
    - Backend clusters lib (NestJS):
      - `bunx nx g lib libs/web-app-backend/idea-clusters`
      - Select NestJS project type (`@nx/nest`), Jest, ESLint

  - Testing defaults:
    - Use Jest for all new libraries.
    - Backend tests should use `createTestApp()` to bootstrap a realistic Nest app with testcontainers, and `createMock()` from `@golevelup/ts-jest`.
    - Prefer `firstValueFrom`/`lastValueFrom` over `toPromise` when bridging Observables.

### 4.1. Backend Implementation (`libs/web-app-backend/`)

#### 4.1.1. Data Models (TypeORM Entities)
* **File (`FileMetaData` Entity - Modification):**
  * - [ ] Add `rating: number` column (nullable, integer, 0-5).
  * - [ ] Add `tags: text[]` column (nullable) with a GIN index for search.
  * - [ ] Add `notes: string` column (type `text`, nullable).
  * - [ ] `size` column already exists (`int`, non-nullable) – **no change required**.
  * - [ ] Use existing `archivedAt: Date` column for the "Archive" state (**no separate `status` column required**).
  * - [ ] Add `trashedAt: Date` column (nullable) for the "Move to Trash" flow.
  * - [ ] Add `cluster: ManyToOne -> IdeaCluster` relationship.

* **Cluster (`IdeaCluster` Entity - New):**
  * - [ ] Create a new `IdeaCluster` entity.
  * - [ ] `id: uuid` (Primary Key).
  * - [ ] `externalId: uuid` (Unique, NOT NULL). API maps this to `id` in responses.
  * - [ ] `name: string`.
  * - [ ] `pinned: boolean` (default `false`).
  * - [ ] `status: string` (e.g., 'active', 'archived', 'trashed', default 'active').
  * - [ ] `trashedAt: Date` (nullable).
  * - [ ] **Relationships:**
    * - [ ] `owner: ManyToOne -> User`. The user who created the cluster.
    * - [ ] `files: OneToMany -> FileMetaData`.
    * - [ ] `collaborators: OneToMany -> IdeaClusterCollaborator`.

* **Collaborator (`IdeaClusterCollaborator` Entity - New):** A join table for sharing.
  * - [ ] `id: uuid` (Primary Key).
  * - [ ] `permission: string` ('editor' or 'viewer').
  * - [ ] **Relationships:**
    * - [ ] `cluster: ManyToOne -> IdeaCluster`.
    * - [ ] `user: ManyToOne -> User`. The user being shared with.

* **Database Migration:**
  * - [ ] Generate a new TypeORM migration to add/modify columns and create the `idea_cluster` and `idea_cluster_collaborator` tables.
  * - [ ] Add a Postgres CHECK constraint on `file_meta_data`: `CHECK (NOT (song_id IS NOT NULL AND cluster_id IS NOT NULL))` so a file can belong to a Song OR a Cluster, but not both. Both may be `NULL` at initial Inbox ingestion.
  * - [ ] Create a GIN index for tags (raw SQL): `CREATE INDEX IF NOT EXISTS idx_file_metadata_tags_gin ON app.file_meta_data USING GIN (tags);`
  * - [ ] Add/adjust indexes per 4.0 (e.g., `file_meta_data(cluster_id)`, `file_meta_data(rating)`, `file_meta_data(trashed_at)`, `file_meta_data(created_at)`; `idea_cluster(owner_id)`, `idea_cluster(status)`, `idea_cluster(pinned)`, `idea_cluster(trashed_at)`, `idea_cluster(created_at)`, and `UNIQUE(external_id)`).
  * - [ ] Ensure `idea_cluster_collaborator` has `UNIQUE(cluster_id, user_id)` and an index on `(user_id)` for "Shared with me".

##### Postgres CHECK constraints (short explainer)

Postgres CHECK constraints are simple row-level rules enforced by the database on every INSERT and UPDATE. If the boolean expression evaluates to false, the statement fails, preventing invalid data from being stored.

In our case, we add:

```sql
ALTER TABLE app.file_meta_data
  ADD CONSTRAINT file_meta_data_song_or_cluster_chk
  CHECK (NOT (song_id IS NOT NULL AND cluster_id IS NOT NULL));
```

This guarantees a file is linked to either a Song or an Idea Cluster, but not both. For Inbox ingestion, both `song_id` and `cluster_id` may be NULL; the grouping job assigns `cluster_id` later. We will create this constraint in the TypeORM migration (either via `queryRunner.query(...)` SQL or a `TableCheck`).

#### 4.1.2. Modules & Logic
* **`FileMetaDataModule`:** (Existing)
  * - [ ] No major changes required beyond entity updates.

* **`IdeaClustersModule` (New/Updated):**
  * - [ ] **Service:**
    * - [ ] `createFromFiles(ownerId, fileIds)`: Core logic. Fetches files, creates clusters, sets `ownerId`.
    * - [ ] `findAllForUser(userId, filters)`: **CRITICAL UPDATE:** This service must now fetch clusters where the user is the `owner` OR where the user exists in the `collaborators` join table. A filter param like `view: 'shared'` will be used to toggle the "Shared with me" view.
    * - [ ] `update(id, dto, userId)`: Logic to rename/pin. Must verify user has `owner` or `editor` permission.
    * - [ ] `updateStatus(ids, status, userId)`: Logic for archive/trash. Must verify user is the `owner`.
    * - [ ] **Sharing Service Logic:**
      * - [ ] `addCollaborator(clusterId, inviterId, inviteeEmail, permission)`: Finds user by email. Creates an `IdeaClusterCollaborator` record. Verifies `inviterId` is the owner.
      * - [ ] `updateCollaboratorPermission(clusterId, ownerId, collaboratorUserId, permission)`.
      * - [ ] `removeCollaborator(clusterId, ownerId, collaboratorUserId)`.
  * - [ ] **Controller:**
    * - [ ] `GET /clusters`: Primary endpoint, now with a `view` query param.
    * - [ ] `POST /clusters/from-files`: Endpoint triggered after uploads.
    * - [ ] `PATCH /clusters/:id`: Endpoint to update cluster metadata.
    * - [ ] **Sharing Controller Endpoints:**
      * - [ ] `POST /clusters/:id/collaborators`: Invite a user.
      * - [ ] `PATCH /clusters/:id/collaborators/:userId`: Update a user's permission.
      * - [ ] `DELETE /clusters/:id/collaborators/:userId`: Remove a user.

#### 4.1.3. End-to-End **Audio** File Upload Workflows

Common steps (both paths)

1. **Request Presigned URL**  
   **Endpoint:** `POST /storage/get-upload-url/audio` (handled by `S3BucketController.getAudioUploadUrl()`).

   | Body Field | Type     | Notes                                                   |
   |------------|----------|---------------------------------------------------------|
   | `fileName` | `string` | The original client file name (used for key).           |
   | `fileSize` | `number` | Bytes; must be `> 0` and `< 12 MiB` (12 * 1024 * 1024). |
   | `fileType` | `string` | MIME‐type, e.g. `audio/m4a`.                            |

   **Server-side validation:**  
   • Rejects zero-byte files (`BadRequestException`).  
   • Rejects > 12 MiB (`BadRequestException`).  
   • Calls `UserQuotaService.validateStorage(userId, fileSize)`; on failure → `BadRequestException "Storage quota exceeded"`.

   **Server-side response:** `{ url, key, token }`  
   • `url` – 7-day presigned **PUT** URL to Cloudflare R2 (generated via `S3BucketService.getPresignedUploadUrl`).  
   • `key` – `<userExternalId>/audio/<epochMs>--<fileName>` (created by `S3BucketService.createNewObjectKey`).  
   • `token` – HMAC-SHA256 of `key||fileSize` (32 hex chars) generated by `HmacService.createHmacToken()` and signed with `HMAC_SECRET`.

2. **Client Direct Upload to R2**  
   The Angular `RecordingUploadQueueService.upload()` performs a `PUT` using the presigned `url`. Progress is emitted via an RxJS stream allowing UI progress bars. Concurrency is **1** (configurable) to limit bandwidth.

Path A — Song‑bound upload (existing)

3A. **Create Secure `FileMetaData` Entry (Song)**  
   **Endpoint:** `PUT /file-meta-data/audio` (handled by `FileMetaDataController.create()`).  
   **Payload:** `{ dto: CreateFileMetaDataDto, token }` where `CreateFileMetaDataDto` includes:

   | Field             | Type     | Source                                             |
   |-------------------|----------|----------------------------------------------------|
   | `displayName`     | `string` | Derived from UI (unique within queue).             |
   | `songId`          | `uuid`   | ExternalId of the owning song.                     |
   | `fileName`        | `string` | Original file name.                                |
   | `key`             | `string` | Same key returned earlier.                         |
   | `size`            | `number` | Bytes.                                             |
   | `mimeType`        | `string` | MIME‐type.                                         |
   | `durationSeconds` | `number` | Calculated client-side via `wavesurfer.js` helper. |
   | `fileCreatedAt`   | `Date`   | Original capture timestamp (if available).         |

   **Controller Internals:**
   • Verifies `token` via `HmacService.verifyHmacToken(token, key, size)` – guarantees key/size not tampered with.  
   • On failure → removes object via `S3BucketService.removeObjectByKey(key)` and returns `400`.

   • Decrements user quota: `UserQuotaService.decrementStorage(userId, size)`. On quota error → removes object & returns `400`.

   • Resolves owning `Song` via `SongEntityService.findOne(songId, userId)`; rolls back quota decrement if not found.

   • Persists entity via `FileMetaDataService.create()` and emits `FileMetaDataCreatedEvent` (domain event for real-time updates).

4. **Download URL Caching (Future Playback)**  
   When playback is requested the client calls `GET /file-meta-data/get-download-url/:id`. The controller reuses the cached URL if still valid, or generates a fresh presigned **GET** (24 h expiry) and stores it back on the entity (`downloadUrl` + `downloadUrlExpiresAt`).

5. **Deletion & Quota Reversal**  
   `DELETE /file-meta-data/:id` removes the DB row, deletes the object from R2 (service layer), and **increments** the user’s quota by the file’s `size`.

Path B — Inbox upload (new; no `songId`)

3B. **Create Secure `FileMetaData` Entry (Inbox)**  
   **Endpoint:** `PUT /inbox/files` (handled by `InboxFilesController.create()`).  
   **Payload:** `{ dto: CreateInboxFileDto, token }` where `CreateInboxFileDto` includes the same fields as above EXCEPT `songId` (omit it):

   | Field             | Type     | Source                                             |
   |-------------------|----------|----------------------------------------------------|
   | `displayName`     | `string` | Derived from UI (unique within queue).             |
   | `fileName`        | `string` | Original file name.                                |
   | `key`             | `string` | Same key returned earlier.                         |
   | `size`            | `number` | Bytes.                                             |
   | `mimeType`        | `string` | MIME‐type.                                         |
   | `durationSeconds` | `number` | Calculated client-side via `wavesurfer.js` helper. |
   | `fileCreatedAt`   | `Date`   | Original capture timestamp (if available).         |

   **Controller Internals:**
   • Verifies `token` via `HmacService.verifyHmacToken(token, key, size)` and decrements storage via `UserQuotaService.decrementStorage(userId, size)` (same as Path A).  
   • Persists `FileMetaData` with `songId = NULL` and `clusterId = NULL` (valid due to the CHECK constraint).  
   • Emits `FileMetaDataCreatedEvent`.

   **Grouping into clusters:** A background service (BullMQ) applies the 90‑minute session window logic to assign `clusterId` based on `fileCreatedAt` (fallback: upload time). It may run on ingestion or as a periodic job.

4. **Download URL Caching & Deletion**  
   Download URL caching and deletion/quota reversal behavior are identical to Path A; when a file later gets promoted to a Song, the API clears `clusterId` and sets `songId` (the CHECK constraint guarantees both aren’t set simultaneously).

### 4.2. Frontend Implementation (`libs/web-app/`)

IMPORTANT: You MUST use DaisyUI components instead of applying tailwind classes directly. Here is a link to the components docs:
https://v4.daisyui.com/components/

Before creating an Angular component you should analyse to understand what elements the Angular component will contain, and which DaisyUI CSS components can be used to build this.

#### 4.2.1. Library & State Management
* - [ ] Create a new routable feature library in Nx (under `libs/web-app/`):  
  `nx g @nx/angular:lib feature-audio-inbox --directory=web-app --routing --lazy --standalone --parentModule=apps/web-app/src/app/config/web-app.routes.ts`.
* - [ ] Use **Angular Signals** for component state.
* - [ ] Create a singleton `UploadService` provided in 'root'.
* - [ ] Use **RxJS** `debounceTime` for the search input.
- - [ ] Use Angular CDK for the drag and drop. This package is installed already.

#### 4.2.2. Component Breakdown (Phase by Phase)

**Phase 1: MVP**
* - [ ] **`UploadManagerComponent`**: Manages upload queue presentation.
* - [ ] **`AudioInboxComponent` (Smart Component):** Fetches data, manages view state (`my-ideas` vs `shared-with-me`).
* - [ ] **`ClusterGridItemComponent` (Presentational):** Displays a cluster card.
* - [ ] **`AudioFileCardComponent` (Presentational):** Displays an audio file card.

**Phase 2: Collaboration & Scalability**
* - [ ] **`FilterSidebarComponent`**:
  * - [ ] Add "My Ideas" and "Shared with me" as primary filter options.
* - [ ] **`ClusterListItemComponent` (Presentational):**
  * - [ ] Add collaborator avatars display.
  * - [ ] Add a `Share` button.
* - [ ] **`ShareModalComponent` (Smart/Presentational Hybrid):**
  * **Responsibility:** Manages adding, removing, and updating collaborators for a specific cluster.
  * **Logic:** Takes a `clusterId` as input. Fetches current collaborators. Provides form to invite new ones. Dispatches update/remove actions.
* - [ ] **Permission-based UI Logic:**
  * - [ ] Update `ClusterGridItemComponent`, `ClusterListItemComponent`, `AudioFileCardComponent` to accept a `permission: 'owner' | 'editor' | 'viewer'` input.
  * - [ ] Use this input to conditionally disable or hide controls like editing notes, changing ratings, or accessing the `...` menu for archiving/deleting.

**Phase 3: Storage**
* - [ ] **`StorageMeterComponent`**: Displays storage usage.
* - [ ] **`StorageManagementComponent`**: Manages archived and trashed items.

#### 4.2.3. Audio Upload Flow (Frontend)

1. **Queue Intake**  
   • `FeatureRecorderUploadService.handleFiles()` and `handleRecorderOutput()` create `NewRecording` objects via helper utilities then push them into `RecordingUploadQueueService` (singleton, provided in `root`).  
   • New recordings are stored in a **Signal** `newRecordings` for reactive UI lists.

2. **User Initiates Upload** (`UploadManagerComponent` ➜ `uploadPendingFiles(songId)`)  
   • Calls `RecordingUploadQueueService.uploadFiles(songId)`.

3. **Uploading Each File (inside `uploadFiles`)**
   a. Convert `Blob` → `File`.  
   b. **Step 1** backend call `getAudioUploadUrl(file)` via `FileMetaDataApiService`, obtaining `{ url, key, token }`.  
   c. **Step 2** perform direct `PUT` to `url`; progress propagated to `uploadProgress` **Signal** (0-100). A progress value of **101** marks completion (stream sentinel).

4. **Create Metadata**  
   • On upload completion, constructs `CreateFileMetaDataDto` (see table above) and calls `createFileMetaDataSecure(dto, token)` which maps to `PUT /file-meta-data/audio`.

   • On success, the newly created `FileMetaData` is locally augmented with a `downloadUrl` generated via `blobToDataURL()` so the user can instantly preview the recording without waiting for R2 propagation.

5. **Concurrent Control & UI Feedback**  
   • Maximum concurrent uploads: **1** (constant `maxConcurrentUploads`).  
   • Signals:
     – `uploadProgress`: number (0-100, 101 sentinel).  
     – `uploadingFileName`: string\|null – name of the file currently uploading.  
     – Components subscribe to display progress bars / spinners.

6. **Error Handling**  
   • Any network/API/upload error logs to console; failed files remain in `uploadQueue` allowing the user to retry.  
   • A higher-level toast via `NotifyService.info()` (TODO) will inform the user.

---

## 5. Edge Cases & Failure Handling

* **Network Loss During Upload:** Retry mechanism in UI. Idempotent backend processing.
* **Page Refresh During Upload:** MVP loses queue. Browser prompt `beforeunload` to warn user.
* **Empty States:** Well-designed empty states for "My Ideas" (prompt to upload), "Shared with me" (prompt explaining what it is), and "Trash".
* **API Errors:** Global HTTP interceptor for user-friendly toast notifications.
* **Permissions & Access:**
  * - **Revoked Access:** If a user's permission is revoked while they are viewing a cluster, a real-time event (e.g., via WebSocket) should trigger a UI update, locking the view and showing a "Your access has been removed" message. For MVP, this will be handled on the next API call or page refresh.
  * - **Inviting Non-Users:** The backend should handle invites to non-existent emails by sending a sign-up invitation. The collaboration record would be in a 'pending' state until the user registers.
  * - **Owner Account Deletion:** A cascading delete strategy must be defined. When an owner deletes their account, all their clusters and associated files should be permanently deleted. Collaborators must be notified.

---

## 6. Work already completed
This is a record of the work completed so far according to a plan generated from everything above (in this file) in Windsurf.
Pay careful attention to the notes, they are important. You should use these to evaluate how much work has been already complete on a task.


### Audio Inbox Implementation Plan

#### Notes
- PRD finalized and fully up to date (v1.3, 9 Aug 2025): all recent backend/DTO/event payload/TypeORM/cluster deletion/Nx boundary rules are now represented in the PRD. See PRD for details of shared event DTOs, null-safe invite handling, string-based TypeORM relations, and cluster deletion semantics.
- Nx workspace: backend in NestJS (TypeORM, BullMQ, PostgreSQL), frontend in Angular with DaisyUI/Tailwind, Angular Query, signals preferred, strict separation of UI/backend code, shared types in shared-models lib.
- All new features/modules must use Nx CLI to generate libraries in the correct location.
- Backend database migration and entities for Audio Inbox and Idea Clusters have been generated, updated, and applied.
- Migration 1754673005054 for Audio Inbox/Idea Clusters was successfully applied after fixing SQL quoting issues (uuid_generate_v4(), CURRENT_TIMESTAMP). New tables: app.idea_cluster, app.idea_cluster_collaborator. Updated app.file_meta with notes, rating, tags (GIN index), trashedAt, clusterId, and added CHECK constraints.
- When an idea cluster is deleted, files referencing it should become 'loose' (file_meta.clusterId set to NULL). onDelete: 'SET NULL' is now used in the relation and a migration will be generated to update the FK.
- NotifyService.info() is for UI toasts only; all API ids are external UUIDs.
- Confirmed: Deleting a cluster should immediately hard-delete it (no trash), and files referencing it must remain (clusterId set to NULL).
- Backend IdeaClusters module, service, and DELETE /clusters/:id endpoint for immediate delete (files remain) are implemented and integrated into AppModule.
- Shared models import aliases (tsconfig.base.json):
  - @major/shared/models: ["libs/shared/models/src/index.ts"]
  - @major/shared/models/classes: ["libs/shared/models/src/lib/classes/index.ts"]
  - @major/shared/models/enums: ["libs/shared/models/src/lib/enum/index.ts"]
  - @major/shared/models/interfaces: ["libs/shared/models/src/lib/interfaces/index.ts"]
  - @major/shared/models/literals: ["libs/shared/models/src/lib/literals/index.ts"]
- Backend namespacing: use a single backend library for clusters (libs/web-app-backend/idea-clusters), keep storage/file-meta logic separate and decoupled, depend only on public modules/services.
- Storage <-> idea-clusters circular dependency resolved: FileMetaDataEntity uses string-based TypeORM relation for cluster, and all imports now use Nx project aliases to comply with enforce-module-boundaries. Backend tests and lint now pass for idea-clusters.
- Forbidden imports discovered: domain-events payloads directly import backend entity classes (e.g., SongEntity, UserEntity, FileMetaDataEntity, CollaborationEntity) from other backend libraries, violating Nx enforce-module-boundaries and causing circular/coupling issues. Event classes also import payloads via absolute project paths. These must be refactored to use plain interfaces/DTOs (in shared-models or local to domain-events) and only allowed imports.
- Ongoing: Refactoring backend domain-events payloads to remove forbidden backend entity imports, replacing with DTOs or shared interfaces to comply with Nx boundaries.
- Inspected ProjectCreatedEvent payload and ProjectEntity fields to determine minimal interface for replacing forbidden ProjectEntity import in event payload.
- Domain-events payloads are being updated to use minimal local interfaces (e.g., UpdatedSongSnapshot, UpdateSongPatch) instead of backend entities/DTOs. Next: continue this refactor for all relevant event payloads and test Nx workspace builds/lints.
- Ongoing: Actively refactoring all domain-events payloads to use minimal local interfaces (InviteSongLike, UserLike, SongLikeMinimum, etc.) and eliminate forbidden backend entity imports, as confirmed by recent code inspection and edits.
- Systematically searching domain-events for remaining forbidden entity imports and refactoring them to use minimal interfaces, to comply with Nx enforce-module-boundaries and improve maintainability.
- All "Like" interfaces have now been fully removed from the domain-events library and replaced with new shared Event* DTOs (EventUserWithStripe, EventStripeSubscription, EventProjectMinimal, EventUserToInvite, etc.). These types were introduced to provide minimal, entity-agnostic shapes for event payloads, ensuring Nx boundary compliance and maintainability. Lint for domain-events passes. Next: continue this refactor for any remaining usages outside domain-events if present.
- Fixed null-invitee handling and import paths in invite event handlers (invite-cancelled, invite-declined) to use barrel imports and guard against non-user invites.
- Circular dependencies remain between invites-events and other backend libraries (notifications, util-websocket, collaborations-events, invites). These require further architectural work to resolve.

#### Task List - this was an old plan - may be useful

##### 1. Backend: Database & Entities
- [x] Generate TypeORM migration for Audio Inbox:
  - [x] FileMetaData: add rating (int, nullable, 0-5), tags (text[], nullable, GIN index), notes (text, nullable), clusterId (nullable FK), trashedAt (nullable), CHECK NOT (song_id IS NOT NULL AND cluster_id IS NOT NULL), indexes as per PRD.
  - [x] IdeaCluster: id (PK), externalId (UUID, unique), name, pinned, status, trashedAt, ownerId (FK), timestamps, indexes.
  - [x] IdeaClusterCollaborator: id (PK), clusterId/userId FKs, permission, UNIQUE(clusterId, userId), index on userId.
- [x] Update FileMetaData entity.
- [x] Create IdeaCluster and IdeaClusterCollaborator entities.

##### 2. Backend: Modules & API
- [x] Generate IdeaClusters NestJS module (libs/web-app-backend/idea-clusters):
  - [x] Service: createFromFiles, findAllForUser, update, updateStatus, add/update/removeCollaborator.
  - [x] Controller: GET /clusters, POST /clusters/from-files, PATCH /clusters/:id, sharing endpoints as per PRD.
  - [x] Controller: DELETE /clusters/:id — immediate delete, files remain (clusterId set to NULL).
- [x] Add InboxFilesController for PUT /inbox/files (no songId), using CreateInboxFileDto.
- [x] Implement BullMQ job/service for grouping unassigned files into clusters.

##### 3. Frontend: Library & State
- [x] Generate Nx feature lib: libs/web-app/feature-audio-inbox (routable, lazy, standalone).
- [x] Generate domain libs: libs/web-app/audio-inbox/domain/data (HTTP), domain/query (Angular Query).
- [x] Create singleton UploadService (provided in root).

##### 4. Frontend: Components (MVP)
- [ ] UploadManagerComponent (queue/progress UI).
- [ ] AudioInboxComponent (smart, fetches clusters, manages view state).
- [ ] ClusterGridItemComponent (cluster card).
- [ ] AudioFileCardComponent (audio file card).

##### 5. Frontend: Core Flows
- [ ] Implement bulk file intake, upload queue, and per-file progress (using RecordingUploadQueueService).
- [ ] Implement Inbox upload flow (no songId), error handling, NotifyService.info() toasts.
- [ ] Implement MVP cluster grid/list, annotation (rating, tags, notes), promote to song.

##### 6. Review & Stage Testing
- [ ] Review and test backend migration and API (unit + e2e).
- [ ] Review and test frontend flows and UI (unit + integration).
- [ ] Document and demo MVP before proceeding to collaboration or storage lifecycle phases.
- [x] Add e2e tests for IdeaClustersController endpoints.
- [x] Refactor all usages of "Like" interfaces in backend libraries (domain-events, collaborations-events, invites-events, etc.) to use DTOs or shared interfaces. Remove all "Like" types and fix all resulting type errors.

## Current Goal
Generate Nx feature lib: libs/web-app/feature-audio-inbox (routable, lazy, standalone).

## Decision Log
* **2023-10-27:** The `feature-audio-inbox` library and its basic configuration already existed. Proceeded to create the `domain`, `domain/data`, and `domain/query` libraries as they were missing.
* **2023-10-27:** Encountered issues creating nested directories. Created files in existing or newly created top-level directories within the library structure. The `UploadService` was created in `domain/src` instead of `domain/src/lib` due to this. This can be revisited.
