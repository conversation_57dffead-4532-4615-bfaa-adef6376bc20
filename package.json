{"name": "major", "productName": "Major", "homepage": "https://major.app", "description": "Major.app", "license": "UNLICENSED", "version": "1.4.3", "author": "Wonky Dice Limited", "repository": {"type": "git", "url": "https://github.com/johngrimsey/major-nx.git"}, "scripts": {"ng": "nx", "nx": "nx", "start:web-app": "nx run web-app:serve", "start:web-app-backend": "nx run web-app-backend:serve", "start:marketing": "nx run marketing-app:serve", "docker:validate-env:staging": "bun run ./tools/docker/validate-env-stack.ts -e .env.staging -s major-stack.prod.yml -c vps3-manager", "docker:create-secrets:staging": "bun run ./tools/docker/validate-env-stack.ts -e .env.staging -s major-stack.prod.yml -c vps3-staging --create-secrets", "build:apps": "nx run-many -t build -p web-app marketing-app web-app-backend -c production", "build:containers": "bun run ./tools/docker/build-containers.ts", "deploy:major": "bun run ./tools/docker/deploy-stacks.ts --skip-tests --skip-lint --skip-build --skip-secret-validation --only-major", "deploy:traefik": "bun run ./tools/docker/deploy-stacks.ts --skip-tests --skip-lint --skip-build --skip-secret-validation --only-traefik", "generate:invite": "bun run ./tools/generate-invite.ts", "db:swarm:pull-latest": "bun infra/scripts/deploy/production/swarm-pull-image.ts db latest", "build-push:db:prod": "bun infra/scripts/build/build-push-db.ts && bun run db:swarm:pull-latest", "deploy:data:prod": "bun infra/scripts/deploy/production/prod-deploy-data.ts", "manage:secrets:prod": "bun run --env-file=.env.prod ./infra/scripts/manage/prod-manage-secrets.ts", "manage:cleanup:prod": "bun run --env-file=.env.prod ./infra/scripts/manage/prod-cleanup-docker.ts", "website:swarm:pull-latest": "bun infra/scripts/deploy/production/swarm-pull-image.ts marketing-app $(git rev-parse --short HEAD)", "website:build-push:prod": "bun infra/scripts/build/build-push-marketing-app.ts", "website:deploy:prod": "bun infra/scripts/deploy/production/prod-deploy-website.ts --domain major.app", "website:build-push-deploy:prod": "bun run website:build-push:prod && bun run website:swarm:pull-latest && bun website:deploy:prod", "logto:migrate:local": "docker context use default && docker run --rm --network majordev_major -e DB_URL=************************************/logto ghcr.io/logto-io/logto:1.27 cli db alteration deploy", "logto:migrate:staging": "docker context use vps3-staging && docker run --rm --network major_major-internal -e DB_URL=************************************/logto ghcr.io/logto-io/logto:1.27 cli db alt deploy", "logto:swarm:pull-latest": "bun infra/scripts/deploy/production/swarm-pull-image.ts logto latest", "logto:build-push:prod": "bun infra/scripts/build/build-push-logto.ts", "logto:deploy:prod": "bun infra/scripts/deploy/production/prod-deploy-auth.ts --domain major.app", "logto:build-push-deploy:prod": "bun run logto:build-push:prod && bun run logto:swarm:pull-latest && bun logto:deploy:prod", "web-app:swarm:pull-latest": "bun infra/scripts/deploy/production/swarm-pull-image.ts web-app $(git rev-parse --short HEAD)", "web-app:build-push:prod": "bun infra/scripts/build/build-push-web-app.ts", "web-app:deploy:prod": "bun infra/scripts/deploy/production/prod-deploy-web-app.ts --domain major.app", "web-app:build-push-deploy:prod": "bun run web-app:build-push:prod && bun run web-app:swarm:pull-latest && bun run web-app:deploy:prod", "migrations-runner:build-push:prod": "bun infra/scripts/build/build-push-migrations-runner.ts", "migrations-runner:swarm:pull-latest": "bun infra/scripts/deploy/production/swarm-pull-image.ts migrations-runner $(git rev-parse --short HEAD)", "migrations-runner:run:prod": "bun infra/scripts/manage/prod-run-migrations.ts", "migrations-runner:deploy:prod": "bun run migrations-runner:build-push:prod && bun run migrations-runner:swarm:pull-latest && bun run migrations-runner:run:prod", "web-app-backend:swarm:pull-latest": "bun infra/scripts/deploy/production/swarm-pull-image.ts web-app-backend $(git rev-parse --short HEAD)", "web-app-backend:build-push:prod": "bun infra/scripts/build/build-push-web-app-backend.ts", "web-app-backend:deploy:prod": "bun infra/scripts/deploy/production/prod-deploy-web-app-backend.ts --domain major.app", "web-app-backend:build-push-deploy:prod": "bun run web-app-backend:build-push:prod && bun run migrations-runner:build-push:prod && bun run web-app-backend:swarm:pull-latest && bun run migrations-runner:swarm:pull-latest && bun run web-app-backend:deploy:prod", "deploy:migrations:prod": "bun run migrations-runner:deploy:prod", "deploy:app:prod": "bun run web-app-backend:build-push-deploy:prod && bun run web-app:build-push-deploy:prod", "deploy:traefik:prod": "bun infra/scripts/deploy/production/prod-deploy-traefik.ts", "all:deploy:prod": "bun run deploy:traefik:prod && bun run manage:secrets:prod && bun run build-push-deploy:data:prod && bun run build-push-deploy:auth:prod && bun run build-push-deploy:web-app:prod && bun run build-push-deploy:web-app-backend:prod && bun run build-push-deploy:website:prod", "staging-manager": "bun run infra/scripts/provision/staging/staging-manager.ts", "build:marketing-app:staging": "nx run marketing-app:build:staging --delete-output-path", "build:web-app:staging": "nx run web-app:build:staging --base-href='/' --delete-output-path", "build:web-app-backend:staging": "nx run web-app-backend:build:staging --delete-output-path", "build:marketing-app:prod": "nx run marketing-app:build:production --delete-output-path", "build:web-app:prod": "nx run web-app:build:production --base-href='/' --delete-output-path --skip-nx-cache", "build:web-app-backend:prod": "nx run web-app-backend:build:production --delete-output-path --skip-nx-cache", "postbuild:web-app-backend:prod": "bun tools/generate-package-json.ts web-app-backend", "build:migrations-runner:prod": "nx run migrations-runner:build:production --delete-output-path --skip-nx-cache", "postbuild:migrations-runner:prod": "bun tools/generate-package-json.ts migrations-runner", "build:version-file": "genversion -e -s -v apps/web-app/src/app/version.ts", "build:changelog-json": "changelog-parser > apps/web-app/src/app/changelog.json", "docker:build:all": "bun run ./tools/docker/build-containers.ts", "docker:build:skip-push": "bun run ./tools/docker/build-containers.ts --skip-push", "docker:build:amd64": "bun run ./tools/docker/build-containers.ts -p linux/amd64", "docker:build:arm64": "bun run ./tools/docker/build-containers.ts -p linux/arm64", "docker:build:web-app": "bun run ./tools/docker/build-containers.ts -a web-app", "docker:build:marketing-app": "bun run ./tools/docker/build-containers.ts -a marketing-app", "docker:build:web-app-backend": "bun run ./tools/docker/build-containers.ts -a web-app-backend", "docker:build:affected": "nx affected --target=container", "docker:up": "docker context use default && docker compose -f docker-compose.dev.yml watch", "typeorm": "ts-node --project tsconfig.base.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migrate:generate": "npm run typeorm -- -d libs/web-app-backend/database-migrations/src/lib/datasource.ts migration:generate libs/web-app-backend/database-migrations/src/lib/changelist", "migrate:run": "npm run typeorm -- -d libs/web-app-backend/database-migrations/src/lib/datasource.ts migration:run", "migrate:revert": "npm run typeorm -- -d libs/web-app-backend/database-migrations/src/lib/datasource.ts migration:revert", "release": "bun run infra/scripts/release/release.ts", "release:patch": "bun run infra/scripts/release/release.ts --patch", "release:minor": "bun run infra/scripts/release/release.ts --minor", "release:major": "bun run infra/scripts/release/release.ts --major", "release:dry-run": "bun run infra/scripts/release/release.ts -d", "test": "nx run-many --all --target test --parallel=20", "test:web-app": "nx run-many --target=test --projects=libs/web-app/*  --parallel=20", "test:web-app-backend": "nx run-many --target=test --projects=libs/web-app-backend/* --runInBand", "lint": "nx run-many --all --target lint --parallel=20", "biome:lint": "biome lint apps libs tools", "format:write": "nx format:write", "format:check": "biome check apps libs tools", "workspace-generator": "nx workspace-generator", "dep-graph": "nx dep-graph", "help": "nx help", "docs:json": "compodoc -p ./tsconfig.base.json -e json -d .", "ngrok:webhooks": "ngrok http https://api.dev.major.app --host-header=api.dev.major.app --url=casual-quickly-newt.ngrok-free.app", "rebuild:web-app-backend:local": "docker compose -f docker-compose.dev.yml up -d --force-recreate --build web-app-backend"}, "private": true, "trustedDependencies": ["@compodoc/compodoc", "@nestjs/core", "@swc/core", "core-js", "core-js-pure", "esbuild", "husky", "lmdb", "msgpackr-extract", "node-gyp", "@rspack/core", "protobufjs", "re2"], "dependencies": {"@angular/animations": "20.1.4", "@angular/cdk": "20.1.4", "@angular/common": "20.1.4", "@angular/compiler": "20.1.4", "@angular/core": "20.1.4", "@angular/forms": "20.1.4", "@angular/material": "20.1.4", "@angular/platform-browser": "20.1.4", "@angular/platform-browser-dynamic": "20.1.4", "@angular/platform-server": "20.1.4", "@angular/pwa": "20.1.4", "@angular/router": "20.1.4", "@angular/service-worker": "^20.1.4", "@angular/ssr": "20.1.4", "@aws-sdk/client-s3": "^3.577.0", "@aws-sdk/s3-request-presigner": "^3.577.0", "@breezystack/lamejs": "^1.2.7", "@fontsource/merriweather": "^4.5.6", "@fontsource/plus-jakarta-sans": "^4.5.2", "@golevelup/nestjs-stripe": "^0.8.2", "@google/genai": "^1.11.0", "@logto/browser": "^2.2.16", "@logto/js": "^4.1.1", "@mdi/angular-material": "^7.2.96", "@nestjs/axios": "^3.0.2", "@nestjs/bullmq": "^10.2.1", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^11.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^11.0.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/mapped-types": "^2.0.5", "@nestjs/microservices": "^10.4.7", "@nestjs/platform-express": "^11.0.0", "@nestjs/platform-socket.io": "^10.3.8", "@nestjs/platform-ws": "^10.3.8", "@nestjs/schedule": "^5.0.1", "@nestjs/testing": "^11.0.0", "@nestjs/throttler": "^5.2.0", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.3.8", "@ng-select/ng-select": "14.2.2", "@ngneat/helipopper": "^5.1.1", "@ngneat/overview": "^3.0.2", "@ngneat/until-destroy": "^9.2.2", "@ngrx/component": "19.0.1", "@ngrx/component-store": "19.0.1", "@ngrx/effects": "19.0.1", "@ngrx/entity": "19.0.1", "@ngrx/operators": "19.0.1", "@ngrx/router-store": "19.0.1", "@ngrx/store": "19.0.1", "@ngxs/logger-plugin": "^3.8.1", "@ngxs/storage-plugin": "^3.8.1", "@ngxs/store": "^3.8.1", "@sendgrid/mail": "^8.1.0", "@sentry/angular": "^8.54.0", "@sentry/nestjs": "^8.25.0", "@socket.io/redis-adapter": "^8.3.0", "@stripe/stripe-js": "^4.8.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/angular-query-experimental": "^5.54.1", "@tiptap/extension-bold": "^2.10.3", "@tiptap/extension-bubble-menu": "^2.10.3", "@tiptap/extension-document": "^2.10.3", "@tiptap/extension-floating-menu": "^2.10.3", "@tiptap/extension-hard-break": "^2.10.3", "@tiptap/extension-history": "^2.10.3", "@tiptap/extension-italic": "^2.10.3", "@tiptap/extension-paragraph": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/extension-text": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/starter-kit": "^2.0.2", "angular-auth-oidc-client": "^19.0.0", "angular-shepherd": "^19.0.2", "audio-recorder-polyfill": "^0.4.1", "axios": "1.7.2", "backoff-rxjs": "^7.0.0", "blob-util": "^2.0.2", "body-parser": "^1.19.0", "browser-image-resizer": "^2.4.1", "bullmq": "^5.13.1", "cache-manager": "^5.7.6", "caniuse-lite": "^1.0.30001441", "canvas-confetti": "^1.9.3", "chalk": "^4.1.2", "chord-transposer": "^2.2.1", "chordsheetjs": "^2.9.1", "cloudconvert": "^2.3.7", "cors": "^2.8.5", "daisyui": "^4.10.2", "date-fns": "^4.1.0", "dayjs": "^1.10.6", "dayjs-twitter": "^0.4.0", "driver.js": "^1.3.1", "express": "4.21.2", "fast-xml-parser": "5.0.8", "file-exists": "^5.0.1", "fs-extra": "^8.1.0", "fuse.js": "^6.4.6", "get-blob-duration": "^1.1.2", "get-mp3-duration": "^1.0.0", "handlebars": "^4.7.6", "helmet": "^7.1.0", "html-to-text": "^9.0.5", "install": "^0.13.0", "ioredis": "^5.4.1", "jose": "^5.3.0", "localforage": "^1.10.0", "lodash-es": "^4.17.20", "match-sorter": "^6.3.0", "mjml": "^4.15.3", "nest-commander": "^3.15.0", "nestjs-s3": "^2.0.1", "ng-recaptcha3": "^1.3.2", "ngx-localstorage": "^5.0.0", "ngx-markdown": "^19.1.0", "ngx-pipes": "^3.2.2", "ngx-progressbar": "^9.0.0", "ngx-socket-io": "4.8.5", "ngx-tiptap": "^12.0.0", "ngxs-reset-plugin": "^3.0.0", "ngxtension": "^4.3.2", "node-html-parser": "^1.2.21", "nodemailer": "^7.0.5", "normalize-number": "^1.0.3", "npm": "^10.8.3", "peerjs": "^1.5.4", "pg": "^8.11.5", "posthog-js": "^1.136.8", "posthog-node": "^4.17.1", "reflect-metadata": "^0.1.13", "rxjs": "7.8.1", "sanitize-filename": "^1.6.3", "semver": "^7.3.8", "shortid": "^2.2.15", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "ssr-window": "^5.0.0", "standardized-audio-context": "^25.3.77", "store": "^2.0.12", "stripe": "^14.19.0", "striptags": "^3.1.1", "tailwind-scrollbar": "3.0.5", "tslib": "^2.8.1", "typeorm": "^0.3.20", "ua-parser-js": "^2.0.4", "uuid": "^9.0.1", "volume-meter": "^2.0.1", "wavesurfer.js": "7.3.1", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "20.1.4", "@angular-devkit/core": "20.1.4", "@angular-devkit/schematics": "20.1.4", "@angular-eslint/eslint-plugin": "20.1.1", "@angular-eslint/eslint-plugin-template": "20.1.1", "@angular-eslint/template-parser": "20.1.1", "@angular/cli": "~20.1.0", "@angular/compiler-cli": "20.1.4", "@angular/language-service": "20.1.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@compodoc/compodoc": "^1.1.14", "@faker-js/faker": "^7.4.0", "@golevelup/ts-jest": "^0.5.0", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "11.0.5", "@ngneat/spectator": "^19.0.0", "@ngrx/schematics": "19.0.1", "@ngrx/store-devtools": "19.0.1", "@ngxs/devtools-plugin": "^3.8.1", "@nx/angular": "21.3.11", "@nx/devkit": "21.3.11", "@nx/esbuild": "21.3.11", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/express": "21.3.11", "@nx/jest": "21.3.11", "@nx/js": "21.3.11", "@nx/nest": "21.3.11", "@nx/node": "21.3.11", "@nx/playwright": "21.3.11", "@nx/rspack": "21.3.11", "@nx/web": "21.3.11", "@playwright/test": "^1.36.0", "@release-it/conventional-changelog": "^10.0.1", "@schematics/angular": "20.1.4", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/angular-query-devtools-experimental": "^5.33.0", "@tanstack/eslint-plugin-query": "^5.50.0", "@types/blob-util": "^2.0.0", "@types/body-parser": "^1.19.0", "@types/canvas-confetti": "^1.6.4", "@types/changelog-parser": "^2.8.4", "@types/chordsheetjs": "^2.8.0", "@types/cors": "^2.8.6", "@types/dom-mediacapture-record": "^1.0.7", "@types/express": "^4.17.21", "@types/file-exists": "^4.0.0", "@types/file-saver": "^1.3.0", "@types/format-duration": "^1.0.1", "@types/html-to-text": "^8.1.0", "@types/jest": "30.0.0", "@types/lodash": "^4.14.172", "@types/lodash-es": "^4.17.4", "@types/mailgun-js": "^0.16.3", "@types/mjml": "^4.7.4", "@types/node": "22.13.0", "@types/nodemailer": "^6.4.17", "@types/peerjs": "^1.1.0", "@types/pretty-ms": "^5.0.1", "@types/shortid": "0.0.29", "@types/supertest": "^6.0.2", "@types/ua-parser-js": "^0.7.36", "@types/uuid": "^9.0.8", "@types/wavesurfer.js": "^6.0.3", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@typescript-eslint/utils": "7.18.0", "@webcomponents/custom-elements": "^1.4.3", "autoprefixer": "^10.4.2", "changelog-parser": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "detect-indent": "latest", "detect-newline": "^3.1.0", "dotenv": "~16.4.5", "esbuild": "^0.19.2", "esbuild-plugin-tsc": "^0.4.0", "eslint": "8.57.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-playwright": "^1.6.2", "exitzero": "^1.0.1", "file-saver": "^1.3.8", "fuzzy": "^0.1.3", "genversion": "^2.2.1", "http-server": "^0.11.1", "husky": "^4.2.5", "jest": "30.0.5", "jest-environment-jsdom": "30.0.5", "jest-environment-node": "^29.7.0", "jest-preset-angular": "15.0.0", "jsonc-eslint-parser": "^2.1.0", "marked": "^15.0.7", "ng-mocks": "^14.13.2", "nx": "21.3.11", "pg-mem": "^3.0.4", "postcss": "^8.4.20", "prettier": "^2.6.2", "prettier-plugin-tailwindcss": "^0.4.1", "redis-memory-server": "^0.12.1", "release-it": "^19.0.2", "stringify-package": "latest", "supertest": "^6.3.4", "swc-loader": "^0.2.6", "tailwindcss": "3.4.0", "testcontainers": "^11.0.3", "ts-jest": "29.4.1", "ts-node": "10.9.1", "typescript": "5.8.3", "jest-util": "30.0.5"}, "overrides": {"stylus": "github:stylus/stylus#0.64.0"}}