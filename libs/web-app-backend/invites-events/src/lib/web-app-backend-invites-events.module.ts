import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WebAppBackendUtilWebsocketModule } from '@major/web-app-backend/util-websocket';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { WebAppBackendNotificationsModule } from 'web-app-backend/notifications';
import { NotificationEntity } from 'web-app-backend/notifications/entities';
import { InviteEventHandlerBase } from './handlers/invite-event-handler.base';
import { InviteAcceptedHandler } from './handlers/invite-accepted.handler';
import { InviteDeclinedHandler } from './handlers/invite-declined.handler';
import { InviteCreatedHandler } from './handlers/invite-created.handler';
import { InviteCancelledHandler } from './handlers/invite-cancelled.handler';
import { WebAppBackendCollaborationsEventsModule } from 'web-app-backend/collaborations-events';

@Module({
	imports: [
		TypeOrmModule.forFeature([NotificationEntity, SongEntity]),
		WebAppBackendNotificationsModule,
		WebAppBackendUtilWebsocketModule,
		WebAppBackendCollaborationsEventsModule,
	],
	providers: [
		InviteAcceptedHandler,
		InviteCancelledHandler,
		InviteCreatedHandler,
		InviteDeclinedHandler,
		InviteEventHandlerBase,
	],
})
export class WebAppBackendInvitesEventsModule {}
