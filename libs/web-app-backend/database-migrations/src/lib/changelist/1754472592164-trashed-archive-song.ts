import { MigrationInterface, QueryRunner } from 'typeorm';

export class TrashedArchiveSong1754472592164 implements MigrationInterface {
	name = 'TrashedArchiveSong1754472592164';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."archived_song" ADD "trashed" boolean NOT NULL DEFAULT false`,
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."archived_song" DROP COLUMN "trashed"`,
		);
	}
}
