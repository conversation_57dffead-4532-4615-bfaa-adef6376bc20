import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from '../guards/ws-jwt.guard';
import {
	ConnectedSocket,
	OnGatewayConnection,
	OnGatewayDisconnect,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { ValidateLogtoJwtService } from '@major/web-app-backend/util-auth';
import { logColorError } from '../../../../util-logging/src/lib/utils/cli-colors';
import { UserPresenceService } from '../services/user-presence.service';
import { RefreshPayload } from '@major/shared/models/interfaces';
import {
	WebSocketEvent,
	WebSocketMessage,
} from '@major/shared/util-websocket/types';
import { Server, Socket } from 'socket.io';
import { getUserRoomName } from '../utils/get-user-room-name';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class WebAppGateway implements OnGatewayConnection, OnGatewayDisconnect {
	private logger = new Logger(WebAppGateway.name);

	@WebSocketServer()
	server!: Server;

	constructor(
		private userPresenceService: UserPresenceService,
		private validateJwtService: ValidateLogtoJwtService,
	) {}

	async handleConnection(@ConnectedSocket() client: Socket) {
		const jwt = client.handshake.auth['token'];

		// No JWT provided
		if (!jwt) {
			this.logger.warn(logColorError('No JWT token provided'));

			client.emit('error', { message: 'Authentication required' });
			client.disconnect();
			return;
		}

		try {
			const currentJwtUser = await this.parseJwt(jwt);

			if (!currentJwtUser) {
				this.logger.warn(logColorError('JWT validation returned no user'));
				client.emit('error', { message: 'Invalid authentication token' });
				client.disconnect();
				return;
			}

			const userRoomName = getUserRoomName(currentJwtUser.id);
			await client.join(userRoomName);
			this.logger.debug(`Client joined room "${userRoomName}"`);

			await this.userPresenceService.addUser(client.id, currentJwtUser);
			this.logger.debug(`Client connected: ${currentJwtUser.email}`);
		} catch (error: any) {
			// Handle JWT validation errors
			this.logger.warn(
				logColorError(
					`JWT validation failed: ${error?.message ?? 'No message'}`,
				),
			);
			client.emit('error', { message: 'Invalid authentication token' });
			client.disconnect();
		}
	}

	async handleDisconnect(client: Socket) {
		this.logger.debug(`Client disconnected: ${client.id}`);

		await this.userPresenceService.removeUser(client.id);
	}

	/**
	 * Use this method to send websocket message to a specific user.
	 * User id is a number, it is the UserEntity.id primary key field
	 *
	 * @param userId
	 * @param message
	 */
	async sendToUser(userId: number, message: WebSocketMessage) {
		if (await this.userPresenceService.isUserOnline(userId)) {
			this.logger.debug(`Emit "${message.event}" to userId=${userId}`);

			this.server.to(getUserRoomName(userId)).emit(message.event, message);

			await this.userPresenceService.updateLastSeen(userId);
		} else {
			this.logger.log(`User userId=${userId} is offline`);
		}
	}

	notifyRefreshNeeded(payload: RefreshPayload) {
		const promises = payload.targetUserIds.map((userId) => {
			return this.sendToUser(userId, {
				event: WebSocketEvent.REFRESH_NEEDED,
				data: {
					scope: payload.scope,
					context: payload.context,
				},
			});
		});

		return Promise.all(promises);
	}

	private async parseJwt(jwt: string) {
		return await this.validateJwtService.validateAndParseToken(jwt);
	}
}
