import { On<PERSON>or<PERSON>E<PERSON>, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { CloudConvertJobEntity } from '../types/entities/cloud-convert-job.entity';
import { EntityManager, Repository } from 'typeorm';
import { CloudConvertEntityService } from '../services/cloud-convert-entity.service';
import { Job } from 'bullmq';
import { CloudConvertResultJob } from '../types/interfaces/cloud-convert-result-job.interface';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { startCase } from 'lodash';
import { CloudConvertJobStatus, UserQuotaType } from '@major/shared/models';
import { SongCollaborationService } from '../../../songs/src/lib/services/song-collaboration.service';
import { plainToInstance } from 'class-transformer';
import { CreateSongDto } from '../../../songs/src/lib/dto/create-song.dto';
import { CloudConvertEventEmitter } from 'libs/web-app-backend/cloud-convert/src/services/cloud-convert-event-emitter.service';
import { UserQuotaService } from 'web-app-backend/user-quotas';

@Processor('cloud-convert-result')
export class ResultConsumer extends WorkerHost {
	private readonly JOB_NAME = 'create-new-song';
	private readonly logger = new Logger(ResultConsumer.name);

	constructor(
		@InjectEntityManager() private entityManager: EntityManager,
		@InjectRepository(CloudConvertJobEntity)
		private readonly jobRepository: Repository<CloudConvertJobEntity>,
		private readonly cloudConvertEntityService: CloudConvertEntityService,
		private readonly cloudConvertEventEmitter: CloudConvertEventEmitter,
		private readonly httpService: HttpService,
		private readonly songCollaborationService: SongCollaborationService,
		private readonly userQuotaService: UserQuotaService,
	) {
		super();
	}

	@OnWorkerEvent('active')
	onActive(job: Job<CloudConvertResultJob>) {
		this.logger.debug(`Processing ${job.name} job`);
	}

	@OnWorkerEvent('completed')
	onCompleted(job: Job<CloudConvertResultJob>) {
		this.logger.debug(`Finished processing ${job.name} job`);
	}

	@OnWorkerEvent('failed')
	async onFailed(job: Job<CloudConvertResultJob>, error: Error) {
		this.logger.error(
			`Failed job ${job.id} of type "${job.name}": ${error.message}`,
			error.stack,
		);

		// const jobEntity = await this.jobRepository.findOneByOrFail({
		//   id: job.data.cloudConvertJobEntityId,
		// });
		//
		// jobEntity.status = CloudConvertJobStatus.Failed;
		// jobEntity.error = { message: error.message };
		// jobEntity.endedAt = new Date();
		//
		// const savedJob = await this.cloudConvertEntityService.save(jobEntity);
		// await this.notificationService.notifyJobUpdate(savedJob);
	}

	async process(job: Job<CloudConvertResultJob>) {
		const response = await lastValueFrom(
			this.httpService.get<string>(job.data.downloadUrl, {
				responseType: 'text',
			}),
		);

		const createSongDto = plainToInstance(CreateSongDto, {
			title: this.createSongTitleFromFilename(job.data.fileName),
			authors: '',
			lyrics: response.data.replace(/\n/g, '<br>'),
			createdAt: new Date(Number(job.data.fileLastModified)).toISOString(),
		});

		const song = await this.songCollaborationService.create(
			job.data.userId,
			createSongDto,
		);

		const jobEntity = await this.jobRepository.findOneByOrFail({
			id: job.data.cloudConvertJobEntityId,
		});

		jobEntity.cloudConvertCreditsUsed = job.data.cloudConvertCreditsUsed;
		jobEntity.status = CloudConvertJobStatus.Finished;
		jobEntity.endedAt = new Date();
		jobEntity.songExternalId = song.externalId;

		this.logger.debug(
			`Job "${job.name}" id=${job.id} status=${CloudConvertJobStatus.Finished}`,
		);

		const savedJob = await this.cloudConvertEntityService.save(jobEntity);
		await this.cloudConvertEventEmitter.notifyJobUpdate(savedJob);

		const userId = jobEntity.userId;

		// now increase the usage count for the user
		// which is a convenience addition to the UserStats model
		// which is easily accessible in the UI
		// const userCounts = await this.entityManager.findOneOrFail(UserCountEntity, {
		//   where: { userId },
		//   select: ['cloudConvertConversions'],
		// });

		// but the most important step is to ensure the quota usage is counted
		// we decrement the user's "quota", which is different to their "limit"
		await this.userQuotaService.decrement(userId, UserQuotaType.IMPORTS);

		// const cloudConvertConversions = userCounts.cloudConvertConversions + 1;
		// await this.entityManager.update(UserCountEntity, { userId }, { cloudConvertConversions });
	}

	private createSongTitleFromFilename(filename: string): string {
		const delimiter = '--';
		const removeExtension = true;

		// Split by timestamp delimiter
		const parts = filename.split(delimiter);
		if (parts.length < 2) return filename;

		// Remove the timestamp part
		const titleWithExtension = parts.slice(1).join(delimiter);

		// Remove extension if needed
		const title = removeExtension
			? titleWithExtension.replace(/\.[^/.]+$/, '')
			: titleWithExtension;

		// Replace remaining hyphens with spaces and trim
		return startCase(title.replace(/-/g, ' ').trim());
	}
}
