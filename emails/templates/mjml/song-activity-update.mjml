<mjml>
  <mj-head>
    <mj-title>New activity on {{ songTitle }}</mj-title>
    <mj-preview>You missed some updated to {{ songTitle }}</mj-preview>
    <mj-font name="Plus Jakarta Sans"
             href="https://fonts.googleapis.com/css?family=Plus+Jakarta+Sans:300,400,600,800"/>

    <mj-attributes>
      <mj-all font-family="Plus Jakarta Sans, Arial, sans-serif"/>
      <mj-text font-weight="400" font-size="16px" color="#1f2937" line-height="24px"/>
    </mj-attributes>
  </mj-head>

  <mj-body>
    <!-- header -->
    <mj-section padding-top="16px" padding-bottom="16px" padding-left="32px" border-bottom="1px solid #e5e7eb">
      <mj-image src="https://storage.googleapis.com/cdn.major.app/logo-and-m.png" width="96" alt="Major Logo"></mj-image>
    </mj-section>

    <!-- body -->
    <mj-section padding="32px">
      <mj-text font-size="40px" font-weight="800" line-height="1.25">
        <span style="color: blue">{{ songTitle }}</span> has updates
      </mj-text>
    </mj-section>

    <!-- updates -->
    <mj-section padding="32px" border-top="1px solid #e5e7eb">

      <mj-raw>
        {{#each userActivities}}
      </mj-raw>

      <mj-text>
        <strong>{{userName}}</strong>
        <ul>
          <mj-raw>
            {{#each activities}}
          </mj-raw>

          <li>{{ description }}</li>

          <mj-raw>
            {{/each}}
          </mj-raw>
        </ul>
      </mj-text>

      <mj-raw>
        {{/each}}
      </mj-raw>

      <mj-spacer height="32px"></mj-spacer>

      <!-- TODO add link to song -->
      <mj-button width="200" line-height="1.8"
                 font-weight="600"
                 font-size="18px"
                 border-radius="8px"
                 href="{{songUrl}}"
                 background-color="blue">View song
      </mj-button>
    </mj-section>

    <!-- Footer -->
    <mj-section border-top="1px solid #e5e7eb" background-color="#fafafa" padding-left="32px" padding-top="32px" padding-bottom="32px">
      <mj-text font-size="14px" font-weight="600">
        This email was sent by Major.
        <br /> <span style="color:gray">The premiere songwriting workspace</span>.
        <br /> <a href="https://major.app">Find out more</a>
      </mj-text>

      <mj-spacer height="24px"></mj-spacer>

      <mj-text font-size="12px" color="gray">
        Please do not reply to this automated email.
        <br />© 2025 Major. All rights reserved.
      </mj-text>
    </mj-section>

  </mj-body>
</mjml>
