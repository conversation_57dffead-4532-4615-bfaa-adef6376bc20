{"name": "song-titles", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/web-app-backend/song-titles/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/web-app-backend/song-titles/jest.config.ts"}}}}