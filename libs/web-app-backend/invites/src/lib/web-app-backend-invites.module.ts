import { Modu<PERSON> } from '@nestjs/common';
import { InvitesController } from './controllers/invites.controller';
import { WebAppBackendCollaborationsModule } from '@major/web-app-backend/collaborations';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { WebAppBackendUsersModule } from '@major/web-app-backend/users';
import { InviteService } from './services/invite.service';
import { BullModule } from '@nestjs/bullmq';
import { InviteSongConsumer } from './queue/invite-song.consumer';
import { InviteSongProducer } from './queue/invite-song.producer';
import { WebAppBackendPaymentsModule } from 'web-app-backend/payments';
import { InviteCreationService } from './services/invite-creation.service';
import { WebAppBackendUtilSecurityModule } from 'web-app-backend/util-security';

@Module({
	imports: [
		BullModule.registerQueue({
			name: 'invite-song',
			defaultJobOptions: {
				removeOnComplete: 10,
				removeOnFail: 10,
			},
		}),
		TypeOrmModule.forFeature([SongEntity, InviteSongEntity]),
		WebAppBackendCollaborationsModule,
		WebAppBackendPaymentsModule,
		WebAppBackendUsersModule,
		WebAppBackendUtilSecurityModule,
	],
	controllers: [InvitesController],
	providers: [
		InviteService,
		InviteSongConsumer,
		InviteSongProducer,
		InviteCreationService,
	],
	exports: [InviteSongProducer, InviteCreationService],
})
export class WebAppBackendInvitesModule {}
