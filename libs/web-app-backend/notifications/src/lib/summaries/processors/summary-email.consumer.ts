import { Processor, WorkerHost } from '@nestjs/bullmq';
import { forwardRef, Inject, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { Redis } from 'ioredis';
import { groupBy } from 'lodash';

import {
	REDIS_EVENT_PREFIX,
	SUMMARY_EMAIL_QUEUE,
} from 'libs/web-app-backend/notifications/src/lib/summaries/types/constants/constants';
import {
	SongActivityEventStoredData,
	SummaryJobData,
} from 'libs/web-app-backend/notifications/src/lib/summaries/types/interfaces/notification-event.interface';
import { REDIS_CLIENT } from 'libs/web-app-backend/redis/src/lib/types';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { EditorUserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/editor-user-presence.service';
import { UserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/user-presence.service';
import { WEB_APP_BASE_URL } from 'libs/web-app-backend/util-config/src/lib/symbols';
import { IdeaCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/idea/idea-created.event';
import { CollaborationMessageCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration-messaging/collaboration-message-created.event';
import { FileMetaDataCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/file-meta-data/file-meta-data-created.event';
import { AppMailerService } from 'libs/web-app-backend/mailer/src/lib/services/app-mailer.service';
import { SongActivitySummaryEmailOptions, UserActivityInterface } from 'mailer';

/**
 * Processes delayed summary jobs, fetches stored event details from Redis,
 * aggregates them, and sends a single summary email per recipient/song.
 */
@Processor(SUMMARY_EMAIL_QUEUE)
export class SummaryEmailConsumer extends WorkerHost {
	private readonly logger = new Logger(SummaryEmailConsumer.name);
	private userRepo!: Repository<UserEntity>;
	private songRepo!: Repository<SongEntity>;

	constructor(
		@Inject(WEB_APP_BASE_URL) private readonly baseUrl: string,
		@Inject(REDIS_CLIENT) private readonly redisClient: Redis,
		@InjectDataSource() dataSource: DataSource,
		@Inject(forwardRef(() => EditorUserPresenceService))
		private editorUserPresenceService: EditorUserPresenceService,
		@Inject(forwardRef(() => UserPresenceService))
		private userPresenceService: UserPresenceService,
		private appMailerService: AppMailerService,
	) {
		super();

		this.userRepo = dataSource.getRepository(UserEntity);
		this.songRepo = dataSource.getRepository(SongEntity);
	}

	async process(job: Job<SummaryJobData>) {
		const { recipientUserId, songId } = job.data;
		const jobId = job.id;
		const redisKey = `${REDIS_EVENT_PREFIX}${jobId}`;

		this.logger.log(
			`Processing job ${job.id} for user ${recipientUserId}, song ${songId}`,
		);

		try {
			// Fetch all stored event details atomically and clear the list
			const multi = this.redisClient.multi();

			// Get all items
			multi.lrange(redisKey, 0, -1);

			// Delete the list
			multi.del(redisKey);

			const results = await multi.exec();

			if (!results || results.length === 0 || !results[0] || !results[0][1]) {
				this.logger.warn(
					`No event details found in Redis for job ${jobId}. Possibly already processed or expired.`,
				);
				return; // Nothing to process
			}

			const eventStrings = results[0][1] as string[];

			if (eventStrings.length === 0) {
				this.logger.warn(`Empty event list found in Redis for job ${jobId}.`);
				return; // Nothing to process
			}

			const events: SongActivityEventStoredData[] = eventStrings.map((str) =>
				JSON.parse(str),
			);

			// Get recipient details
			const recipient = await this.userRepo.findOne({
				where: { id: recipientUserId },
			});

			if (!recipient || !recipient.email) {
				this.logger.error(
					`Could not find recipient or recipient email for user ID ${recipientUserId}. Cannot send summary for job ${jobId}.`,
				);
				throw new Error(
					`Recipient ${recipientUserId} not found or missing email.`,
				);
			}

			// Get song details
			const song = await this.songRepo.findOne({
				where: { id: songId },
			});

			if (!song) {
				this.logger.error(
					`Could not find song with ID ${songId}. Cannot send summary for job ${jobId}.`,
				);

				throw new Error(`Song ${songId} not found.`);
			}

			// Check if user is currently active in the song - don't send email if they are
			const isUserOnline =
				await this.userPresenceService.isUserOnline(recipientUserId);

			const isUserInSong =
				await this.editorUserPresenceService.isUserPresentInSong(
					song.externalId, // Use the external ID that matches what the frontend uses
					recipient.userId, // Use the external user ID
				);

			// if (isUserOnline && isUserInSong) {
			if (isUserOnline && isUserInSong) {
				this.logger.log(
					`Skipping email for user ${recipientUserId} as they are currently active in song ${songId} (${song.externalId})`,
					// `Skipping email for user ${recipientUserId} as they are currently online`,
				);
				return;
			}

			// Aggregate events by action user and event type
			const summary = this.aggregateEvents(events);

			const songTitle = song.title || `Song ${songId}`;

			// Prepare template data for SendGrid
			const subject = `${songTitle} - New Activity Summary`;

			const templateData = await this.prepareTemplateData(
				summary,
				songTitle,
				song.externalId,
			);

			await this.appMailerService.sendSongActivitySummaryEmail({
				recipientEmail: recipient.email,
				subject,
				templateData,
			});

			this.logger.log(
				`Successfully processed job ${jobId} and sent summary email to ${recipient.email}`,
			);
		} catch (error: any) {
			this.logger.error(
				`Failed to process summary job ${jobId} for user ${recipientUserId}, song ${songId}: ${error.message}`,
				error.stack,
			);
			// Re-throw the error so BullMQ can handle retries based on queue settings
			throw error;
		}
	}

	/**
	 * Aggregates events by actionUserId and then by eventType
	 */
	private aggregateEvents(
		events: SongActivityEventStoredData[],
	): Map<string, Record<string, number>> {
		// Group events by actionUserId
		const eventsByUser = groupBy(events, 'actionUserId');

		// For each user, count events by type
		const summary = new Map<string, Record<string, number>>();

		for (const [userId, userEvents] of Object.entries(eventsByUser)) {
			const eventCounts = {} as Record<string, number>;

			// Count occurrences of each event type
			for (const event of userEvents) {
				eventCounts[event.eventType] = (eventCounts[event.eventType] || 0) + 1;
			}

			summary.set(userId, eventCounts);
		}

		return summary;
	}

	/**
	 * Prepares data for the SendGrid template
	 */
	private async prepareTemplateData(
		summary: Map<string, Record<string, number>>,
		songTitle: string,
		songExternalId?: string,
	): Promise<SongActivitySummaryEmailOptions['templateData']> {
		// Debug log the raw summary data
		this.logger.debug(
			'Raw event summary:',
			JSON.stringify(
				Array.from(summary.entries()).map(([userId, events]) => ({
					userId,
					events,
				})),
			),
		);

		// Create template data structure
		const templateData: SongActivitySummaryEmailOptions['templateData'] = {
			songTitle,
			songUrl: songExternalId
				? `${this.baseUrl}/song/${songExternalId}`
				: undefined,
			userActivities: [],
		};

		// Process each user's activities
		for (const [actionUserId, events] of summary.entries()) {
			// Get user's name
			const user = await this.userRepo.findOne({
				where: { id: Number(actionUserId) },
			});
			const userName = user?.displayName ?? `User ${actionUserId}`;

			const userActivity: UserActivityInterface = {
				userName,
				activities: [],
			};

			for (const [eventType, count] of Object.entries(events)) {
				this.logger.debug(
					`Processing event type: "${eventType}" with count: ${count}`,
				);

				// Map event types to user-friendly terms
				let item: string;

				switch (eventType) {
					case String(IdeaCreatedEvent.eventName):
						item = count > 1 ? 'ideas' : 'idea';
						break;

					case String(CollaborationMessageCreatedEvent.eventName):
						item = count > 1 ? 'messages' : 'message';
						break;

					case String(FileMetaDataCreatedEvent.eventName):
						item = count > 1 ? 'recordings' : 'recording';
						break;

					default:
						// Fallback to splitting by dot for unknown event types
						item = eventType.split('.')[0];
						item = count > 1 ? `${item}s` : item;
				}

				userActivity.activities.push({
					type: eventType,
					count,
					description: `${count} new ${item}`,
				});
			}

			templateData.userActivities.push(userActivity);
		}

		return templateData;
	}
}
