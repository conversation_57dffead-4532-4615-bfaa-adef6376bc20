export function curateChangelogPrompt(changelogItems: string) {
	return `# Technical Changelog to User-Friendly Release Notes Converter

## Task Description
Your primary goal is to transform a technical, commit-based changelog into a concise, engaging, and easy-to-understand set of release notes for end-users. The output should be a narrative of what has changed in each version, focusing on user benefits and major new capabilities, rather than a granular list of every single change.

## Input Format
You will receive a series of changelog entries derived from git commit logs. These entries may follow different formats and conventions, including but not limited to:
- Conventional commits format (feat:, fix:, chore:, etc.)
- Ticket/issue numbers (JIRA-123, #456)
- Developer-focused technical descriptions
- Varied levels of detail and formality
- Possible inclusion of breaking changes
- References to internal systems or code that users wouldn't understand

## Output Requirements

### Overall Structure & Tone
1.  **Group by Version:** The highest level of organization is the version number (e.g., \`# Version 1.2.0\`).
2.  **Engaging Introduction:** For major or minor versions (e.g., 1.2.0, 1.1.0), write a brief, one-sentence summary that captures the theme of the update.
3.  **Simple Categories:** Under each version, use only two main categories:
    - \`### ✨ What's New\`
    - \`### 🛠️ Improvements & Fixes\`
4.  **Prioritize User Impact:** Place the most exciting and impactful user-facing features under "What's New". Less critical bug fixes and minor tweaks go under "Improvements & Fixes".
5.  **Synthesize, Don't Just List:** **This is critical.** Do not create a 1-to-1 mapping of commits to release notes. Instead, analyze all commits for a version and group related changes into a single, coherent bullet point. For example, multiple commits about "activity feed" should result in one clear entry like: "**Activity Feed:** See all the latest updates for your songs in one place..."

### Content Filtering Rules
1.  **STRICTLY EXCLUDE ALL technical implementation details:**
    - No mentions of build processes, CI/CD, Docker, or infrastructure.
    - No testing frameworks, test-related changes, or refactoring that doesn't produce a user-visible benefit.
    - No state management details (e.g., Redux, NGRX, TanStack Query).
    - No database information, migrations, or schemas.
    - No TypeScript/JavaScript details, dependency updates, or library names.
    - No code structure changes, internal model names (e.g., \`SongEntity\`), or API endpoints.

2.  **Only Include:**
    - New features or capabilities users can see and use.
    - Bug fixes that resolved issues users may have encountered.
    - Performance improvements framed in terms of user experience (e.g., "The editor now loads much faster").
    - Visible UI changes or improvements.

### Writing Style
1.  **User-Focused Language:** Transform technical descriptions into user benefits. Focus on what the user can now do.
2.  **Simple & Direct:** Use plain English. Avoid jargon.
3.  **Consistent Voice:** Use present tense (e.g., "Adds" not "Added"). Start each entry with an action verb.
4.  **Clarity & Conciseness:** Keep entries brief (1-2 lines). Use complete sentences.

### Formatting
- Use Markdown for structure.
- Top-level heading for each version: \`# Version 1.2.0 - July 30, 2025\`
- Sub-headings for categories: \`### ✨ What's New\` and \`### 🛠️ Improvements & Fixes\`
- Use bullet points (\`*\`) for each item.
- Use **bold** text to highlight the feature name.

## Example Transformation

### Input (Git Commit Log):
\`\`\`
feat: add activity feed (activity) (cfe6dc9)
feat: add song creation activity (activity) (0bd6e9a)
feat: Implement file created activity (main) (f66b977)
feat: autosave in editor (9ced2c7)
fix(recorder): fix recorder files list not updating when creating new (a9d1e43)
chore: update dependencies
refactor: improve app boot process (main) (c1728d8)
\`\`\`

### Desired Output (User-Facing Release Notes):

# Version 1.2.0 - July 30, 2025

This is a big update focused on collaboration and improving your workflow!

### ✨ What's New

*   **Activity Feed:** See all the latest updates for your songs in one place! The new feed tracks everything from new recordings and file renames to when collaborators join or leave.
*   **Auto-Saving Editor:** Never lose your work again. The editor now automatically saves your changes as you type.

### 🛠️ Improvements & Fixes

*   Fixed a bug where the list of recordings wouldn't update after creating a new one.
*   The app now starts up and feels more responsive.

---

## Final Verification Checklist
Before finalizing, verify:
1.  Have ALL technical details been completely removed?
2.  Is the output grouped by Version, then by "What's New" and "Improvements & Fixes"?
3.  Are related commits synthesized into single, user-friendly points?
4.  Is the language simple, direct, and focused on user benefits?

Here are the changelog entries:

${changelogItems}`;
}
