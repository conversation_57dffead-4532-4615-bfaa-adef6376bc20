import { Inject, Injectable, Logger } from '@nestjs/common';
import { HMAC_SECRET } from '@major/web-app-backend/util-auth';
import { createHmac } from 'node:crypto';

@Injectable()
export class HmacService {
	private logger = new Logger('HmacService');

	constructor(@Inject(HMAC_SECRET) private readonly hmacSecret: string) {}

	/**
	 * Create a specific token for validating an uploaded file's key and fileSize
	 *
	 * @param key
	 * @param fileSize
	 */
	createHmacToken(key: string, fileSize: number) {
		// const tokenExpiryTimestamp = dayjs().add(1, 'hour').valueOf();
		// const data = `${key}||${fileSize}||${tokenExpiryTimestamp}`;

		const data = `${key}||${fileSize}`;
		const token = createHmac('sha256', this.hmacSecret)
			.update(data)
			.digest('hex');

		return token;
	}

	/**
	 * Decode a specific hmac signature
	 *
	 * @param tokenToVerify
	 * @param bucketObjectKey
	 * @param fileSize
	 */
	verifyHmacToken(
		tokenToVerify: string,
		bucketObjectKey: string,
		fileSize: number,
	) {
		const token = createHmac('sha256', this.hmacSecret)
			.update(`${bucketObjectKey}||${fileSize}`)
			.digest('hex');

		return token === tokenToVerify;
	}

	verifyHmacSignature(
		key: string,
		message: string,
		signature: string,
	): boolean {
		const hmac = createHmac('sha256', key);
		hmac.update(message);
		const computedSignature = hmac.digest('hex');

		if (computedSignature !== signature) {
			this.logger.log(
				`Invalid signature: expected "${signature}" got "${computedSignature}"`,
			);
			return false;
		}

		return true;
	}
}
