import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FileMetaDataEntity } from './entities/file-meta-data.entity';
import {
	Brackets,
	EntityManager,
	Repository,
	SelectQueryBuilder,
} from 'typeorm';
import { UpdateFileMetaDatumDto } from './dto/update-file-meta-datum.dto';
import { FileMetaDataTransformer } from '@major/web-app-backend/util-typeorm';
import { DomainEventEmitter } from 'domain-events';
import { FileMetaDataCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/file-meta-data/file-meta-data-created.event';
import { FileMetaDataRemovedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/file-meta-data/file-meta-data-removed.event';

@Injectable()
export class FileMetaDataService {
	private logger = new Logger(FileMetaDataService.name);
	private fileMetaDataTransformer: FileMetaDataTransformer;

	constructor(
		@InjectRepository(FileMetaDataEntity)
		public readonly repository: Repository<FileMetaDataEntity>,
		private entityManager: EntityManager,
		private domainEventEmitter: DomainEventEmitter,
	) {
		this.fileMetaDataTransformer = new FileMetaDataTransformer(
			this.entityManager,
		);
	}

	//
	//
	//

	private async executeQueryWithTransformer(
		query: SelectQueryBuilder<FileMetaDataEntity>,
	): Promise<FileMetaDataEntity[]> {
		const { raw, entities } = await query.getRawAndEntities();
		return this.fileMetaDataTransformer.transformFileMetaDataResults(
			raw,
			entities,
		);
	}

	//
	//
	//

	async create(doc: FileMetaDataEntity) {
		this.logger.debug(`create() called with file: ${JSON.stringify(doc)}`);

		// sane and reload, id will then exist
		const saved = await this.repository.save(doc);

		// fetch the FileMetaData with relations to ensure the event has a fat payload
		const fileEntity = await this.repository.findOneOrFail({
			where: { id: saved.id },
			relations: {
				song: {
					collaborations: true,
				},
				owner: true,
			},
		});

		this.logger.debug(
			`Fetched file id=${saved.id}: ${JSON.stringify(fileEntity)}`,
		);

		this.domainEventEmitter.emitEvent(
			new FileMetaDataCreatedEvent({
				songId: saved.song.id,
				actionUserId: fileEntity.owner.id,
				recordingId: fileEntity.id,
				file: fileEntity,
				timestamp: Date.now(),
			}),
		);

		return fileEntity;
	}

	async findAll(ownerId: number) {
		const query = this.repository
			.createQueryBuilder('fileMeta')
			.leftJoin('fileMeta.owner', 'fileOwner')
			.addSelect('fileOwner.userId', 'files_ownerExternalId')
			.addSelect('fileOwner.displayName', 'files_ownerDisplayName')
			.addSelect('fileOwner.photoUrl', 'files_ownerPhotoUrl')
			.where('fileOwner.id = :ownerId', { ownerId });

		return await this.executeQueryWithTransformer(query);
	}

	async findOneById(id: string) {
		return this.repository.findOne({
			where: { id },
			relations: ['owner', 'song'],
		});
	}

	async findOneByOwnerOrCollaborator(
		id: string,
		ownerOrSongCollaboratorId: number,
	) {
		const query = this.repository
			.createQueryBuilder('fileMeta')
			.leftJoin('fileMeta.song', 'song')
			.leftJoin('fileMeta.owner', 'fileOwner')
			.leftJoin('song.collaborations', 'collaborations')
			// This is a custom field that is not part of the entity
			// The custom transformer FileMetaDataTransformer must handle this
			.addSelect(
				'CASE WHEN fileOwner.id = :userId THEN true ELSE false END',
				'files_currentUserIsOwner',
			)
			// also add select for the file owner's displayName and photoUrl
			.addSelect('fileOwner.userId', 'files_ownerExternalId')
			.addSelect('fileOwner.displayName', 'files_ownerDisplayName')
			.addSelect('fileOwner.photoUrl', 'files_ownerPhotoUrl')
			.where(
				new Brackets((qb) => {
					qb.where('collaborations.inviteeId = :userId').orWhere(
						'fileMeta.ownerId = :userId',
					);
				}),
			)
			.andWhere('fileMeta.id = :id', { id })
			.setParameter('userId', ownerOrSongCollaboratorId);

		const results = await this.executeQueryWithTransformer(query);
		// console.log('findOneByOwnerOrCollaborator()');
		// console.log(results);
		return results[0] ?? null;
	}

	saveOne(doc: FileMetaDataEntity) {
		return this.repository.save(doc);
	}

	remove(id: string, ownerId: number) {
		return this.repository.delete({ id, owner: { id: ownerId } });
	}

	update(id: string, updateFileMetaDatumDto: UpdateFileMetaDatumDto) {
		return this.repository.update(id, updateFileMetaDatumDto);
	}

	findBySongId(songExternalId: string, currentUserId: number) {
		const query = this.repository
			.createQueryBuilder('fileMeta')
			.leftJoin('fileMeta.song', 'song')
			.leftJoinAndSelect('fileMeta.owner', 'fileOwner')
			.addSelect(
				'CASE WHEN fileOwner.id = :userId THEN true ELSE false END',
				'files_currentUserIsOwner',
			)
			.addSelect('fileOwner.userId', 'files_ownerExternalId')
			.addSelect('fileOwner.displayName', 'files_ownerDisplayName')
			.addSelect('fileOwner.photoUrl', 'files_ownerPhotoUrl')
			.where('song.externalId = :songExternalId')
			.orderBy('fileMeta.createdAt', 'DESC')
			.setParameter('userId', currentUserId)
			.setParameter('songExternalId', songExternalId);

		return this.executeQueryWithTransformer(query);
	}

	// TODO should not be able to rename if not a collaborator
	rename(id: string, displayName: string) {
		return this.repository.update(id, { displayName });
	}
}
