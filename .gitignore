# See http://help.github.com/ignore-files/ for more about ignoring files.
.env*
*.patch

# compiled output
/dist
/tmp
/out-tsc

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.zed

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.angular/cache
.swc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
/*.log
secrets_update_log.txt

# System Files
.DS_Store
Thumbs.db
.vscode

.runtimeconfig.json
apps/firebase/fixtures/auth_export/
migrations.json
apps/functions2/src/.runtimeconfig.json
apps/functions2/env/*

.angular
.firebase

# created by firebase-tools docker image
# https://github.com/AndreySenov/firebase-tools-docker/blob/main/doc/guide/running_firebase_emulators.md
.config

.npm
.cache

# DB data
db/data

# firebase extension
extensions/firestore-stripe-payments.env


.nx/cache
.nx/workspace-data
test-results
release-notes.md

#
# AI coding tools
#
.windsurf
.opencode
.opencode.json
.taskmaster

#
# Logs
#
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/

# docker volumes
infra/docker/volumes
.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
