import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CloudConvertJobEntity } from '../types/entities/cloud-convert-job.entity';
import { Logger } from '@nestjs/common';
import { captureException } from '@sentry/nestjs';
import { CloudConvertJob, CloudConvertJobStatus } from '@major/shared/models';
import { CloudConvertEntityService } from '../services/cloud-convert-entity.service';
import { CloudConvertSdkService } from '../services/cloud-convert-sdk.service';
import { CloudConvertEventEmitter } from 'libs/web-app-backend/cloud-convert/src/services/cloud-convert-event-emitter.service';

@Processor('cloud-convert')
export class ConversionConsumer extends WorkerHost {
	private JOB_NAME = 'convert-to-text';
	private readonly logger = new Logger(ConversionConsumer.name);

	constructor(
		@InjectRepository(CloudConvertJobEntity)
		private readonly jobRepository: Repository<CloudConvertJobEntity>,
		private readonly cloudConvertEntityService: CloudConvertEntityService,
		private readonly cloudConvertSdkService: CloudConvertSdkService,
		private readonly notificationService: CloudConvertEventEmitter,
	) {
		super();
	}

	private canProcess(job: Job) {
		return job.name === this.JOB_NAME;
	}

	@OnWorkerEvent('active')
	onActive(job: Job<CloudConvertJob>) {
		if (!this.canProcess(job)) {
			return;
		}

		this.logger.debug('Processing cloudconvert job');
	}

	@OnWorkerEvent('completed')
	onCompleted(job: Job<CloudConvertJob>) {
		if (!this.canProcess(job)) {
			return;
		}

		this.logger.debug(`Finished processing ${job.name} job`);
	}

	@OnWorkerEvent('failed')
	onFailed(job: Job, error: Error) {
		if (!this.canProcess(job)) {
			return;
		}

		this.logger.error(
			`Failed job ${job.id} of type ${job.name}: ${error.message}`,
			error.stack,
		);

		// You can add custom logic here, like notifying an admin or logging to a service

		captureException(error, {
			data: {
				job,
			},
		});
	}

	async process(job: Job<CloudConvertJob>) {
		if (!this.canProcess(job)) {
			return;
		}

		this.logger.debug(`Processing ${job.name} job`);

		const cloudConvertJob = await this.submitToCloudConvert(job);

		if (!cloudConvertJob) {
			throw new Error(`Cloud convert job failed: ${job.name} ${job.id}`);
		}

		const jobEntity = await this.jobRepository.findOneByOrFail({
			bullJobId: job.id,
		});
		jobEntity.cloudConvertJobId = cloudConvertJob.id;
		jobEntity.status = CloudConvertJobStatus.Pending;

		await this.jobRepository.save(jobEntity);
		await this.notificationService.notifyJobUpdate(jobEntity);

		return;
	}

	async submitToCloudConvert(bullJob: Job<CloudConvertJob>) {
		const cloudConvertJob = bullJob.data;

		try {
			this.logger.debug('Calling CloudConvert SDK');

			return this.cloudConvertSdkService.createJobFromTemplate(
				cloudConvertJob.cloudConvertTemplate,
			);
		} catch (error) {
			this.logger.error(`CloudConvert call failed: ${error}`);
			captureException('Submitting job to cloudconvert failed', {
				data: {
					bullJob,
					error,
				},
			});

			const jobEntity = await this.jobRepository.findOneByOrFail({
				bullJobId: bullJob.id,
			});
			jobEntity.status = CloudConvertJobStatus.Failed;
			await this.jobRepository.save(jobEntity);

			return false;
		}
	}
}
