import { Module } from '@nestjs/common';
import { S3Module } from 'nestjs-s3';
import { WebAppBackendUserQuotasModule } from 'web-app-backend/user-quotas';
import { WebAppBackendUtilSecurityModule } from 'web-app-backend/util-security';
import { S3BucketController } from './controllers/s3-bucket.controller';
import { S3AvatarsBucketService } from './services/s3-avatars-bucket.service';
import { S3BucketService } from './services/s3-bucket.service';
import {
	CLOUDFLARE_R2_ACCESS_KEY_ID,
	CLOUDFLARE_R2_ENDPOINT,
	CLOUDFLARE_R2_SECRET_ACCESS_KEY,
} from './symbols';
import { WebAppBackendStorageConfigModule } from './web-app-backend-storage-config.module';
import { FileMetaEventListener } from 'libs/web-app-backend/storage/src/lib/listeners/file-meta-event.listener';
import { provideConfigValue } from 'web-app-backend/util-config';
import { HMAC_SECRET } from '@major/web-app-backend/util-auth';

@Module({
	imports: [
		WebAppBackendStorageConfigModule,
		WebAppBackendUtilSecurityModule,
		WebAppBackendUserQuotasModule,
		S3Module.forRootAsync({
			imports: [WebAppBackendStorageConfigModule],
			inject: [
				CLOUDFLARE_R2_ACCESS_KEY_ID,
				CLOUDFLARE_R2_SECRET_ACCESS_KEY,
				CLOUDFLARE_R2_ENDPOINT,
			],
			useFactory: (
				accessKeyId: string,
				secretAccessKey: string,
				endpoint: string,
			) => ({
				config: {
					credentials: {
						accessKeyId,
						secretAccessKey,
					},
					endpoint,
					region: 'auto',
					forcePathStyle: true,
					signatureVersion: 'v4',
				},
			}),
		}),
	],
	controllers: [S3BucketController],
	providers: [
		provideConfigValue(HMAC_SECRET, 'HMAC_SECRET'),

		S3BucketService,
		S3AvatarsBucketService,
		FileMetaEventListener,
	],
	exports: [S3BucketService, S3AvatarsBucketService],
})
export class WebAppBackendStorageModule {}
