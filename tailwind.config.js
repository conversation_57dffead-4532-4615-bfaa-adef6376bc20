const defaultTheme = require('tailwindcss/defaultTheme');

/** @type {import('tailwindcss').Config} */
module.exports = {
	important: true,
	content: ['./apps/**/*.{html,ts}', './libs/**/*.{html,ts}'],
	darkMode: 'class',
	theme: {
		fontWeight: {
			thin: '200', // was 100
			extralight: '300', // was 200
			light: '400', // was 300
			normal: '500', // was 400
			medium: '600', // was 500
			semibold: '700', // was 600
			bold: '800', // was 700
			extrabold: '900', // was 800
			black: '950', // was 900
		},
		extend: {
			backgroundPosition: {
				'zigzag-pattern': '23px 0, 23px 0, 0 0, 0 0',
			},
			backgroundSize: {
				'zigzag-pattern': '46px 46px',
			},
			screens: {
				'3xl': '1600px',
				'4xl': '1800px',
				mobile: { max: '767px' },
				desktop: { min: '768px' },
			},
			fontSize: {
				'2xs': '11px',
			},
			fontFamily: {
				sans: ['Plus Jakarta Sans', ...defaultTheme.fontFamily.sans],
				serif: ['Merriweather', ...defaultTheme.fontFamily.serif],
			},
			backgroundImage: {
				'screen-editor': "url('/assets/images/screenshots/editor.png')",
				'screen-dashboard': "url('/assets/images/screenshots/dashboard.png')",
				'screen-songs': "url('/assets/images/screenshots/songs.png')",
				'girl-sofa-laptop': "url('/assets/images/photos/girl-sofa-laptop.jpg')",
				'zigzag-pattern':
					'linear-gradient(135deg, #d9dbfd 25%, transparent 25%), linear-gradient(225deg, #d9dbfd 25%, transparent 25%), linear-gradient(45deg, #d9dbfd 25%, transparent 25%), linear-gradient(315deg, #d9dbfd 25%, #e5e5f7 25%)',
			},
			animation: {
				shake: 'shake 0.82s cubic-bezier(.36,.07,.19,.97) both',
			},
			keyframes: {
				shake: {
					'10%, 90%': { transform: 'translate3d(-1px, 0, 0)' },
					'20%, 80%': { transform: 'translate3d(2px, 0, 0)' },
					'30%, 50%, 70%': { transform: 'translate3d(-4px, 0, 0)' },
					'40%, 60%': { transform: 'translate3d(4px, 0, 0)' },
				},
			},
			// Updated container queries configuration
			containerQueries: {
				xs: '16rem', // w-64 (64 * 4 = 256px = 16rem)
				sm: '24rem', // w-96 (96 * 4 = 384px = 24rem)
				md: '32rem', // w-128 (128 * 4 = 512px = 32rem)
				lg: '48rem', // w-192 (192 * 4 = 768px = 48rem)
				xl: '64rem', // w-256 (256 * 4 = 1024px = 64rem)
				'2xl': '80rem', // w-320 (320 * 4 = 1280px = 80rem)
				'3xl': '96rem', // w-384 (384 * 4 = 1536px = 96rem)
				'4xl': '112rem', // w-448 (448 * 4 = 1792px = 112rem)
			},
		},
	},
	plugins: [
		require('@tailwindcss/forms'),
		require('@tailwindcss/typography'),
		require('@tailwindcss/container-queries'),
		require('tailwind-scrollbar')({ nocompatible: true }),
		require('daisyui'),
	],
	daisyui: {
		logs: false,
		themes: [
			{
				major: {
					primary: '#4f46e5',
					'primary-focus': '#4338ca',
					'primary-content': '#ffffff',

					secondary: '#e0e7ff',
					'secondary-focus': '#c7d2fe',
					'secondary-content': '#4338ca',

					accent: '#ffffff',
					'accent-focus': '#ffffff',
					'accent-content': '#4f46e5',

					neutral: '#0e0e0e',
					'neutral-focus': '#050505',
					'neutral-content': '#ffffff',

					'base-100': '#ffffff',
					'base-200': '#fafbff',
					'base-300': '#edeef5',
					'base-content': '#1f2937',

					info: '#38a4fc',
					'info-content': '#ffffff',
					success: '#22c55e',
					'success-content': '#ffffff',
					warning: '#ff9900',
					'warning-content': '#ffffff',
					error: '#ff5724',
					'error-content': '#ffffff',

					'--rounded-box': '1rem',
					'--rounded-btn': '0.5rem',
					'--rounded-badge': '1.9rem',

					'--animation-btn': '0.25s',
					'--animation-input': '0.2s',

					'--btn-text-case': 'uppercase',
					'--navbar-padding': '0.5rem',
					'--border-btn': '1px',
					'--tab-border': '1px',
					'--tab-radius': '0.5rem',
				},

				dark: {
					primary: '#4f46e5',
					'primary-focus': '#4338ca',
					'primary-content': '#f5f5f5',

					secondary: '#082f49',
					'secondary-focus': '#202a3b',
					'secondary-content': '#dbdfe8',

					accent: '#1a998c',
					'accent-focus': '#166159',
					'accent-content': '#f5f5f5',

					neutral: '#000000',
					'neutral-focus': '#000000',
					'neutral-content': '#f5f5f5',

					'base-100': '#2b2d30',
					'base-200': '#222223',
					'base-300': '#020202',
					'base-content': '#e8ecf0',

					info: '#075985',
					'info-content': '#bae6fd',
					success: '#4d7c0f',
					'success-content': '#bef264',
					warning: '#ff9900',
					'warning-content': '#ffffff',
					error: '#ef4444',
					'error-content': '#fdd',

					'--rounded-box': '1rem',
					'--rounded-btn': '0.5rem',
					'--rounded-badge': '1.9rem',

					'--animation-btn': '0.25s',
					'--animation-input': '0.2s',

					'--btn-text-case': 'uppercase',
					'--navbar-padding': '0.5rem',
					'--border-btn': '1px',

					'--tab-border': '1px',
					'--tab-radius': '0.5rem',
				},

				primary: {
					primary: '#ffffff', // white
					'primary-focus': '#e0e7ff', // indigo-100
					'primary-content': '#4f46e5', // indigo-500

					secondary: '#ffa500', // orange-400
					'secondary-focus': '#ff9900', // orange-500
					'secondary-content': '#4f46e5', // indigo-500

					accent: '#ffa500', // orange-400
					'accent-focus': '#ff9900', // orange-500
					'accent-content': '#4f46e5', // indigo-500

					neutral: '#0e0e0e',
					'neutral-focus': '#050505',
					'neutral-content': '#ffffff',

					'base-100': '#c7d2fe', // indigo-200
					'base-200': '#a3a3a3',
					'base-300': '#4f46e5', // indigo-500
					'base-content': '#ffffff',

					info: '#38a4fc',
					'info-content': '#ffffff',
					success: '#22c55e',
					'success-content': '#ffffff',
					warning: '#ff9900',
					'warning-content': '#ffffff',
					error: '#ff5724',
					'error-content': '#ffffff',
				},

				note: {
					primary: '#eab308', // yellow-500
					'primary-focus': '#ca8a04', // yellow-600
					'primary-content': '#713f12', // yellow-950

					secondary: '#fef08a', // yellow-200
					'secondary-focus': '#fde047', // yellow-300
					'secondary-content': '#854d0e', // yellow-800

					accent: '#fef9c3', // yellow-100
					'accent-focus': '#fef08a', // yellow-200
					'accent-content': '#a16207', // yellow-700

					neutral: '#0e0e0e',
					'neutral-focus': '#050505',
					'neutral-content': '#ffffff',

					'base-100': '#fef9c3', // yellow-100 - main post-it background
					'base-200': '#fef08a', // yellow-200 - slightly darker post-it
					'base-300': '#fde047', // yellow-300 - for borders and accents
					'base-content': '#854d0e', // yellow-800 - primary text color

					info: '#38a4fc',
					'info-content': '#ffffff',
					success: '#22c55e',
					'success-content': '#ffffff',
					warning: '#ff9900',
					'warning-content': '#ffffff',
					error: '#ff5724',
					'error-content': '#ffffff',

					'--rounded-box': '0.75rem',
					'--rounded-btn': '0.5rem',
					'--rounded-badge': '1.9rem',

					'--animation-btn': '0.25s',
					'--animation-input': '0.2s',

					'--btn-text-case': 'uppercase',
					'--navbar-padding': '0.5rem',
					'--border-btn': '1px',
					'--tab-border': '1px',
					'--tab-radius': '0.5rem',
				},
			},
		],
	},
};
