FROM node:22.13-slim AS builder

# Set bash as the default shell
SHELL ["/bin/bash", "-c"]

# Install necessary packages for the installation script and bun
RUN apt-get update && apt-get install -y curl unzip python3 git lsof make g++ procps

ARG BUN_VERSION="1.2.19"

RUN curl -fsSL https://bun.sh/install | bash -s "bun-v${BUN_VERSION}"

ENV PATH="/root/.bun/bin:${PATH}"

WORKDIR /usr/src/app

COPY package.json bun.lock ./

RUN bun i --frozen-lockfile

COPY . .

FROM builder AS web-app

# Expose the port your app runs on
EXPOSE 4200

# Define the command to run your app
CMD [ "bun", "nx", "run", "web-app:serve:development" ]

# Stage for web-app-backend
FROM builder AS web-app-backend

# Expose the port your app runs on
EXPOSE 3000

# Define the command to run your app
CMD [ "bun", "nx", "run", "web-app-backend:serve:development", "--verbose" ]

# Stage for migrations-runner
FROM builder AS migrations-runner

# Run migrations specifically for docker-compose ('development')
# The local configuration is used for debugging, running on host machine
CMD [ "bun", "nx", "run", "migrations-runner:migrate:development" ]

# Stage for marketing-app
FROM builder AS marketing

# Expose the port your app runs on
EXPOSE 4200

# Define the command to run your app
CMD [ "bun", "nx", "run", "marketing-app:serve:development", "--verbose"]
