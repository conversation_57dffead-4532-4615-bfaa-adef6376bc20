import { MigrationInterface, QueryRunner } from 'typeorm';

export class ActivityTypeEnumUpdate1750779372859 implements MigrationInterface {
	name = 'ActivityTypeEnumUpdate1750779372859';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."release_songs" DROP CONSTRAINT "FK_849a843900d1c80fa7b6c0fd45b"`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" DROP CONSTRAINT "FK_1e1842aa3640e5345a22098970a"`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ALTER COLUMN "inviteeId" SET NOT NULL`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."song" ALTER COLUMN "externalId" SET DEFAULT uuid_generate_v4()`,
		);
		await queryRunner.query(
			`ALTER TYPE "app"."activity_type_enum" RENAME TO "activity_type_enum_old"`,
		);
		await queryRunner.query(
			`CREATE TYPE "app"."activity_type_enum" AS ENUM('import.complete', 'song.created', 'song.created_with_invites', 'song.edited', 'song.renamed', 'song.archived', 'song.restored', 'song.lyrics.updated', 'song.recording.added', 'song.idea.created', 'project.joined', 'project.created', 'project.renamed', 'project.archived', 'project.restored', 'collaborator.invitation.sent', 'collaborator.invited.to.entity', 'user.joined.entity', 'user.left.entity', 'user.membership.plan.changed', 'user.profile.updated', 'release.created', 'release.scheduled', 'release.published')`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."activity" ALTER COLUMN "type" TYPE "app"."activity_type_enum" USING "type"::"text"::"app"."activity_type_enum"`,
		);
		await queryRunner.query(`DROP TYPE "app"."activity_type_enum_old"`);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ADD CONSTRAINT "FK_1e1842aa3640e5345a22098970a" FOREIGN KEY ("inviteeId") REFERENCES "app"."user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."release_songs" ADD CONSTRAINT "FK_849a843900d1c80fa7b6c0fd45b" FOREIGN KEY ("releaseId") REFERENCES "app"."release"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "app"."release_songs" DROP CONSTRAINT "FK_849a843900d1c80fa7b6c0fd45b"`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" DROP CONSTRAINT "FK_1e1842aa3640e5345a22098970a"`,
		);
		await queryRunner.query(
			`CREATE TYPE "app"."activity_type_enum_old" AS ENUM('song.created', 'song.created_with_invites', 'song.edited', 'song.renamed', 'song.archived', 'song.restored', 'song.lyrics.updated', 'song.recording.added', 'song.idea.created', 'project.joined', 'project.created', 'project.renamed', 'project.archived', 'project.restored', 'collaborator.invitation.sent', 'collaborator.invited.to.entity', 'user.joined.entity', 'user.left.entity', 'user.membership.plan.changed', 'user.profile.updated', 'release.created', 'release.scheduled', 'release.published')`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."activity" ALTER COLUMN "type" TYPE "app"."activity_type_enum_old" USING "type"::"text"::"app"."activity_type_enum_old"`,
		);
		await queryRunner.query(`DROP TYPE "app"."activity_type_enum"`);
		await queryRunner.query(
			`ALTER TYPE "app"."activity_type_enum_old" RENAME TO "activity_type_enum"`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."song" ALTER COLUMN "externalId" DROP DEFAULT`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."song" ALTER COLUMN "externalId" DROP DEFAULT`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ALTER COLUMN "inviteeId" DROP NOT NULL`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."invite_song" ADD CONSTRAINT "FK_1e1842aa3640e5345a22098970a" FOREIGN KEY ("inviteeId") REFERENCES "app"."user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."release_songs" ADD CONSTRAINT "FK_849a843900d1c80fa7b6c0fd45b" FOREIGN KEY ("releaseId") REFERENCES "app"."release"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
		);
	}
}
