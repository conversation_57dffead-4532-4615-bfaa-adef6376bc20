{"$schema": "./node_modules/nx/schemas/nx-schema.json", "tasksRunnerOptions": {"default": {"options": {"canTrackAnalytics": false, "showUsageWarnings": true}}}, "defaultProject": "web-app", "generators": {"@nx/angular:application": {"e2eTestRunner": "playwright", "linter": "eslint", "style": "scss", "unitTestRunner": "jest"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest"}, "@nx/angular:component": {"style": "scss", "type": "component"}, "@nx/angular": {"application": {"linter": "eslint"}, "library": {"linter": "eslint"}, "storybook-configuration": {"linter": "eslint"}, "directive": {"type": "directive"}, "service": {"type": "service"}, "scam": {"type": "component"}, "scam-directive": {"type": "directive"}, "guard": {"typeSeparator": "."}, "interceptor": {"typeSeparator": "."}, "module": {"typeSeparator": "."}, "pipe": {"typeSeparator": "."}, "resolver": {"typeSeparator": "."}}, "@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "@nx/esbuild:esbuild": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/webpack:webpack": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@angular-devkit/build-angular:browser": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "server": {"cache": true}, "@angular-devkit/build-angular:application": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "tui": {"enabled": false}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": ["{workspaceRoot}/angular.json", "{workspaceRoot}/tslint.json", "{workspaceRoot}/nx.json", "{workspaceRoot}/tsconfig.base.json"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s"]}, "parallel": 1, "useInferencePlugins": false, "defaultBase": "main"}