{"compileOnSave": false, "compilerOptions": {"allowJs": false, "strict": false, "rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "Node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "esModuleInterop": true, "resolveJsonModule": true, "target": "ES2022", "module": "CommonJS", "typeRoots": ["node_modules/@types"], "lib": ["ES2022", "DOM"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@major/marketing-app-shell": ["libs/marketing/app-shell/src/index.ts"], "@major/marketing/feature-about": ["libs/marketing/feature-about/src/index.ts"], "@major/marketing/feature-landing-page": ["libs/marketing/feature-landing-page/src/index.ts"], "@major/marketing/feature-payment": ["libs/marketing/feature-payment/src/index.ts"], "@major/marketing/ui": ["libs/marketing/ui/src/index.ts"], "@major/marketing/ui-payment-plan": ["libs/marketing/ui-payment-plan/src/index.ts"], "@major/marketing/ui-pricing": ["libs/marketing/ui-pricing/src/index.ts"], "@major/marketing/ui-privacy": ["libs/marketing/ui-privacy/src/index.ts"], "@major/marketing/ui-terms": ["libs/marketing/ui-terms/src/index.ts"], "@major/marketing/ui-testamonials": ["libs/marketing/ui-testamonials/src/index.ts"], "@major/shared-ui-assets": ["libs/shared/ui-assets/src/index.ts"], "@major/shared/component-styles": ["libs/shared/ui-stylesheets/utils/index.ts"], "@major/shared/components": ["apps/web-app/src/app/modules/shared/components/index.ts"], "@major/shared/core-providers": ["libs/shared/core-providers/src/index.ts"], "@major/shared/data-access-actions": ["libs/shared/data-access-actions/src/index.ts"], "@major/shared/data-factories": ["libs/shared/data-factories/src/index.ts"], "@major/shared/directives": ["apps/web-app/src/app/modules/shared/directives/index.ts"], "@major/shared/domain-document": ["libs/shared/domain-document/src/index.ts"], "@major/shared/domain-project": ["libs/shared/domain-project/src/index.ts"], "@major/shared/domain-song": ["libs/shared/domain-song/src/index.ts"], "@major/shared/feature-auth": ["libs/shared/feature-auth/src/index.ts"], "@major/shared/feature-auth/services": ["libs/shared/feature-auth/src/lib/services/index.ts"], "@major/shared/models": ["libs/shared/models/src/index.ts"], "@major/shared/models/classes": ["libs/shared/models/src/lib/classes/index.ts"], "@major/shared/models/enums": ["libs/shared/models/src/lib/enum/index.ts"], "@major/shared/models/interfaces": ["libs/shared/models/src/lib/interfaces/index.ts"], "@major/shared/models/literals": ["libs/shared/models/src/lib/literals/index.ts"], "@major/shared/pipes": ["apps/web-app/src/app/modules/shared/pipes/index.ts"], "@major/shared/ui-icon": ["libs/shared/ui-icon/src/index.ts"], "@major/shared/ui-markdown-section": ["libs/shared/ui-markdown-section/src/index.ts"], "@major/shared/ui-theme": ["libs/shared/ui-theme/src/index.ts"], "@major/shared/util": ["libs/shared/util/src/index.ts"], "@major/shared/util-animations": ["libs/shared/util-animations/src/index.ts"], "@major/shared/util-customer": ["libs/shared/util-customer/src/index.ts"], "@major/shared/util-data": ["libs/shared/util-data/src/index.ts"], "@major/shared/util-feature-flags": ["libs/shared/util-feature-flags/src/index.ts"], "@major/shared/util-firebase": ["libs/shared/util-firebase/src/index.ts"], "@major/shared/util-http": ["libs/shared/util-http/src/index.ts"], "@major/shared/util-pipes": ["libs/shared/util-pipes/src/index.ts"], "@major/shared/util-platform": ["libs/shared/util-platform/src/index.ts"], "@major/shared/util-quill": ["libs/shared/util-quill/src/index.ts"], "@major/shared/util-services": ["libs/shared/util-services/src/index.ts"], "@major/shared/util-stripe": ["libs/shared/util-stripe/src/index.ts"], "@major/shared/util-stripe/types": ["libs/shared/util-stripe/src/lib/types/index.ts"], "@major/shared/util-websocket": ["libs/shared/util-websocket/src/index.ts"], "@major/shared/util-websocket/types": ["libs/shared/util-websocket/src/lib/types/index.ts"], "@major/web-app": ["apps/web-app/src/app/index.ts"], "@major/web-app-backend/admin": ["libs/web-app-backend/admin/src/index.ts"], "@major/web-app-backend/archived": ["libs/web-app-backend/archived/src/index.ts"], "@major/web-app-backend/archived/entities": ["libs/web-app-backend/archived/src/archived-songs/entities/index.ts"], "@major/web-app-backend/boards/entities": ["libs/web-app-backend/ideas/src/boards/entities/index.ts"], "@major/web-app-backend/collaboration-messaging": ["libs/web-app-backend/collaboration-messaging/src/index.ts"], "@major/web-app-backend/collaboration/entities": ["libs/web-app-backend/collaborations/src/lib/types/entities/index.ts"], "@major/web-app-backend/collaboration/enum": ["libs/web-app-backend/collaborations/src/lib/types/enum/index.ts"], "@major/web-app-backend/collaborations": ["libs/web-app-backend/collaborations/src/index.ts"], "@major/web-app-backend/file-meta-data": ["libs/web-app-backend/storage/src/file-meta-data/index.ts"], "@major/web-app-backend/file-meta-data/entities": ["libs/web-app-backend/storage/src/file-meta-data/entities/index.ts"], "@major/web-app-backend/ideas": ["libs/web-app-backend/ideas/src/index.ts"], "@major/web-app-backend/ideas/entities": ["libs/web-app-backend/ideas/src/ideas/entities/index.ts"], "@major/web-app-backend/songs": ["libs/web-app-backend/songs/src/index.ts"], "@major/web-app-backend/songs/entities": ["libs/web-app-backend/songs/src/lib/entities/index.ts"], "@major/web-app-backend/storage": ["libs/web-app-backend/storage/src/index.ts"], "@major/web-app-backend/users": ["libs/web-app-backend/users/src/index.ts"], "@major/web-app-backend/users/entities": ["libs/web-app-backend/users/src/lib/types/entities/index.ts"], "@major/web-app-backend/users/services": ["libs/web-app-backend/users/src/index.ts"], "@major/web-app-backend/util-auth": ["libs/web-app-backend/util-auth/src/index.ts"], "@major/web-app-backend/util-auth/testing": ["libs/web-app-backend/util-auth/src/lib/testing/index.ts"], "@major/web-app-backend/util-testing": ["libs/web-app-backend/util-testing/src/index.ts"], "@major/web-app-backend/util-typeorm": ["libs/web-app-backend/util-typeorm/src/index.ts"], "@major/web-app-backend/util-websocket": ["libs/web-app-backend/util-websocket/src/index.ts"], "@major/web-app-backend/util-websocket/types": ["libs/web-app-backend/util-websocket/src/lib/types/index.ts"], "@major/web-app-backend/webhooks": ["libs/web-app-backend/webhooks/src/index.ts"], "@major/web-app-backend/webhooks/types": ["libs/web-app-backend/webhooks/src/logto/types/index.ts"], "@major/web-app/app-shell2": ["libs/web-app/app-shell2/src/index.ts"], "@major/web-app/app-shell2/feature-modal": ["libs/web-app/app-shell2/feature-modal/src/index.ts"], "@major/web-app/catalog/feature-release-notes": ["libs/web-app/catalog/feature-release-notes/src/index.ts"], "@major/web-app/catalog/feature-search": ["libs/web-app/catalog/feature-search/src/index.ts"], "@major/web-app/collaborations/domain": ["libs/web-app/collaborations/domain/src/index.ts"], "@major/web-app/collaborations/feature-list": ["libs/web-app/collaborations/feature-list/src/index.ts"], "@major/web-app/collaborations/shell": ["libs/web-app/collaborations/shell/src/index.ts"], "@major/web-app/dashboard/feature-dashboard": ["libs/web-app/dashboard/feature-dashboard/src/index.ts"], "@major/web-app/dashboard/shell": ["libs/web-app/dashboard/shell/src/index.ts"], "@major/web-app/data-access-store": ["libs/web-app/data-access-store/src/index.ts"], "@major/web-app/data-api": ["libs/web-app/data-api/src/index.ts"], "@major/web-app/editor/domain": ["libs/web-app/editor/domain/src/index.ts"], "@major/web-app/editor/feature-collaboration": ["libs/web-app/editor/feature-collaboration/src/index.ts"], "@major/web-app/editor/feature-dictionary": ["libs/web-app/editor/feature-dictionary/src/index.ts"], "@major/web-app/editor/feature-recorder": ["libs/web-app/editor/feature-recorder/src/index.ts"], "@major/web-app/editor/feature-song-editor": ["libs/web-app/editor/feature-song-editor/src/index.ts"], "@major/web-app/experiments/editor-form": ["libs/web-app/experiments/editor-form/src/index.ts"], "@major/web-app/experiments/feature-player": ["libs/web-app/experiments/feature-player/src/index.ts"], "@major/web-app/experiments/feature-websocket": ["libs/web-app/experiments/feature-websocket/src/index.ts"], "@major/web-app/experiments/shell": ["libs/web-app/experiments/shell/src/index.ts"], "@major/web-app/experiments/tiptap": ["libs/web-app/experiments/tiptap/src/index.ts"], "@major/web-app/ideas/feature-idea-edit": ["libs/web-app/ideas/feature-idea-edit/src/index.ts"], "@major/web-app/ideas/feature-list-ideas": ["libs/web-app/ideas/feature-list-ideas/src/index.ts"], "@major/web-app/onboarding/feature-onboarding": ["libs/web-app/onboarding/feature-onboarding/src/index.ts"], "@major/web-app/onboarding/feature-tour": ["libs/web-app/onboarding/feature-tour/src/index.ts"], "@major/web-app/projects/feature-projects": ["libs/web-app/projects/feature-projects/src/index.ts"], "@major/web-app/purchase/shell": ["libs/web-app/purchase/shell/src/index.ts"], "@major/web-app/session/domain": ["libs/web-app/session/domain/src/index.ts"], "@major/web-app/session/feature-detail": ["libs/web-app/session/feature-detail/src/index.ts"], "@major/web-app/session/feature-list": ["libs/web-app/session/feature-list/src/index.ts"], "@major/web-app/session/feature-new": ["libs/web-app/session/feature-new/src/index.ts"], "@major/web-app/session/feature-workspace": ["libs/web-app/session/feature-workspace/src/index.ts"], "@major/web-app/session/shell": ["libs/web-app/session/shell/src/index.ts"], "@major/web-app/settings/domain": ["libs/web-app/settings/domain/src/index.ts"], "@major/web-app/settings/feature-appearance": ["libs/web-app/settings/feature-appearance/src/index.ts"], "@major/web-app/settings/feature-billing": ["libs/web-app/settings/feature-billing/src/index.ts"], "@major/web-app/settings/feature-memberships": ["libs/web-app/settings/feature-memberships/src/index.ts"], "@major/web-app/settings/feature-profile": ["libs/web-app/settings/feature-profile/src/index.ts"], "@major/web-app/settings/feature-usage": ["libs/web-app/settings/feature-usage/src/index.ts"], "@major/web-app/settings/shell": ["libs/web-app/settings/shell/src/index.ts"], "@major/web-app/shared/labels": ["libs/web-app/shared/labels/src/index.ts"], "@major/web-app/shared/recording/feature-recording-list": ["libs/web-app/shared/recording/feature-recording-list/src/index.ts"], "@major/web-app/shared/recording/ui-waveform": ["libs/web-app/shared/recording/ui-waveform/src/index.ts"], "@major/web-app/shared/recording/ui/recording-name": ["libs/web-app/shared/recording/ui/recording-name/src/index.ts"], "@major/web-app/shared/song/feature-notes": ["libs/web-app/shared/song/feature-notes/src/index.ts"], "@major/web-app/shared/song/feature-print": ["libs/web-app/shared/song/feature-print/src/index.ts"], "@major/web-app/shared/song/feature-tasks": ["libs/web-app/shared/song/feature-tasks/src/index.ts"], "@major/web-app/signup/domain": ["libs/web-app/signup/domain/src/index.ts"], "@major/web-app/signup/feature-signup": ["libs/web-app/signup/feature-signup/src/index.ts"], "@major/web-app/signup/shell": ["libs/web-app/signup/shell/src/index.ts"], "@major/web-app/social/feature-friends": ["libs/web-app/social/feature-friends/src/index.ts"], "@major/web-app/social/feature-invite": ["libs/web-app/social/feature-invite/src/index.ts"], "@major/web-app/social/feature-shared-with-me": ["libs/web-app/social/feature-shared-with-me/src/index.ts"], "@major/web-app/social/feature-sharing": ["libs/web-app/social/feature-sharing/src/index.ts"], "@major/web-app/songs/domain": ["libs/web-app/songs/domain/src/index.ts"], "@major/web-app/songs/feature-authors": ["libs/web-app/songs/feature-authors/src/index.ts"], "@major/web-app/songs/feature-detail": ["libs/web-app/songs/feature-detail/src/index.ts"], "@major/web-app/songs/feature-import": ["libs/web-app/songs/feature-import/src/index.ts"], "@major/web-app/songs/feature-list": ["libs/web-app/songs/feature-list/src/index.ts"], "@major/web-app/songs/feature-recent": ["libs/web-app/songs/feature-recent/src/index.ts"], "@major/web-app/songs/feature-recording-detail": ["libs/web-app/songs/feature-recording-detail/src/index.ts"], "@major/web-app/songs/feature-song-recordings": ["libs/web-app/songs/feature-song-recordings/src/index.ts"], "@major/web-app/songs/shell": ["libs/web-app/songs/shell/src/index.ts"], "@major/web-app/types": ["libs/web-app/types/src/index.ts"], "@major/web-app/ui/avatar": ["libs/web-app/ui/avatar/src/index.ts"], "@major/web-app/ui/common": ["libs/web-app/ui/common/src/index.ts"], "@major/web-app/ui/directory": ["libs/web-app/ui/directory/src/index.ts"], "@major/web-app/ui/dropzone": ["libs/web-app/ui/dropzone/src/index.ts"], "@major/web-app/ui/form": ["libs/web-app/ui/form/src/index.ts"], "@major/web-app/ui/image": ["libs/web-app/ui/image/src/index.ts"], "@major/web-app/ui/indicators": ["libs/web-app/ui/indicators/src/index.ts"], "@major/web-app/ui/layout": ["libs/web-app/ui/layout/src/index.ts"], "@major/web-app/ui/material": ["libs/web-app/ui/material/src/index.ts"], "@major/web-app/user/domain": ["libs/web-app/user/domain/src/index.ts"], "@major/web-app/user/feature-user-feedback": ["libs/web-app/user/feature-user-feedback/src/index.ts"], "@major/web-app/user/feature-user-menu": ["libs/web-app/user/feature-user-menu/src/index.ts"], "@major/web-app/util/router": ["libs/web-app/util/router/src/index.ts"], "@major/web-app/util/services": ["libs/web-app/util/services/src/index.ts"], "@major/web-app/util/util-helpers": ["libs/web-app/util/util-helpers/src/index.ts"], "activity": ["libs/web-app-backend/activity/src/index.ts"], "beta-codes": ["libs/web-app-backend/beta-codes/src/index.ts"], "beta-signup": ["libs/web-app-backend/beta-signup/src/index.ts"], "pwa-domain": ["libs/web-app/pwa/domain/src/index.ts"], "domain-events": ["libs/web-app-backend/domain-events/src/index.ts"], "feature-activity": ["libs/web-app/shared/song/feature-activity/src/index.ts"], "feature-beta-signup": ["libs/web-app/signup/feature-beta-signup/src/index.ts"], "feature-collaborations-manager": ["libs/web-app/collaborations/feature-collaborations-manager/src/index.ts"], "feature-create-song": ["libs/web-app/songs/feature-create-song/src/index.ts"], "feature-song-activity": ["libs/web-app/songs/feature-song-activity/src/index.ts"], "feature-song-titles": ["libs/web-app/ideas/feature-song-titles/src/index.ts"], "mailer": ["libs/web-app-backend/mailer/src/index.ts"], "shared/ui-spotlight": ["libs/shared/ui-spotlight/src/index.ts"], "shared/util-file": ["libs/shared/util-file/src/index.ts"], "song-titles": ["libs/web-app-backend/song-titles/src/index.ts"], "ui": ["libs/web-app/shared/ui/src/index.ts"], "user-settings": ["libs/web-app-backend/user-settings/src/index.ts"], "util-posthog": ["libs/shared/util-posthog/src/index.ts"], "util-posthog-node": ["libs/web-app-backend/util-posthog-node/src/index.ts"], "util-telegram": ["libs/web-app-backend/util-telegram/src/index.ts"], "web-app-backend/application": ["libs/web-app-backend/application/src/index.ts"], "web-app-backend/assistant": ["libs/web-app-backend/assistant/src/index.ts"], "web-app-backend/cloud-convert": ["libs/web-app-backend/cloud-convert/src/index.ts"], "web-app-backend/cloud-convert/entities": ["libs/web-app-backend/cloud-convert/src/types/entities/index.ts"], "web-app-backend/collaborations-events": ["libs/web-app-backend/collaborations-events/src/index.ts"], "web-app-backend/collaborations-events/types": ["libs/web-app-backend/collaborations-events/src/lib/types/index.ts"], "web-app-backend/database": ["libs/web-app-backend/database/src/index.ts"], "web-app-backend/database-migrations": ["libs/web-app-backend/database-migrations/src/index.ts"], "web-app-backend/database-seed": ["libs/web-app-backend/database-seed/src/index.ts"], "web-app-backend/invites": ["libs/web-app-backend/invites/src/index.ts"], "web-app-backend/invites-events": ["libs/web-app-backend/invites-events/src/index.ts"], "web-app-backend/invites/entities": ["libs/web-app-backend/invites/src/lib/types/entities/index.ts"], "web-app-backend/invites/utils": ["libs/web-app-backend/invites/src/lib/utils/index.ts"], "web-app-backend/memberships": ["libs/web-app-backend/memberships/src/index.ts"], "web-app-backend/notifications": ["libs/web-app-backend/notifications/src/index.ts"], "web-app-backend/notifications/entities": ["libs/web-app-backend/notifications/src/lib/types/entities/index.ts"], "web-app-backend/payments": ["libs/web-app-backend/payments/src/index.ts"], "web-app-backend/payments/entities": ["libs/web-app-backend/payments/src/lib/types/entities/index.ts"], "web-app-backend/projects": ["libs/web-app-backend/projects/src/index.ts"], "web-app-backend/projects/entities": ["libs/web-app-backend/projects/src/lib/entities.ts"], "web-app-backend/redis": ["libs/web-app-backend/redis/src/index.ts"], "web-app-backend/types": ["libs/web-app-backend/types/src/index.ts"], "web-app-backend/user-quotas": ["libs/web-app-backend/user-quotas/src/index.ts"], "web-app-backend/util-config": ["libs/web-app-backend/util-config/src/index.ts"], "web-app-backend/util-logging": ["libs/web-app-backend/util-logging/src/index.ts"], "web-app-backend/util-security": ["libs/web-app-backend/util-security/src/index.ts"], "web-app-backend/util-sendgrid": ["libs/web-app-backend/util-sendgrid/src/index.ts"], "web-app/catalog/feature-archive": ["libs/web-app/catalog/feature-archive/src/index.ts"], "web-app/experiments/feature-peerjs": ["libs/web-app/experiments/feature-peerjs/src/index.ts"], "web-app/files/domain": ["libs/web-app/files/domain/src/index.ts"], "web-app/help/domain": ["libs/web-app/help/domain/src/index.ts"], "web-app/invites/domain": ["libs/web-app/invites/domain/src/index.ts"], "web-app/invites/feature-accept": ["libs/web-app/invites/feature-accept/src/index.ts"], "web-app/invites/shell": ["libs/web-app/invites/shell/src/index.ts"], "web-app/notifications/domain": ["libs/web-app/notifications/domain/src/index.ts"], "web-app/notifications/ui": ["libs/web-app/notifications/ui/src/index.ts"], "web-app/purchase/domain": ["libs/web-app/purchase/domain/src/index.ts"], "web-app/purchase/feature-choose-plan": ["libs/web-app/purchase/feature-choose-plan/src/index.ts"], "web-app/quotas/domain": ["libs/web-app/quotas/domain/src/index.ts"], "web-app/settings/feature-notifications": ["libs/web-app/settings/feature-notifications/src/index.ts"], "web-app/settings/feature-plan": ["libs/web-app/settings/feature-plan/src/index.ts"], "web-app/songs/feature-organise": ["libs/web-app/songs/feature-organise/src/index.ts"], "web-app/songs/feature-split-sheet": ["libs/web-app/songs/feature-split-sheet/src/index.ts"], "web-app/user/feature-notifications": ["libs/web-app/user/feature-notifications/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}