import { Modu<PERSON> } from '@nestjs/common';
import { FileMetaDataService } from './file-meta-data.service';
import { FileMetaDataController } from './file-meta-data.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileMetaDataEntity } from './entities/file-meta-data.entity';
import { WebAppBackendSongsModule } from '@major/web-app-backend/songs';
import { FileOrderEntity } from '@major/web-app-backend/songs/entities';
import { WebAppBackendStorageModule } from '../lib/web-app-backend-storage.module';
import { WebAppBackendUtilSecurityModule } from 'web-app-backend/util-security';
import { WebAppBackendUserQuotasModule } from 'web-app-backend/user-quotas';

@Module({
	imports: [
		TypeOrmModule.forFeature([FileMetaDataEntity, FileOrderEntity]),
		WebAppBackendSongsModule,
		WebAppBackendStorageModule,
		WebAppBackendUserQuotasModule,
		WebAppBackendUtilSecurityModule,
	],
	controllers: [FileMetaDataController],
	providers: [FileMetaDataService],
	exports: [FileMetaDataService],
})
export class FileMetaDataModule {}
