import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import {
	ConnectedSocket,
	MessageBody,
	SubscribeMessage,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';
import { Server, Socket } from 'socket.io';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { DomainEventEmitter } from 'domain-events';
import { WebSocketEvent } from 'libs/shared/util-websocket/src/lib/types/enums/websocket-event.enum';
import { instanceToPlain } from 'class-transformer';
import { EditorUserPresenceService } from 'libs/web-app-backend/util-websocket/src/lib/services/editor-user-presence.service';
import type { CurrentWebSocketUser } from '@major/web-app-backend/util-websocket/types';
import { JwtWsUser } from 'libs/web-app-backend/util-websocket/src/lib/decorators/ws-user.decorator';
import { CollaborationMessageEntity } from 'libs/web-app-backend/collaboration-messaging/src/lib/types/entities/collaboration-message.entity';
import {
	logColorPrimary,
	logColorSecondary,
} from 'libs/web-app-backend/util-logging/src/lib/utils/cli-colors';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { CollaborationMessageCreatedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration-messaging/collaboration-message-created.event';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class EditorChatGateway {
	private logger = new Logger(EditorChatGateway.name);

	@WebSocketServer()
	server!: Server;

	constructor(
		@InjectDataSource() private dataSource: DataSource,
		private domainEventEmitter: DomainEventEmitter,
		private editorUserPresenceService: EditorUserPresenceService,
	) {}

	private async emitUserPresence(songExternalId: string) {
		const users =
			await this.editorUserPresenceService.getUsersIdsOnlineForSong(
				songExternalId,
			);

		this.server
			.to(`song:${songExternalId}`)
			.emit(WebSocketEvent.EditorPresence, { data: users });

		this.logger.debug(
			`${logColorPrimary(WebSocketEvent.EditorPresence)} users=${users}`,
		);
	}

	/**
	 * When a user opens the chat feature, we broadcast the
	 * message history to them to populate the chat
	 */
	@SubscribeMessage(WebSocketEvent.ChatJoin)
	async chatJoin(
		@ConnectedSocket() client: Socket,
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody() payload: { songId: string },
	) {
		try {
			const messageRepo = this.dataSource.getRepository(
				CollaborationMessageEntity,
			);
			const messages = await messageRepo.find({
				where: {
					songExternalId: payload.songId,
				},
				order: {
					createdAt: 'ASC',
				},
				relations: ['user'],
			});

			// send the chat history
			client.emit(WebSocketEvent.ChatHistory, {
				event: WebSocketEvent.ChatHistory,
				data: instanceToPlain(messages),
			});

			// send the user presence details
			await this.editorUserPresenceService.setUserPresent(
				payload.songId,
				user.userId,
			);
			await this.emitUserPresence(payload.songId);

			this.logger.debug(
				`${logColorPrimary(WebSocketEvent.ChatHistory)} ${logColorSecondary(
					user.email,
				)} songId=${payload.songId}`,
			);

			return { status: 'success', message: `Joined chat ${payload.songId}` };
		} catch (e) {
			this.logger.error('chatJoin()', e);
			return {
				status: 'error',
				message: `Failed to join chat ${payload.songId}`,
			};
		}
	}

	@SubscribeMessage(WebSocketEvent.ChatMessage)
	async chatMessage(
		@JwtWsUser() user: CurrentWebSocketUser,
		@MessageBody()
		payload: { content: string; songId: string },
	) {
		// update user presence
		await this.editorUserPresenceService.setUserPresent(
			payload.songId,
			user.userId,
		);

		await this.emitUserPresence(payload.songId);

		this.logger.debug(
			`${logColorPrimary(WebSocketEvent.ChatMessage)} ${logColorSecondary(
				user.email,
			)}: songId=${payload.songId}`,
		);

		const userEntityRepo = this.dataSource.getRepository(UserEntity);
		const messageRepo = this.dataSource.getRepository(
			CollaborationMessageEntity,
		);
		const songRepo = this.dataSource.getRepository(SongEntity);

		const userEntity = await userEntityRepo.findOneOrFail({
			where: { id: user.id },
			relations: [],
		});

		const songEntity = await songRepo.findOneOrFail({
			where: { externalId: payload.songId },
			relations: [],
		});

		const message = messageRepo.create({
			user: userEntity,
			content: payload.content,
			song: songEntity,
			songExternalId: songEntity.externalId,
		});

		try {
			await messageRepo.save(message, {
				reload: true,
			});

			// success now emit event
			this.domainEventEmitter.emitEvent(
				new CollaborationMessageCreatedEvent({
					actionUserId: user.id,
					messageId: message.id,
					songId: songEntity.id,
					timestamp: message.createdAt.getTime(),
				}),
			);

			this.server
				.to(`song:${payload.songId}`)
				.emit(WebSocketEvent.ChatMessage, {
					event: WebSocketEvent.ChatMessage,
					data: instanceToPlain(message),
				});
		} catch (e) {
			this.logger.error(e);
		}
	}
}
