import { Entity<PERSON>anager, Repository } from 'typeorm';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { SongEntity } from '../entities/song.entity';
import { Injectable } from '@nestjs/common';
import { CreateSongDto } from '../dto/create-song.dto';
import { CollaborationEntity } from 'libs/web-app-backend/collaborations/src/lib/types/entities';
import { UserEntity } from 'libs/web-app-backend/users/src/lib/types/entities';
import { CollaborationPermissionLevel } from '@major/web-app-backend/collaboration/enum';
import { v4 as uuidv4 } from 'uuid';
import { CollaborationStatus } from '@major/shared/models';

/**
 * "Create a new song collaboration"
 *
 * This is required for creating a new song.
 * Because all associations with a song, event the owner's association,
 * all are counted as a collaboration.
 *
 * So, the user that first creates the song will have created both
 * a new Song, and a relation to a Collaboration, where the user
 * is both inviter AND invitee.
 */
@Injectable()
export class SongCollaborationService {
  constructor(
    @InjectRepository(SongEntity)
    private readonly songEntityRepository: Repository<SongEntity>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {}

  /**
   * Creates a new document and establishes an initial collaboration record for the owner.
   * Use a transaction to ensure both Song and Collaboration are created together.
   *
   * @param {number} ownerId - The user creating the song
   * @param {CreateSongDto} createSongDto - Initial data to create song with
   * @returns {Promise<SongEntity>} The newly created document with its collaboration setup.
   */
  create(ownerId: number, createSongDto: CreateSongDto): Promise<SongEntity> {
    return this.entityManager.transaction(async (transactionalEntityManager) => {
      const owner = await transactionalEntityManager.findOne(UserEntity, {
        where: { id: ownerId },
      });

      if (!owner) {
        throw new Error('Owner not found');
      }

      // Create new document
      const song = this.songEntityRepository.create(createSongDto);

      // inside the transaction the @BeforeInsert hook will not be called
      song.externalId = uuidv4();
      song.owner = owner;

      const savedSong = await transactionalEntityManager.save(song);

      // Create collaboration record for the owner
      const collaboration = new CollaborationEntity();
      collaboration.song = savedSong;
      collaboration.inviter = owner; // the owner is both inviter and invitee for the initial record
      collaboration.invitee = owner;
      collaboration.inviteeEmail = owner.email; // assuming the User entity has an email field
      collaboration.status = CollaborationStatus.Accepted; // Automatically accept for the owner
      collaboration.permissionLevel = CollaborationPermissionLevel.Edit; // Full permissions for the owner
      // collaboration.isTrashed = false; // Not in the trash initially

      await transactionalEntityManager.save(collaboration);

      return savedSong;
    });
  }
}
