import {
	Body,
	Controller,
	ForbiddenException,
	Logger,
	NotFoundException,
	Post,
	UseGuards,
} from '@nestjs/common';
import { CollaboratorRemovalService } from '../services/collaborator-removal.service';
import {
	CurrentJwtUser,
	JwtAuthGuard,
	JwtUser,
} from '@major/web-app-backend/util-auth';
import { RemovalOptions } from '@major/shared/models/interfaces';
import { CollaborationEntityService } from '../services/collaboration-entity.service';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DomainEventEmitter } from 'domain-events';
import { CollaborationLeftEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-left.event';
import { CollaborationRemovedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/collaboration/collaboration-removed.event';

@Controller('collaborations')
@UseGuards(JwtAuthGuard)
export class CollaboratorRemovalController {
	private logger = new Logger(CollaboratorRemovalController.name);

	constructor(
		@InjectRepository(SongEntity)
		private readonly songEntityRepository: Repository<SongEntity>,
		private readonly collaboratorRemovalService: CollaboratorRemovalService,
		private readonly collaborationEntityService: CollaborationEntityService,
		private domainEventEmitter: DomainEventEmitter,
	) {}

	@Post('remove')
	async removeCollaborator(
		@Body() options: RemovalOptions,
		@JwtUser() user: CurrentJwtUser,
	) {
		// Verify song ownership
		const song = await this.songEntityRepository.findOneBy({
			externalId: options.songExternalId,
			owner: {
				id: user.id,
			},
		});

		if (!song) {
			throw new ForbiddenException(
				'Only the song owner can remove collaborators',
			);
		}

		const collaboration =
			await this.collaborationEntityService.findOneBySongIdAndInviteeExternalId(
				song.id,
				user.id,
				options.userExternalId,
			);

		if (!collaboration) {
			throw new ForbiddenException(
				'Collaboration not found or you do not have permission to remove it',
			);
		}

		await this.collaboratorRemovalService.removeCollaboration(
			collaboration.id,
			options,
		);

		//
		// Emit event
		//
		this.domainEventEmitter.emitEvent(
			new CollaborationRemovedEvent({
				collaboration,
				songId: collaboration.song.id,
				actionUserId: user.id,
				timestamp: Date.now(),
			}),
		);
	}

	@Post('leave')
	async leaveCollaboration(
		@Body() options: RemovalOptions,
		@JwtUser() user: CurrentJwtUser,
	) {
		// Verify frontend is sending correct user info (contract validation)
		if (options.userExternalId !== user.userId) {
			throw new ForbiddenException(
				'You can only leave collaborations for yourself',
			);
		}

		// Get the song first
		const song = await this.songEntityRepository.findOneBy({
			externalId: options.songExternalId,
		});

		if (!song) {
			throw new NotFoundException('Song not found');
		}

		console.log(
			'Find collaboration where songId=%s, inviterId=%s, inviteeId=%s',
			song.id,
			user.id,
			options.userExternalId,
		);

		// Find collaboration using only the JWT user info since that's our source of truth
		const collaboration =
			await this.collaborationEntityService.findOneBySongIdAndInviteeExternalId(
				song.id,
				user.id,
				options.userExternalId,
			);

		if (!collaboration) {
			throw new ForbiddenException('You are not a collaborator on this song');
		}

		this.logger.debug(`Got collaboration ${JSON.stringify(collaboration)}`);

		// Use JWT user info for the actual operation
		await this.collaboratorRemovalService.leaveCollaboration(collaboration.id, {
			...options,
			userExternalId: user.userId, // Ensure we use the JWT user info
		});

		//
		// Emit event
		//
		this.domainEventEmitter.emitEvent(
			new CollaborationLeftEvent({
				collaboration,
				actionUserId: user.id,
				songId: collaboration.song.id,
				timestamp: Date.now(),
			}),
		);
	}
}
