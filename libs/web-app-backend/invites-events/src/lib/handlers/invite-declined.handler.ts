import { Injectable } from '@nestjs/common';
import { InviteEventHandlerBase } from './invite-event-handler.base';
import { OnEvent } from '@nestjs/event-emitter';
import { RefreshScope } from '@major/shared/models';
import { InviteDeclinedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-declined.event';

@Injectable()
export class InviteDeclinedHandler extends InviteEventHandlerBase {
	/**
	 * @param event
	 */
	@OnEvent(InviteDeclinedEvent.eventName)
	async onInviteDeclined(event: InviteDeclinedEvent) {
		const { invite } = event.payload;

		await this.markInviteeNotificationAsRead(invite);

		const declinedNotification =
			await this.notificationFactory.createInviteDeclinedNotification(invite);

		await this.notificationProducer.addDeliveryJob(declinedNotification);

		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.INVITES_SENT,
			targetUserIds: [invite.inviter.id],
		});

		this.refreshMessageService.notifyRefreshNeeded({
			scope: RefreshScope.INVITES_BY_SONG,
			targetUserIds: [invite.inviter.id],
			context: {
				id: invite.song.externalId,
			},
		});

		this.logger.debug(`Invite declined id=${invite.id}`);
	}
}
