import { User } from './user.interface';
import { Song } from './song.interface';

export interface FileMetaData {
	id: string;
	displayName: string;
	song: Song;
	fileName: string;
	mimeType: string;
	key: string;
	owner: User;
	ownerExternalId?: string;
	ownerPhotoUrl?: string;
	ownerDisplayName?: string;
	currentUserIsOwner?: boolean;
	durationSeconds: number;
	size: number;
	downloadUrl?: string | null;
	downloadUrlExpiresAt?: string | Date | null;
	fileCreatedAt: string | Date;
	updatedAt: string | Date;
	createdAt: string | Date;
}

export type CreateFileMetaDataDto = Omit<
	FileMetaData,
	'id' | 'owner' | 'song' | 'createdAt' | 'updatedAt'
> & {
	songId: string;
};
