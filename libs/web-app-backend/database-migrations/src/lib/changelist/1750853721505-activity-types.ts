import { MigrationInterface, QueryRunner } from 'typeorm';

export class ActivityTypes1750853721505 implements MigrationInterface {
	name = 'ActivityTypes1750853721505';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TYPE "app"."activity_type_enum" RENAME TO "activity_type_enum_old"`,
		);
		await queryRunner.query(
			`CREATE TYPE "app"."activity_type_enum" AS ENUM('import.complete', 'song.created', 'song.created_with_invites', 'song.edited', 'song.renamed', 'song.archived', 'song.restored', 'song.lyrics.updated', 'song.recording.added', 'song.idea.created', 'invite.created', 'invite.accepted', 'invite.declined', 'project.created', 'project.renamed', 'collaborator.invitation.sent', 'collaborator.left', 'collaborator.removed', 'collaborator.invited.to.entity', 'user.membership.plan.changed', 'user.profile.updated', 'release.created', 'release.scheduled', 'release.published')`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."activity" ALTER COLUMN "type" TYPE "app"."activity_type_enum" USING "type"::"text"::"app"."activity_type_enum"`,
		);
		await queryRunner.query(`DROP TYPE "app"."activity_type_enum_old"`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`CREATE TYPE "app"."activity_type_enum_old" AS ENUM('import.complete', 'song.created', 'song.created_with_invites', 'song.edited', 'song.renamed', 'song.archived', 'song.restored', 'song.lyrics.updated', 'song.recording.added', 'song.idea.created', 'project.joined', 'project.created', 'project.renamed', 'project.archived', 'project.restored', 'collaborator.invitation.sent', 'collaborator.invited.to.entity', 'user.joined.entity', 'user.left.entity', 'user.membership.plan.changed', 'user.profile.updated', 'release.created', 'release.scheduled', 'release.published')`,
		);
		await queryRunner.query(
			`ALTER TABLE "app"."activity" ALTER COLUMN "type" TYPE "app"."activity_type_enum_old" USING "type"::"text"::"app"."activity_type_enum_old"`,
		);
		await queryRunner.query(`DROP TYPE "app"."activity_type_enum"`);
		await queryRunner.query(
			`ALTER TYPE "app"."activity_type_enum_old" RENAME TO "activity_type_enum"`,
		);
	}
}
