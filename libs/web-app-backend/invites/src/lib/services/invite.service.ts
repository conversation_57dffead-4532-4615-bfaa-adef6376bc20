import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { CurrentJwtUser } from '@major/web-app-backend/util-auth';
import { CollaborationPermissionLevel } from '@major/web-app-backend/collaboration/enum';
import { CollaborationStatus, InvitationStatus } from '@major/shared/models';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';
import { DomainEventEmitter, InviteAcceptedEvent } from 'domain-events';
import { InviteDeclinedEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-declined.event';
import { InviteDeclinedPayload } from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-declined.payload';
import { InviteCancelledEvent } from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-cancelled.event';
import { InviteCancelledPayload } from 'libs/web-app-backend/domain-events/src/lib/types/events/invite-song/invite-cancelled.payload';
import { UserEntity } from '@major/web-app-backend/users/entities';

@Injectable()
export class InviteService {
	private logger = new Logger(InviteService.name);

	constructor(
		@InjectRepository(InviteSongEntity)
		private repository: Repository<InviteSongEntity>,
		@InjectEntityManager() private entityManager: EntityManager,
		private domainEventEmitter: DomainEventEmitter,
	) {}

	/**
	 * Accepts an invitation, creates the collaboration, and emits the domain event.
	 * This method is responsible for fetching its own data.
	 *
	 * If CollaborationEntity is removed from this code
	 * or not updated in the same way, then UserCountsSubscriber.getUserId()
	 * will need changing to work (probably with InviteSongEntity instead)	 * @param inviteId The ID of the invite to accept.
	 * @see UserCountsSubscriber.getUserId()
	 *
	 * @param user The user accepting the invite.
	 * @returns The updated and fully-loaded InviteSongEntity.
	 */
	async accept(
		inviteId: string,
		user: CurrentJwtUser,
	): Promise<InviteSongEntity> {
		let acceptedInvite: InviteSongEntity;

		await this.entityManager.transaction(async (manager) => {
			// 1. The service now fetches its own data using the ID.
			const invite = await manager.findOne(InviteSongEntity, {
				where: { id: inviteId },
				// Load all relations needed for the transaction and the event payload.
				relations: ['song', 'inviter', 'invitee'],
			});

			if (!invite) {
				// It's good practice for the service to guard against invalid IDs.
				throw new NotFoundException(`Invite with ID ${inviteId} not found.`);
			}

			// Create collaboration...
			const collaboration = manager.create(CollaborationEntity, {
				invite,
				songId: invite.song.id,
				inviteeId: user.id,
				inviterId: invite.inviter.id,
				permissionLevel: CollaborationPermissionLevel.Edit,
				status: CollaborationStatus.Accepted,
			});

			// Update invite status and link the accepting user
			invite.status = InvitationStatus.Accepted;
			invite.acceptedAt = new Date();
			invite.collaboration = collaboration;

			// user did not exist when invite was created, invitee will be null
			// fetch the user who has by now signed up to accept invite
			// and attach them to the invite
			if (!invite.invitee) {
				this.logger.debug(
					`Invitee user ${invite.getInviteeEmail()} not found, fetching...`,
				);

				try {
					const invitee = await this.entityManager.findOneByOrFail(UserEntity, {
						email: invite.getInviteeEmail(),
					});

					invite.invitee = invitee;

					this.logger.debug(
						`Invitee user id=${invitee.id} (${invitee.email}) found`,
					);
				} catch (e) {
					this.logger.warn(
						`Could not find new invitee user ${invite.getInviteeEmail()}`,
					);
				}
			}

			this.logger.debug(
				'Setting invite status to accepted and creating collaboration...',
			);

			await manager.save(collaboration);
			await manager.save(invite);

			// Fetch all collaborations for the song to include in the event
			const collaborations = await manager
				.createQueryBuilder(CollaborationEntity, 'collaboration')
				.leftJoin('collaboration.song', 'song')
				.leftJoinAndSelect('collaboration.inviter', 'inviter')
				.leftJoinAndSelect('collaboration.invitee', 'invitee')
				.where('song.id = :songId', { songId: invite.song.id })
				.getMany();

			invite.song.collaborations = collaborations;

			type FatPayloadEvent = InviteAcceptedEvent['payload']['invite'];

			// Emit event
			const event = new InviteAcceptedEvent({
				invite: invite as FatPayloadEvent,
			});

			this.domainEventEmitter.emitEvent(event);

			// Assign to the outer scope variable to be returned after the transaction commits.
			acceptedInvite = invite;
		});

		// The exclamation mark asserts that acceptedInvite will be assigned.
		return acceptedInvite!;
	}

	/**
	 * Only call this after validating the user is the invitee
	 *
	 * @param inviteId {string}
	 */
	async decline(inviteId: string) {
		const invite = await this.repository.findOne({
			where: { id: inviteId },
			relations: ['song', 'inviter', 'invitee'],
		});

		if (!invite) {
			throw new NotFoundException(`Invite id=${inviteId} not found`);
		}

		invite.status = InvitationStatus.Declined;
		invite.declinedAt = new Date();

		await this.repository.save(invite);

		type FatPayload = InviteDeclinedEvent['payload']['invite'];

		const payload: InviteDeclinedPayload = {
			invite: invite as FatPayload,
		};

		const event = new InviteDeclinedEvent(payload);

		this.domainEventEmitter.emitEvent(event);

		return invite;
	}

	async cancel(inviteId: string) {
		const invite = await this.repository.findOne({
			where: { id: inviteId },
			relations: ['song', 'inviter', 'invitee'],
		});

		if (!invite) {
			throw new NotFoundException(`Invite id=${inviteId} not found`);
		}

		invite.status = InvitationStatus.Cancelled;
		invite.cancelledAt = new Date();

		await this.repository.save(invite);

		type FatPayload = InviteCancelledEvent['payload']['invite'];

		const payload: InviteCancelledPayload = {
			invite: invite as FatPayload,
		};

		const event = new InviteCancelledEvent(payload);

		this.domainEventEmitter.emitEvent(event);

		return invite;
	}
}
