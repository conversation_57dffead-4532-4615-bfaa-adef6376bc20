import {
	ChangeDetectionStrategy,
	Component,
	HostListener,
	Inject,
	inject,
	OnDestroy,
	OnInit,
} from '@angular/core';
import { WindowRef } from '@major/web-app/util/services';
import { RouterOutlet } from '@angular/router';
import { AppBootstrapService } from './services/app-bootstrap.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { firstValueFrom, lastValueFrom, merge } from 'rxjs';
import { AppShellFacade } from '@major/web-app/app-shell2';
import { WebAppPurchaseFeatureChoosePlanComponent } from 'libs/web-app/purchase/feature-choose-plan/src/lib/web-app-purchase-feature-choose-plan/web-app-purchase-feature-choose-plan.component';
import { takeUntil, tap } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'web-app-root',
	templateUrl: './app.component.html',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [RouterOutlet],
})
export class AppComponent2 implements OnInit, OnDestroy {
	private readonly appBootstrapService = inject(AppBootstrapService);
	private readonly oidcSecurityService = inject(OidcSecurityService);
	private readonly appShellFacade = inject(AppShellFacade);
	private matDialog = inject(MatDialog);

	openPurchase$ = this.appShellFacade.onPurchase$.pipe(
		tap(() => this.openPurchaseModal()),
	);

	@HostListener('window:beforeunload')
	unloadHandler() {
		this.appBootstrapService.shutdown();
	}

	constructor(
		@Inject('environment.production') public isProd: boolean,
		private windowRef: WindowRef,
	) {
		// so we can target the existing tab when opening links from emails (by using target="major")
		this.windowRef.window.name = 'major';

		this.openPurchase$.pipe(takeUntilDestroyed()).subscribe();
	}

	async ngOnInit() {
		// await this.fixStateErrorBetweenAuthOidcClientAndKeycloak();
		await lastValueFrom(this.oidcSecurityService.checkAuth());
		await this.appBootstrapService.bootApp();
	}

	ngOnDestroy() {
		this.appBootstrapService.shutdown();
	}

	/**
	 * See https://github.com/damienbod/angular-auth-oidc-client/issues/1721#issuecomment-2410575436 for more details
	 */
	private async fixStateErrorBetweenAuthOidcClientAndKeycloak(): Promise<void> {
		const url = new URL(document.defaultView.location.toString());
		const urlState = url.searchParams.get('state');

		if (!urlState) {
			return;
		}

		const oidcState = await firstValueFrom(this.oidcSecurityService.getState());

		if (oidcState !== urlState) {
			this.oidcSecurityService.authorize();
		}
	}

	private async openPurchaseModal() {
		const ref = this.matDialog.open(WebAppPurchaseFeatureChoosePlanComponent, {
			backdropClass: 'backdrop-blur',
		});

		ref
			.componentRef!.instance.done.pipe(
				tap(() => ref.close()),
				takeUntil(merge(ref.afterClosed())),
			)
			.subscribe();
	}
}
