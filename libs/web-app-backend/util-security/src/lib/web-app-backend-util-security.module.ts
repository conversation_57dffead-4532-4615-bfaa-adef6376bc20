import { Module } from '@nestjs/common';
import { HmacService } from './services/hmac.service';
import { UrlSafePayloadService } from './services/url-safe-payload.service';
import {
	INVITE_SECRET_KEY,
	provideConfigValue,
} from 'web-app-backend/util-config';
import { HMAC_SECRET } from '@major/web-app-backend/util-auth';

@Module({
	providers: [
		provideConfigValue(HMAC_SECRET, 'HMAC_SECRET'),
		provideConfigValue(INVITE_SECRET_KEY, 'INVITE_SECRET_KEY'),
		HmacService,
		UrlSafePayloadService,
	],
	exports: [HmacService, UrlSafePayloadService, HMAC_SECRET],
})
export class WebAppBackendUtilSecurityModule {}
