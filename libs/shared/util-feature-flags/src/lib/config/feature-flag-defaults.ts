import { FeatureFlag } from '../enum/remote-config-values.enum';

/**
 * RemoteConfig is not the best name, should be called FeatureFlag perhaps
 * but it will relate to Firebase remote config variable names.
 *
 * So, "feature flags" in major actually map to firebase 'Remote Config' values
 */
export const featureFlagDefaults = {
	// always true
	[FeatureFlag.feature_quotas_enabled]: true,
	[FeatureFlag.purchase_enabled]: false,
	[FeatureFlag.signup_enabled]: true,
	[FeatureFlag.beta_signup_enabled]: false,
	[FeatureFlag.feature_scratchpad_enabled]: false,

	// Ready but disabled for now
	[FeatureFlag.feature_memberships_enabled]: false,
	[FeatureFlag.usernames_enabled]: false,

	// WIP features / experimental / almost ready
	[FeatureFlag.feature_player_enabled]: false,
	[FeatureFlag.editor_calls_enabled]: false,
	[FeatureFlag.feature_search_enabled]: false,
	[FeatureFlag.feature_split_sheet_enabled]: false,
	[FeatureFlag.feature_starred_enabled]: false,
	[FeatureFlag.features_tasks_enabled]: false,
	[FeatureFlag.labels_enabled]: false,
	[FeatureFlag.organise_enabled]: false,
	[FeatureFlag.pseudonyms_enabled]: false,

	// deprecated
	[FeatureFlag.feature_messages_enabled]: false,
	[FeatureFlag.convert_song_idea]: false,
	[FeatureFlag.feature_friends_enabled]: false,
	[FeatureFlag.features_recordings_enabled]: false,
	[FeatureFlag.feature_sharing_enabled]: false,
};
