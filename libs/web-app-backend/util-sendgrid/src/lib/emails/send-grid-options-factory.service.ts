import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { InviteReceivedEmail } from './invite-received.email';
import { ModuleRef } from '@nestjs/core';
import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isInviteSongShape } from 'libs/web-app-backend/mailer/src/lib/utils/is-shape-invite-song';
import { isCollaborationShape } from '../utils/is-shape-collaboration';

/**
 * @deprecated
 */
@Injectable()
export class SendGridOptionsFactory {
	private logger = new Logger(SendGridOptionsFactory.name);

	constructor(private moduleRef: ModuleRef) {}

	getEmailOptionsFor(doc: unknown) {
		if (isInviteSongShape(doc)) {
			const inviteSongEntity = plainToInstance(InviteSongEntity, doc, {
				excludeExtraneousValues: false,
			});

			const email = this.moduleRef.get(InviteReceivedEmail);
			return email.getSendGridSendOptions(inviteSongEntity);
		}

		if (isCollaborationShape(doc)) {
			this.logger.warn(
				`Cannot build email options for Collaboration ${JSON.stringify(doc)}`,
			);
		}

		throw new Error('Cannot create sendgrid options for unknown document type');
	}
}
