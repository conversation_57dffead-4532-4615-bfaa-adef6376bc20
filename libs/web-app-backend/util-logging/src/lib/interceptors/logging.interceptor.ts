import {
	CallHandler,
	ExecutionContext,
	Injectable,
	Logger,
	NestInterceptor,
} from '@nestjs/common';
import { catchError, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
	logColorEmphasis,
	logColorError,
	logColorPrimary,
	logColorSecondary,
	logColorSuccess,
} from '../utils/cli-colors';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
	private logger!: Logger;

	intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
		const contextType = context.getType<'http' | 'stripe_webhook'>();

		// If this is a Stripe webhook event then allow it
		if (contextType === 'stripe_webhook') {
			return next.handle();
		}

		const httpContext = context.switchToHttp();
		const contextName = context.getClass().name;

		this.logger = new Logger(contextName);

		const req = httpContext.getRequest();
		const res = httpContext.getResponse();
		const now = Date.now();

		if (req.url.indexOf('/health') !== -1) {
			return next.handle();
		}

		return next.handle().pipe(
			tap(() => {
				const responseTime = Date.now() - now;

				const line = [
					// logColorSuccess('[Http]'),
					// logColorSecondary(new Date().toISOString()),
					logColorEmphasis(req.method.toUpperCase()),
					logColorPrimary(req.url),
					['4', '5'].includes(res.statusCode.toString().charAt(0))
						? logColorError(res.statusCode)
						: logColorSuccess(res.statusCode),
					logColorSecondary(`${responseTime}ms`),
					logColorSecondary(
						req.body
							? Object.keys(req.body).length !== 0
								? JSON.stringify(req.body)
								: ''
							: '',
					),
				];

				if (req.user) {
					const identifier =
						process.env['NODE_ENV'] === 'production'
							? req.user.id
							: req.user.email;

					// put at the beginning of the line
					line.unshift(logColorSecondary(identifier));
				}

				// console.log(line.join(' '));
				this.logger.log(line.join(' '));
			}),
			catchError((error) => {
				const responseTime = Date.now() - now;

				const line = [
					// logColorError('[Http]'),
					// logColorSecondary(new Date().toISOString()),
					logColorEmphasis(req.method.toUpperCase()),
					logColorPrimary(req.url),
					logColorError(res.statusCode),
					logColorSecondary(`${responseTime}ms`),
					req.body
						? logColorSecondary(
								Object.keys(req.body).length !== 0
									? JSON.stringify(req.body)
									: '',
							)
						: '',
					logColorError(error.message),
				];

				if (req.user) {
					const identifier =
						process.env['NODE_ENV'] === 'production'
							? req.user.id
							: req.user.email;

					line.unshift(logColorPrimary(identifier));
				}

				// console.log(line.join(' '));
				this.logger.error(line.join(' '));

				throw error;
			}),
		);
	}
}
