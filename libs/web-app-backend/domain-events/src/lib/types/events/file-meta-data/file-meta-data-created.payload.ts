import { FileMetaDataEntity } from '@major/web-app-backend/file-meta-data/entities';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';

export interface FileMetaDataCreatedPayload {
	songId: number;
	actionUserId: number;
	recordingId: string;
	file: FileMetaDataEntity & {
		song: SongEntity & { collaborations: CollaborationEntity[] };
	} & { owner: UserEntity };
	timestamp: number;
}
