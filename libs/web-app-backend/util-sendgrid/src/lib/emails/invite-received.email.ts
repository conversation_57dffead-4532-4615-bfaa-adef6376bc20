import { SendGridTemplateId } from '../types/enums/sendgrid-template-id.enum';
import { InviteSongEntity } from 'web-app-backend/invites/entities';
import { Inject, Injectable } from '@nestjs/common';
import {
	WEB_APP_BASE_URL,
	WEB_APP_PATH_INVITE_LANDING,
} from 'web-app-backend/util-config';
import { SendGridSendOptions } from '../types/send-grid-send-options.interface';
import { getInviteeEmail } from 'web-app-backend/invites/utils';
import { UrlSafePayloadService } from 'web-app-backend/util-security';

/**
 * @deprecated
 */
@Injectable()
export class InviteReceivedEmail {
	constructor(
		@Inject(WEB_APP_PATH_INVITE_LANDING) private inviteBaseUrl: string,
		@Inject(WEB_APP_BASE_URL) private webAppBaseUrl: string,
		private urlSafePayloadService: UrlSafePayloadService,
	) {}

	getSendGridSendOptions(doc: InviteSongEntity): SendGridSendOptions {
		const templateId = doc.inviteeEmail
			? SendGridTemplateId.InviteCoWriter
			: SendGridTemplateId.InviteExistingUserToCoWrite;

		const urlPayload = doc.getDataForInviteUrl();

		const url = this.urlSafePayloadService.createUrlSafePayload(urlPayload);

		return {
			recipient: { email: getInviteeEmail(doc) },
			templateId,
			templateData: {
				senderName: doc.inviter.displayName ?? 'A friend',
				songTitle: doc.song.title ?? 'Untitled Song',
				acceptUrl: `${this.inviteBaseUrl}/${url}`,
				manageUrl: `${this.webAppBaseUrl}/dashboard`,
			},
		};
	}
}
