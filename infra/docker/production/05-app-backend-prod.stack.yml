# infra/docker/production/05-app-backend-prod.stack.yml
version: '3.8'

networks:
  traefik-public:
    external: true
    name: traefik-public
  major-internal:
    # Connect to the network defined in major-data stack
    external: true
    name: major-internal

# Define all secrets required by the backend
secrets:
  database_password: { external: true }
  database_host: { external: true }
  database_port: { external: true }
  database_username: { external: true }
  database_name: { external: true }
  database_schema: { external: true }
  redis_host: { external: true }
  redis_port: { external: true }
  redis_password: { external: true }
  cloudflare_r2_access_key_id: { external: true }
  cloudflare_r2_secret_access_key: { external: true }
  cloudflare_r2_account_id: { external: true }
  cloudflare_r2_api_token: { external: true }
  cloudflare_r2_bucket_name: { external: true }
  cloudflare_r2_endpoint: { external: true }
  cloudflare_r2_avatars_bucket_name: { external: true }
  cloudflare_r2_avatars_bucket_url: { external: true }
  cloud_convert_access_token: { external: true }
  cloud_convert_webhook_secret_job_created: { external: true }
  cloud_convert_webhook_secret_job_failed: { external: true }
  cloud_convert_webhook_secret_job_finished: { external: true }
  hmac_secret: { external: true }
  invite_secret_key: { external: true }
  logto_url: { external: true }
  logto_endpoint: { external: true }
  logto_admin_endpoint: { external: true }
  logto_openid_provider_endpoint: { external: true }
  logto_authorization_endpoint: { external: true }
  logto_token_endpoint: { external: true }
  logto_userinfo_endpoint: { external: true }
  logto_m2m_app_id: { external: true }
  logto_m2m_app_secret: { external: true }
  logto_jwt_audience: { external: true }
  logto_webhook_key_create_user: { external: true }
  logto_webhook_key_post_sign_in: { external: true }
  email_from_name: { external: true }
  email_from_address: { external: true }
  sendgrid_api_key: { external: true }
  google_gemini_api_key: { external: true }

  mail_server: { external: true }
  mail_port: { external: true }
  mail_username: { external: true }
  mail_password: { external: true }
  mail_template_dir: { external: true }

  stripe_api_key: { external: true }
  stripe_secret_key: { external: true }
  stripe_publishable_key: { external: true }
  stripe_webhook_secret: { external: true }
  posthog_key: { external: true }
  posthog_host: { external: true }
  web_app_base_url: { external: true }
  web_app_origin: { external: true }
  web_app_path_invite_landing: { external: true }
  limits_songs: { external: true }
  limits_storage_bytes_per_song: { external: true }
  limits_storage_bytes_per_user: { external: true }
  peerjs_key: { external: true }
  recaptcha_site_key: { external: true }
  recaptcha_secret_key: { external: true }
  schedule_email_summary_delay_ms: { external: true }

services:
  web-app-backend:
    # Use environment variables for image URL and tag
    image: ${BACKEND_IMAGE_URL:-registry.gitlab.com/johngrimsey/major/web-app-backend}:${IMAGE_TAG:-latest}
    secrets:
      # List all secrets used by the service
      - database_password
      - database_host
      - database_port
      - database_username
      - database_name
      - database_schema
      - redis_host
      - redis_port
      - redis_password
      - cloudflare_r2_access_key_id
      - cloudflare_r2_secret_access_key
      - cloudflare_r2_account_id
      - cloudflare_r2_api_token
      - cloudflare_r2_bucket_name
      - cloudflare_r2_endpoint
      - cloudflare_r2_avatars_bucket_name
      - cloudflare_r2_avatars_bucket_url
      - cloud_convert_access_token
      - cloud_convert_webhook_secret_job_created
      - cloud_convert_webhook_secret_job_failed
      - cloud_convert_webhook_secret_job_finished
      - hmac_secret
      - invite_secret_key
      - google_gemini_api_key
      - mail_server
      - mail_port
      - mail_username
      - mail_password
      - mail_template_dir
      - stripe_api_key
      - stripe_secret_key
      - stripe_publishable_key
      - stripe_webhook_secret
      - logto_url
      - logto_endpoint
      - logto_admin_endpoint
      - logto_openid_provider_endpoint
      - logto_authorization_endpoint
      - logto_token_endpoint
      - logto_userinfo_endpoint
      - logto_m2m_app_id
      - logto_m2m_app_secret
      - logto_jwt_audience
      - logto_webhook_key_create_user
      - logto_webhook_key_post_sign_in
      - email_from_name
      - email_from_address
      - sendgrid_api_key
      - posthog_key
      - posthog_host
      - web_app_base_url
      - web_app_origin
      - web_app_path_invite_landing
      - limits_songs
      - limits_storage_bytes_per_song
      - limits_storage_bytes_per_user
      - peerjs_key
      - recaptcha_site_key
      - recaptcha_secret_key
      - schedule_email_summary_delay_ms
    environment:
      # Keep NODE_ENV and NODE_OPTIONS
      - NODE_ENV=production
      - NODE_OPTIONS=--no-warnings
      # Keep all _FILE variables pointing to secrets
      - DATABASE_PASSWORD_FILE=/run/secrets/database_password
      - DATABASE_HOST_FILE=/run/secrets/database_host
      - DATABASE_PORT_FILE=/run/secrets/database_port
      - DATABASE_USERNAME_FILE=/run/secrets/database_username
      - DATABASE_NAME_FILE=/run/secrets/database_name
      - DATABASE_SCHEMA_FILE=/run/secrets/database_schema
      - REDIS_HOST_FILE=/run/secrets/redis_host
      - REDIS_PORT_FILE=/run/secrets/redis_port
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - CLOUDFLARE_R2_ACCESS_KEY_ID_FILE=/run/secrets/cloudflare_r2_access_key_id
      - CLOUDFLARE_R2_SECRET_ACCESS_KEY_FILE=/run/secrets/cloudflare_r2_secret_access_key
      - CLOUDFLARE_R2_ACCOUNT_ID_FILE=/run/secrets/cloudflare_r2_account_id
      - CLOUDFLARE_R2_API_TOKEN_FILE=/run/secrets/cloudflare_r2_api_token
      - CLOUDFLARE_R2_BUCKET_NAME_FILE=/run/secrets/cloudflare_r2_bucket_name
      - CLOUDFLARE_R2_ENDPOINT_FILE=/run/secrets/cloudflare_r2_endpoint
      - CLOUDFLARE_R2_AVATARS_BUCKET_NAME_FILE=/run/secrets/cloudflare_r2_avatars_bucket_name
      - CLOUDFLARE_R2_AVATARS_BUCKET_URL_FILE=/run/secrets/cloudflare_r2_avatars_bucket_url
      - CLOUD_CONVERT_ACCESS_TOKEN_FILE=/run/secrets/cloud_convert_access_token
      - CLOUD_CONVERT_WEBHOOK_SECRET_JOB_CREATED_FILE=/run/secrets/cloud_convert_webhook_secret_job_created
      - CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FAILED_FILE=/run/secrets/cloud_convert_webhook_secret_job_failed
      - CLOUD_CONVERT_WEBHOOK_SECRET_JOB_FINISHED_FILE=/run/secrets/cloud_convert_webhook_secret_job_finished
      - HMAC_SECRET_FILE=/run/secrets/hmac_secret
      - INVITE_SECRET_KEY_FILE=/run/secrets/invite_secret_key
      - GOOGLE_GEMINI_API_KEY_FILE=/run/secrets/google_gemini_api_key
      - MAIL_SERVER_FILE=/run/secrets/mail_server
      - MAIL_PORT_FILE=/run/secrets/mail_port
      - MAIL_USERNAME_FILE=/run/secrets/mail_username
      - MAIL_PASSWORD_FILE=/run/secrets/mail_password
      - MAIL_TEMPLATE_DIR_FILE=/run/secrets/mail_template_dir
      - STRIPE_API_KEY_FILE=/run/secrets/stripe_api_key
      - STRIPE_SECRET_KEY_FILE=/run/secrets/stripe_secret_key
      - STRIPE_PUBLISHABLE_KEY_FILE=/run/secrets/stripe_publishable_key
      - STRIPE_WEBHOOK_SECRET_FILE=/run/secrets/stripe_webhook_secret
      - LOGTO_URL_FILE=/run/secrets/logto_url
      - LOGTO_ADMIN_ENDPOINT_FILE=/run/secrets/logto_admin_endpoint
      - LOGTO_ENDPOINT_FILE=/run/secrets/logto_endpoint
      - LOGTO_OPENID_PROVIDER_ENDPOINT_FILE=/run/secrets/logto_openid_provider_endpoint
      - LOGTO_AUTHORIZATION_ENDPOINT_FILE=/run/secrets/logto_authorization_endpoint
      - LOGTO_TOKEN_ENDPOINT_FILE=/run/secrets/logto_token_endpoint
      - LOGTO_USERINFO_ENDPOINT_FILE=/run/secrets/logto_userinfo_endpoint
      - LOGTO_M2M_APP_ID_FILE=/run/secrets/logto_m2m_app_id
      - LOGTO_M2M_APP_SECRET_FILE=/run/secrets/logto_m2m_app_secret
      - LOGTO_JWT_AUDIENCE_FILE=/run/secrets/logto_jwt_audience
      - LOGTO_WEBHOOK_KEY_CREATE_USER_FILE=/run/secrets/logto_webhook_key_create_user
      - LOGTO_WEBHOOK_KEY_POST_SIGN_IN_FILE=/run/secrets/logto_webhook_key_post_sign_in
      - EMAIL_FROM_NAME_FILE=/run/secrets/email_from_name
      - EMAIL_FROM_ADDRESS_FILE=/run/secrets/email_from_address
      - SENDGRID_API_KEY_FILE=/run/secrets/sendgrid_api_key
      - POSTHOG_KEY_FILE=/run/secrets/posthog_key
      - POSTHOG_HOST_FILE=/run/secrets/posthog_host
      - WEB_APP_BASE_URL_FILE=/run/secrets/web_app_base_url
      - WEB_APP_ORIGIN_FILE=/run/secrets/web_app_origin
      - WEB_APP_PATH_INVITE_LANDING_FILE=/run/secrets/web_app_path_invite_landing
      - LIMITS_SONGS_FILE=/run/secrets/limits_songs
      - LIMITS_STORAGE_BYTES_PER_SONG_FILE=/run/secrets/limits_storage_bytes_per_song
      - LIMITS_STORAGE_BYTES_PER_USER_FILE=/run/secrets/limits_storage_bytes_per_user
      - PEERJS_KEY_FILE=/run/secrets/peerjs_key
      - RECAPTCHA_SITE_KEY_FILE=/run/secrets/recaptcha_site_key
      - RECAPTCHA_SECRET_KEY_FILE=/run/secrets/recaptcha_secret_key
      - SCHEDULE_EMAIL_SUMMARY_DELAY_MS_FILE=/run/secrets/schedule_email_summary_delay_ms
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        order: stop-first
        failure_action: rollback
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
      placement:
        max_replicas_per_node: 1 # Spread backend instances across nodes
      labels:
        - 'traefik.enable=true'
        # Use STACK_NAME for router/service names
        - 'traefik.http.routers.${STACK_NAME:-app-backend-prod}_web-app-backend.rule=Host(`api.${TLD:-major.app}`)'
        - 'traefik.http.routers.${STACK_NAME:-app-backend-prod}_web-app-backend.entrypoints=websecure'
        - 'traefik.http.services.${STACK_NAME:-app-backend-prod}_web-app-backend.loadbalancer.server.port=3000' # Backend listens on 3000
        - 'traefik.http.routers.${STACK_NAME:-app-backend-prod}_web-app-backend.tls.certresolver=letsencrypt'
        - 'traefik.docker.network=traefik-public'
    networks:
      - traefik-public
      - major-internal # Connect to DB, Redis, Logto etc.
    healthcheck:
      test: [ "CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/health" ] # Use wget --spider for efficiency
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s # Give backend time to start and connect to DB/Redis
