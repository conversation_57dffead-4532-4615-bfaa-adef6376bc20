import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtGuard } from 'libs/web-app-backend/util-websocket/src/lib/guards/ws-jwt.guard';
import {
	MessageBody,
	SubscribeMessage,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { JwtWsUser } from 'libs/web-app-backend/util-websocket/src/lib/decorators/ws-user.decorator';
import type {
	CurrentWebSocketUser,
	SendInvitePayload,
} from '@major/web-app-backend/util-websocket/types';
import { getUserRoomName } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-user-room-name';
import { UserEntity } from '@major/web-app-backend/users/entities';
import { WebSocketEvent } from '@major/shared/util-websocket/types';
import { logColorPrimary } from 'libs/web-app-backend/util-logging/src/lib/utils/cli-colors/index';
import { getSocketGatewayCors } from 'libs/web-app-backend/util-websocket/src/lib/utils/get-socket-gateway-cors';

@UseGuards(WsJwtGuard)
@WebSocketGateway({
	path: '/websockets',
	cors: getSocketGatewayCors(),
})
export class VideoCallGateway {
	private logger = new Logger(VideoCallGateway.name);

	@WebSocketServer()
	server!: Server;

	constructor(@InjectDataSource() private dataSource: DataSource) {}

	@SubscribeMessage(WebSocketEvent.VideoCallSendInvite)
	async handleSendInvite(
		@JwtWsUser() currentUser: CurrentWebSocketUser,
		@MessageBody() payload: SendInvitePayload,
	) {
		this.logger.debug(
			`${logColorPrimary(WebSocketEvent.VideoCallSendInvite)} ${currentUser.email} invites ${payload.email}`,
		);

		const user = await this.dataSource
			.getRepository(UserEntity)
			.findOneBy({ email: payload.email });

		if (!user) {
			this.logger.log(`Invitee ${payload.email} not found`);
			return;
		}

		try {
			currentUser.client
				.to(getUserRoomName(user.id))
				.emit(WebSocketEvent.VideoCallInviteReceived, {
					data: {
						hostPeerId: payload.hostPeerId,
						hostEmail: currentUser.email,
					},
				});

			this.logger.log(`Invite sent to ${payload.email}`);
		} catch (error) {
			this.logger.log(`Invitee ${payload.email} not connected`);
		}
	}
}
