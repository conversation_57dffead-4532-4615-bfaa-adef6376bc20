import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { NotificationEntity } from './types/entities/notification.entity';
import { SongEntity } from '@major/web-app-backend/songs/entities';
import { NotificationsController } from './controllers/notifications.controller';
import { NotificationEntityService, NotificationFactory } from './services';
import { NotificationsConsumer, NotificationsProducer } from './queue';

// Import summaries components
import {
	NotificationPresenceGateway,
	SCHEDULE_EMAIL_SUMMARY_DELAY_MS,
	SongActivityEventListener,
	SummaryEmailConsumer,
	summaryEmailQueueConfig,
} from './summaries';
import { provideConfigValue } from 'web-app-backend/util-config';
import { WebAppBackendMailerModule } from 'mailer';

@Module({
	imports: [
		WebAppBackendMailerModule,
		TypeOrmModule.forFeature([NotificationEntity, SongEntity]),
		BullModule.registerQueue(
			{
				name: 'notifications',
				defaultJobOptions: {
					removeOnComplete: false,
					attempts: 3,
					backoff: {
						type: 'exponential',
						delay: 1000,
					},
				},
			},

			// Register the summaries queue
			summaryEmailQueueConfig,
		),
	],
	controllers: [NotificationsController],
	providers: [
		provideConfigValue(
			SCHEDULE_EMAIL_SUMMARY_DELAY_MS,
			'SCHEDULE_EMAIL_SUMMARY_DELAY_MS',
		),
		NotificationEntityService,
		NotificationFactory,
		NotificationsConsumer,
		NotificationsProducer,

		// Summaries providers
		SongActivityEventListener,
		NotificationPresenceGateway,
		SummaryEmailConsumer,
	],
	exports: [NotificationsProducer, NotificationFactory],
})
export class WebAppBackendNotificationsModule {}
