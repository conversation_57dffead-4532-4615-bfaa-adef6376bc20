import { <PERSON>, Get, NotFoundException, Param } from '@nestjs/common';
import { CollaborationEntityService } from '../services/collaboration-entity.service';
import { CollaborationEntity } from '@major/web-app-backend/collaboration/entities';
import { instanceToPlain, plainToClass } from 'class-transformer';
import { CurrentJwtUser, JwtUser } from '@major/web-app-backend/util-auth';
import { uniq } from 'lodash';

@Controller('collaborations')
export class CollaborationsController {
	constructor(
		private readonly collaborationsService: CollaborationEntityService,
	) {}

	@Get()
	getAll(@JwtUser() user: CurrentJwtUser) {
		const result = this.collaborationsService.findAllByUserId(user.id);

		if (!result) {
			throw new NotFoundException('Collaborations not found');
		}

		return plainToClass(CollaborationEntity, result, { groups: ['detail'] });
	}

	@Get('unique')
	async getUniqueCollaborations(@JwtUser() user: CurrentJwtUser) {
		const collaborations =
			await this.collaborationsService.getUserUniqueCollaborations(user.id);

		const uniqueEmails = uniq(
			collaborations.flatMap((u) => u.collaborators.map((c) => c.email)),
		).filter((e) => e !== user.email);

		return {
			numberOfUniqueCollaborators: uniqueEmails.length,
			collaborations,
		};
	}

	/**
	 *
	 * @param id
	 */
	@Get(':id')
	getOne(@Param('id') id: string) {
		return this.collaborationsService.findOneById(+id);
	}

	@Get('song/:songId')
	async getOneBySongExternalId(
		@JwtUser() user: CurrentJwtUser,
		@Param('songId') songExternalId: string,
	) {
		const docs =
			await this.collaborationsService.findAllBySongExternalId(songExternalId);

		return instanceToPlain(docs, { groups: ['list'] });
	}
}
