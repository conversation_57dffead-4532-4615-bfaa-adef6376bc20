#!/usr/bin/env node
// @ts-ignore
import chalk from 'chalk';
import { runGenerateVersionFiles } from 'infra/scripts/release/hooks/run-generate-version-files';
import { runCheckAndStageGeneratedFiles } from 'infra/scripts/release/hooks/run-check-and-stage-generated-files';
import { runGenerateDeploymentInfoAndAmend } from 'infra/scripts/release/hooks/run-generate-deployment-info-and-amend';
import { runGenerateCustomerChangelog } from 'infra/scripts/release/hooks/run-customer-changelog';
import { runUpdateManifestVersion } from 'infra/scripts/release/hooks/run-update-manifest-version';

// --- Hook Runner Main Logic ---
async function main() {
	const hookName = process.argv[2];
	if (!hookName) {
		console.error(chalk.red('Error: Hook name not provided.'));
		process.exit(1);
	}

	console.log(chalk.magenta(`\n▶ Executing release-it hook: ${hookName}`));

	try {
		switch (hookName) {
			case 'generateVersionFiles':
				runGenerateVersionFiles();
				break;
			case 'updateManifestVersion':
				runUpdateManifestVersion();
				break;
			case 'checkAndStageGeneratedFiles':
				runCheckAndStageGeneratedFiles();
				break;
			case 'generateDeploymentInfoAndAmend':
				runGenerateDeploymentInfoAndAmend();
				break;
			case 'generateCustomerChangelog':
				await runGenerateCustomerChangelog();
				break;
			default:
				console.error(chalk.red(`Unknown hook: ${hookName}`));
				process.exit(1);
		}
		console.log(chalk.magenta(`Successfully executed hook: ${hookName}\n`));
	} catch (error) {
		// The specific error should have been logged by the hook function
		console.error(
			chalk.bold.red(`❌ Hook '${hookName}' failed. Halting release-it.`),
		);
		process.exit(1); // IMPORTANT: Exit with non-zero code to stop release-it
	}
}

main();
