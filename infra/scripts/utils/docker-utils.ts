// infra/scripts/utils/docker-utils.ts

import chalk from 'chalk';
// infra/scripts/utils/docker-utils.ts
import { exec, execSync } from 'node:child_process';
import {
	logError,
	logInfo,
	logStep,
	logSuccess,
	logWarning,
} from './log-utils';
import fs from 'node:fs';
import { promisify } from 'node:util'; // We'll create this next

// Promisify exec for easier async/await usage
const execAsync = promisify(exec);

//
//
//
const SECRET_LABEL = 'managed-by=secret-script'; // Label for created secrets

export interface DockerImage {
  id: string;
  repository: string;
  tag: string;
  digest: string;
  size: string;
  sizeBytes: number;
  createdAt: string;
}

export interface ContainerUsage {
  // Sets of full image names, e.g., "redis:alpine" or "registry.gitlab.com/user/repo:tag"
  running: Set<string>;
  stopped: Set<string>;
}

/**
 * Executes a shell command and returns its stdout.
 * Handles errors and logs appropriately.
 * @param command The command to execute (e.g., 'docker').
 * @param args Array of arguments for the command.
 * @param options Optional settings including suppressOutput and custom environment variables.
 * @returns Promise<string> The stdout of the command.
 * @throws If the command fails.
 */
export async function runCommand(
	command: string,
	args: string[],
	options: { suppressOutput?: boolean; env?: NodeJS.ProcessEnv } = {},
): Promise<string> {
	const fullCommand = `${command} ${args.join(' ')}`;
	// Only log the command execution if output isn't suppressed
	if (!options.suppressOutput) {
		console.log(chalk.dim(`  $ ${fullCommand}`));
	}

	const executionEnv = { ...process.env, ...options.env };

	return new Promise((resolve, reject) => {
		exec(fullCommand, { env: executionEnv }, (error, stdout, stderr) => {
			if (error) {
				// *** Check suppressOutput before logging error details ***
				if (!options.suppressOutput) {
					logError(`Error executing command: ${fullCommand}`);
					if (stderr) {
						logError(`Stderr: ${stderr.trim()}`);
					}
					// Log stdout even on error if not suppressed and it exists
					if (stdout) {
						console.log(chalk.grey(`Output:\n${stdout.trim()}`));
					}
				}
				// Still reject the promise so the caller knows it failed
				reject(new Error(`Command failed: ${fullCommand} - ${error.message}`));
				return;
			}

			// Log stderr warnings or stdout only if output is not suppressed
			if (!options.suppressOutput) {
				if (stderr?.trim()) {
					// Log non-empty stderr as warning
					logWarning(`Stderr: ${stderr.trim()}`);
				}
				if (stdout?.trim()) {
					// Log non-empty stdout
					// console.log(chalk.grey(`Output:\n${stdout.trim()}`)); // Potentially too verbose for successful commands
				}
			}
			resolve(stdout.trim());
		});
	});
}

/** Gets the current Docker context name. */
async function getCurrentDockerContext(): Promise<string> {
	try {
		// Pass suppressOutput via the options object
		return await runCommand('docker', ['context', 'show'], {
			suppressOutput: true,
		});
	} catch (error) {
		logError('Failed to get current Docker context.');
		logError('Please ensure Docker is running and accessible.');
		throw error; // Propagate error
	}
}

/** Switches the Docker context. */
async function switchDockerContext(contextName: string): Promise<void> {
	try {
		await runCommand('docker', ['context', 'use', contextName], {
			suppressOutput: true,
		}); // Suppress output
	} catch (error) {
		logError(
			`Failed to switch Docker context to ${chalk.yellow(contextName)}.`,
		);
		logError(
			`Does the context "${contextName}" exist? (Check with 'docker context ls')`,
		);
		throw error; // Propagate error
	}
}

/**
 * Ensures the Docker CLI is using the specified context.
 * Switches context if necessary and verifies the switch.
 * Handles dry-run mode.
 *
 * @param targetContext The desired Docker context name.
 * @param dryRun If true, only log actions without executing context switch.
 * @returns Promise<string> The name of the final active context (should be targetContext).
 * @throws If context check or switch fails.
 */
export async function ensureDockerContext(
	targetContext: string,
	dryRun: boolean,
): Promise<string> {
	logStep(`Ensuring Docker context is '${chalk.yellow(targetContext)}'...`);
	let currentContext = await getCurrentDockerContext();
	logInfo(`Current context: ${chalk.yellow(currentContext)}`);

	if (currentContext !== targetContext) {
		logWarning(
			`Current context ('${currentContext}') is not the target ('${targetContext}').`,
		);
		if (dryRun) {
			logInfo(
				`[Dry Run] Would attempt to switch context to ${chalk.yellow(targetContext)}.`,
			);
			// In dry run, we assume the switch would succeed for subsequent steps
			return targetContext;
		}

		logStep(
			`Attempting to switch Docker context to ${chalk.yellow(targetContext)}...`,
		);
		await switchDockerContext(targetContext);
		logSuccess(
			`Successfully switched Docker context to ${chalk.yellow(targetContext)}.`,
		);

		// Verify context switch
		logStep('Verifying context switch...');
		currentContext = await getCurrentDockerContext();

		if (currentContext !== targetContext) {
			logError(
				`Context switch verification failed! Current context is still '${chalk.yellow(currentContext)}'.`,
			);
			throw new Error('Docker context switch verification failed.');
		}
		logSuccess(
			`Context successfully verified: ${chalk.yellow(currentContext)}`,
		);
	} else {
		logSuccess(
			`Already in the correct Docker context: ${chalk.yellow(targetContext)}.`,
		);
	}
	return currentContext; // Should be the targetContext at this point
}

/**
 * Gets the short Git commit hash of the current HEAD.
 * @returns The short commit hash string, or 'unknown' if git command fails.
 */
export function getGitCommitHash(): string {
	try {
		return execSync('git rev-parse --short HEAD', {
			encoding: 'utf-8',
		}).trim();
	} catch (error) {
		logWarning('Unable to get Git commit hash. Using "unknown".');
		// Optionally log the error: logError(`Git command failed: ${error.message}`);
		return 'unknown';
	}
}

/**
 * Logs into a Docker registry using username and password/token via stdin.
 * @param registry The registry URL (e.g., 'registry.gitlab.com').
 * @param username The username (e.g., 'gitlab-ci-token').
 * @param token The password or token.
 */
export async function loginToRegistry(
	registry: string,
	username: string,
	token: string,
): Promise<void> {
	logStep(`Logging in to Docker registry: ${chalk.yellow(registry)}...`);
	const command = `docker login "${registry}" --username "${username}" --password-stdin`;
	console.log(chalk.dim(`  $ echo "[REDACTED]" | ${command}`)); // Log redacted command

	try {
		await new Promise<void>((resolve, reject) => {
			const loginProcess = exec(command, (error, stdout, stderr) => {
				if (error) {
					logError(`Docker login failed: ${error.message}`);
					if (stderr) logError(`Stderr: ${stderr.trim()}`);
					reject(error);
				} else {
					if (stderr) logWarning(`Login stderr: ${stderr.trim()}`); // Often has warnings
					logSuccess('Login successful.');
					resolve();
				}
			});

			// Send the token to stdin
			if (loginProcess.stdin) {
				loginProcess.stdin.write(token);
				loginProcess.stdin.end();
			} else {
				reject(new Error('Failed to get stdin for login process.'));
			}
		});
	} catch (error) {
		// Error already logged, just rethrow
		throw new Error('Docker login failed.');
	}
}

interface BuildDockerImageOptions {
	platform: string;
	dockerfilePath: string;
	buildContext: string;
	tags: string[]; // Array of full image tags (e.g., ['repo/img:latest', 'repo/img:commit'])
	labels?: string[]; // Array of label strings (e.g., ['key=value', 'key2=value2'])
	noCache?: boolean;
	targetStage?: string;
}

/**
 * Builds a Docker image using docker build.
 * @param options Options for the docker build command.
 */
export async function buildDockerImage(
	options: BuildDockerImageOptions,
): Promise<void> {
	logStep(
		`Building Docker image for platform ${chalk.yellow(options.platform)}...`,
	);
	// Log the target stage if specified
	if (options.targetStage) {
		logInfo(`  Target Stage: ${chalk.cyan(options.targetStage)}`);
	}
	for (const tag of options.tags) {
		logInfo(`  Tag: ${chalk.magenta(tag)}`);
	}

	const buildArgs = ['build'];
	if (options.labels && options.labels.length > 0) {
		for (const label of options.labels) {
			buildArgs.push('--label', label);
		}
	}
	if (options.noCache) {
		buildArgs.push('--no-cache');
	}
	// Add --target flag if targetStage is provided
	if (options.targetStage) {
		buildArgs.push('--target', options.targetStage);
	}
	buildArgs.push('--platform', options.platform);
	buildArgs.push('-f', options.dockerfilePath);
	for (const tag of options.tags) {
		buildArgs.push('-t', tag);
	}
	buildArgs.push(options.buildContext); // Build context must be last

	await runCommand('docker', buildArgs); // Assumes runCommand handles logging
	logSuccess('Docker image built successfully.');
}

/**
 * Pushes a Docker image tag to its registry.
 * @param imageNameWithTag The full image name including the tag.
 */
export async function pushDockerImage(imageNameWithTag: string): Promise<void> {
	logStep(`Pushing Docker image: ${chalk.magenta(imageNameWithTag)}...`);
	await runCommand('docker', ['push', imageNameWithTag]);
	logSuccess(`Successfully pushed ${imageNameWithTag}.`);
}

/**
 * Logs out from a Docker registry.
 * @param registry The registry URL.
 */
export async function logoutFromRegistry(registry: string): Promise<void> {
	logStep(`Logging out from Docker registry: ${chalk.yellow(registry)}...`);
	await runCommand('docker', ['logout', registry]);
	logSuccess(`Successfully logged out from ${registry}.`);
}

/**
 * Deploys the Docker stack using the specified context and environment variables.
 */
export async function deployStackWithEnv(
	stackName: string,
	composeFile: string,
	contextName: string,
	envVars: NodeJS.ProcessEnv, // Environment variables for interpolation
): Promise<void> {
	logStep(
		`Deploying stack ${chalk.blue(stackName)} using context ${chalk.yellow(contextName)}...`,
	);
	logInfo(`Stack file: ${chalk.grey(composeFile)}`);
	logInfo('With environment variables for interpolation:');
	for (const [key, value] of Object.entries(envVars)) {
		// Be careful about logging sensitive values if any are passed
		logInfo(`  ${key}=${value}`);
	}

	try {
		// Pass the environment variables to runCommand
		await runCommand(
			'docker',
			[
				'--context',
				contextName,
				'stack',
				'deploy',
				'-c',
				composeFile,
				'--with-registry-auth', // Pass registry credentials
				stackName,
			],
			{ env: envVars }, // Pass the env object here
		);
		logSuccess(`Stack ${chalk.blue(stackName)} deployed successfully.`);
	} catch (error) {
		logError(`Failed to deploy stack ${chalk.blue(stackName)}.`);
		throw error; // Propagate error
	}
}

/**
 * Checks if a Docker secret exists in the target context.
 */
export async function secretExists(
	secretName: string,
	contextName: string,
): Promise<boolean> {
	try {
		// runCommand throws on non-zero exit code
		await runCommand(
			'docker',
			['--context', contextName, 'secret', 'inspect', secretName],
			{ suppressOutput: true },
		);
		return true;
	} catch (error) {
		// Assuming error means secret not found
		return false;
	}
}

/**
 * Creates a Docker secret using stdin for the value.
 */
export async function createSecret(
	secretName: string,
	secretValue: string,
	contextName: string,
): Promise<void> {
	logInfo(`Creating secret ${chalk.blue(secretName)}...`);
	return new Promise<void>((resolve, reject) => {
		const command = `docker --context ${contextName} secret create --label ${SECRET_LABEL} ${secretName} -`;
		const createProcess = exec(command, (error, stdout, stderr) => {
			if (error) {
				logError(
					`Failed to create secret ${chalk.red(secretName)}: ${error.message}`,
				);
				if (stderr) logError(`Stderr: ${stderr}`);
				reject(error);
			} else {
				if (stderr) logWarning(`Stderr during creation: ${stderr}`); // Log warnings if any
				logSuccess(`Secret ${chalk.blue(secretName)} created.`);
				resolve();
			}
		});

		// Write the secret value to the command's stdin
		createProcess.stdin?.write(secretValue);
		createProcess.stdin?.end();
	});
}

/**
 * Removes a Docker secret.
 */
export async function removeSecret(
	secretName: string,
	contextName: string,
): Promise<void> {
	logWarning(
		`Removing existing secret ${chalk.yellow(secretName)} (services using it may briefly fail)...`,
	);
	try {
		await runCommand('docker', [
			'--context',
			contextName,
			'secret',
			'rm',
			secretName,
		]);
		logInfo(`Secret ${chalk.yellow(secretName)} removed.`);
	} catch (error) {
		logError(`Failed to remove secret ${chalk.red(secretName)}.`);
		throw error; // Propagate error
	}
}

interface BuildAndPushOptions {
	appName: string; // e.g., 'marketing-app'
	appBuildCommand: string; // e.g., 'bun run build:marketing-app:prod'
	dockerfilePath: string; // Absolute path to the Dockerfile
	buildContext: string; // Absolute path for the build context
	imageNameInRegistry: string; // The name part in the registry, e.g., 'marketing-app'
	gitlabRegistry: string;
	gitlabLoginUser: string;
	gitlabToken: string;
	gitlabUserOrGroup: string;
	gitlabProjectName: string;
	platform: string; // e.g., 'linux/amd64'
	noCache: boolean;
	dryRun: boolean;
	projectRoot: string; // Absolute path to the project root
}

/**
 * Builds the application, builds the Docker image, and pushes it to the specified GitLab registry.
 */
export async function buildAndPushAppImage(
	options: BuildAndPushOptions,
): Promise<void> {
	const {
		appName,
		appBuildCommand,
		dockerfilePath,
		buildContext,
		imageNameInRegistry,
		gitlabRegistry,
		gitlabLoginUser,
		gitlabToken,
		gitlabUserOrGroup,
		gitlabProjectName,
		platform,
		noCache,
		dryRun,
		projectRoot,
	} = options;

	logStep(`Starting Build & Push for App: ${chalk.cyan(appName)}`);
	console.log('--------------------------------------------------');

	try {
		// 1. Get Git Commit Hash
		const gitCommitHash = getGitCommitHash();
		logInfo(`Using Git commit hash: ${chalk.yellow(gitCommitHash)}`);
		console.log('--------------------------------------------------');

		// 2. Define Image Names and Tags
		const fullImageNameBase = `${gitlabRegistry}/${gitlabUserOrGroup}/${gitlabProjectName}/${imageNameInRegistry}`;
		const fullImageNameLatest = `${fullImageNameBase}:latest`;
		const fullImageNameCommit = `${fullImageNameBase}:${gitCommitHash}`;
		const tags = [fullImageNameLatest, fullImageNameCommit];

		// 3. Define OCI labels
		const labels = [
			`org.opencontainers.image.revision=${gitCommitHash}`,
			`org.opencontainers.image.created=${new Date().toISOString()}`,
			`org.opencontainers.image.source=https://gitlab.com/${gitlabUserOrGroup}/${gitlabProjectName}`, // Adjust if needed
		];

		// 4. Log Configuration
		logInfo('Build & Push Configuration:');
		logInfo(`   App Name:        ${appName}`);
		logInfo(`   Build Command:   ${appBuildCommand}`);
		logInfo(`   Dockerfile:      ${dockerfilePath}`);
		logInfo(`   Build Context:   ${buildContext}`);
		logInfo(`   Target Platform: ${platform}`);
		logInfo(`   Use Cache:       ${!noCache ? 'Yes' : 'No'}`);
		logInfo(`   Target Image:    ${chalk.magenta(fullImageNameBase)}`);
		for (const tag of tags) {
			logInfo(`   Tag:             ${chalk.magenta(tag)}`);
		}
		console.log('--------------------------------------------------');

		// 5. Check if Dockerfile exists
		if (!fs.existsSync(dockerfilePath)) {
			throw new Error(`Dockerfile not found at ${dockerfilePath}`);
		}

		// --- Operations ---

		if (dryRun) {
			// Dry Run Steps
			logStep(`[Dry Run] Build ${appName} Application`);
			logInfo(`Would execute: ${chalk.cyan(appBuildCommand)}`);

			logStep('[Dry Run] Docker Build');
			logInfo(`Would build image with tags: ${tags.join(', ')}`);
			logInfo(`  Platform: ${platform}`);
			logInfo(`  Dockerfile: ${dockerfilePath}`);
			logInfo(`  Context: ${buildContext}`);
			logInfo(`  Labels: ${labels.join(', ')}`);
			logInfo(`  No Cache: ${noCache}`);

			logStep('[Dry Run] Docker Login');
			logInfo(`Would login to ${gitlabRegistry} as ${gitlabLoginUser}`);

			logStep('[Dry Run] Docker Push');
			for (const tag of tags) {
				logInfo(`Would push image: ${chalk.magenta(tag)}`);
			}

			logStep('[Dry Run] Docker Logout');
			logInfo(`Would logout from ${gitlabRegistry}`);
		} else {
			// Actual Execution Steps

			// 6. Build the Application (e.g., Nx build)
			logStep(`Building ${appName} application...`);
			console.log(chalk.dim(`  $ ${appBuildCommand}`));
			try {
				execSync(appBuildCommand, {
					stdio: 'inherit',
					cwd: projectRoot, // Run build command from project root
				});
				logSuccess(`${appName} built successfully.`);
			} catch (error: any) {
				throw new Error(`Failed to build ${appName}: ${error.message}`);
			}
			console.log('--------------------------------------------------');

			// 7. Build the Docker Image
			await buildDockerImage({
				platform: platform,
				dockerfilePath: dockerfilePath,
				buildContext: buildContext,
				tags: tags,
				labels: labels,
				noCache: noCache,
			});
			console.log('--------------------------------------------------');

			// 8. Log in to GitLab Registry
			await loginToRegistry(gitlabRegistry, gitlabLoginUser, gitlabToken);
			console.log('--------------------------------------------------');

			// 9. Push the Docker Image Tags
			logStep(`Pushing image ${fullImageNameBase} to registry...`);
			for (const tag of tags) {
				await pushDockerImage(tag);
			}
			console.log('--------------------------------------------------');

			// 10. Log out from GitLab Registry
			await logoutFromRegistry(gitlabRegistry);
			console.log('--------------------------------------------------');
		} // End if (!dryRun)

		logSuccess(`Build & Push for ${appName} completed successfully.`);
	} catch (error: any) {
		logError(
			`\n❌ Build & Push process failed for ${appName}: ${error.message}`,
		);
		// Attempt logout only if not dry run and error occurred after potential login
		if (!dryRun) {
			try {
				logWarning('\nAttempting to log out from registry due to error...');
				await logoutFromRegistry(gitlabRegistry);
				logSuccess('Logout successful during error cleanup.');
			} catch (logoutError) {
				logError('⚠️ Failed to log out from registry during error cleanup.');
			}
		}
		throw error; // Re-throw the error to be caught by the calling script
	}
}

export async function runDockerTaskAndWait(
	contextName: string,
	serviceName: string,
	imageWithTag: string,
	secretNames: string[],
	networkName?: string,
	envVars?: Record<string, string>,
	timeoutSeconds = 300, // Overall timeout for the task to complete
	commandAndArgs?: string[],
): Promise<void> {
	logStep(`Running one-off task service '${chalk.cyan(serviceName)}'...`);
	logInfo(`Using image: ${chalk.blue(imageWithTag)}`);
	logInfo(`Context: ${chalk.yellow(contextName)}`);
	if (networkName) {
		logInfo(`Network: ${chalk.yellow(networkName)}`);
	}

	const secretArgsArray = secretNames.map(
		(name) => `--secret source=${name},target=${name}`,
	);
	const networkArg = networkName ? `--network ${networkName}` : '';
	const envArgsArray = envVars
		? Object.entries(envVars).map(([key, value]) => `--env "${key}=${value}"`)
		: [];
	const commandSuffixArray = commandAndArgs?.length ? commandAndArgs : [];

	// --- CRITICAL CHANGE: Add --detach ---
	const dockerCommandParts = [
		'docker',
		'--context',
		contextName,
		'service',
		'create',
		'--detach', // Ensures the command returns quickly with the service ID
		'--name',
		serviceName,
		'--restart-condition',
		'none',
		...networkArg.split(' ').filter(Boolean),
		...secretArgsArray,
		...envArgsArray,
		imageWithTag,
		...commandSuffixArray,
	];

	const createCommandForDisplay = dockerCommandParts.join(' ');
	logInfo(`Executing command: ${chalk.cyan(createCommandForDisplay)}`);
	// Logging for secrets and env vars (as before)
	if (secretNames.length > 0) {
		logInfo('With secrets mounted:');
		for (const name of secretNames) {
			logInfo(`  ${name} -> /run/secrets/${name}`);
		}
	}
	if (envVars && Object.keys(envVars).length > 0) {
		logInfo('With environment variables set:');
		for (const [key, value] of Object.entries(envVars)) {
			logInfo(`  ${chalk.green(key)}=${chalk.cyan(value)}`);
		}
	}

	let serviceId = '';
	try {
		logInfo(
			`[DEBUG] Attempting to create Docker service '${serviceName}' (detached)...`,
		);

		// Timeout specifically for the 'docker service create --detach' CLI command
		const SERVICE_CREATE_CLI_TIMEOUT_MS = 30000; // 30 seconds should be ample for detached create

		let createStdout: string;
		try {
			// runCommand should execute 'docker' with dockerCommandParts.slice(1) as args
			const createServicePromise = runCommand(
				dockerCommandParts[0], // 'docker'
				dockerCommandParts.slice(1), // the rest of the arguments
				{
					env: { ...process.env },
					// suppressOutput: false, // Set to false if you need to see raw output for debugging
				},
			);

			const cliTimeoutPromise = new Promise<never>((_, reject) =>
				setTimeout(
					() =>
						reject(
							new Error(
								`'docker service create --detach' CLI command for '${serviceName}' timed out after ${SERVICE_CREATE_CLI_TIMEOUT_MS / 1000}s. This might be due to issues pulling the image or Swarm manager responsiveness. Try pre-pulling the image.`,
							),
						),
					SERVICE_CREATE_CLI_TIMEOUT_MS,
				),
			);

			createStdout = await Promise.race([
				createServicePromise,
				cliTimeoutPromise,
			]);
			// With --detach, stdout should be just the service ID.
			logInfo(
				`[DEBUG] 'docker service create --detach' CLI command finished. Stdout: ${createStdout.trim()}`,
			);
		} catch (creationError: any) {
			logError(
				`Failed to create Docker service '${chalk.red(serviceName)}' via CLI.`,
			);
			if (creationError.message.includes('timed out')) {
				logError(
					`  Reason: 'docker service create --detach' CLI command execution timed out.`,
				);
				logError(
					`  Suggestion: Ensure image '${imageWithTag}' is pre-pulled on Swarm manager(s) or check registry access.`,
				);
			} else {
				logError(
					`  Error details from runCommand should be above if not suppressed. Message: ${creationError.message}`,
				);
			}
			// Attempt cleanup
			try {
				logWarning(
					`Attempting to remove service '${serviceName}' by name due to creation failure...`,
				);
				await runCommand(
					'docker',
					['--context', contextName, 'service', 'rm', serviceName],
					{ suppressOutput: true, env: { ...process.env } },
				);
				logInfo(
					`Cleanup attempt for service '${serviceName}' completed (it may not have existed).`,
				);
			} catch (cleanupError: any) {
				logWarning(
					`Failed to cleanup service '${serviceName}' by name during error handling: ${cleanupError.message}`,
				);
			}
			throw creationError;
		}

		serviceId = createStdout.trim();
		// Validate serviceId format (basic check: should not contain "overall progress" or newlines)
		if (
			!serviceId ||
			serviceId.includes('\n') ||
			serviceId.toLowerCase().includes('overall progress') ||
			serviceId.toLowerCase().includes('error response from daemon') ||
			serviceId.length < 12 // Typical Docker IDs are longer
		) {
			logError(
				`'docker service create --detach' command returned invalid or unexpected output for service ID. Stdout: "${createStdout.trim()}"`,
			);
			logError(
				`This can happen if the image digest cannot be resolved or if there's an immediate error from the Docker daemon.`,
			);
			// Attempt cleanup if serviceId looks like progress output or an error
			if (serviceName) {
				logWarning(
					`Attempting to remove service '${serviceName}' by name as ID was not clean...`,
				);
				try {
					await runCommand(
						'docker',
						['--context', contextName, 'service', 'rm', serviceName],
						{ suppressOutput: true, env: { ...process.env } },
					);
				} catch (e) {
					/* ignore cleanup error if service doesn't exist */
				}
			}
			throw new Error(
				`Failed to create Docker service '${serviceName}', invalid service ID or error message returned.`,
			);
		}

		logInfo(
			`Service '${chalk.cyan(serviceName)}' created with ID: ${chalk.yellow(serviceId)}. Waiting for task to complete...`,
		);

		const startTime = Date.now();
		let taskCompleted = false;
		let taskStatus = '';
		let taskError = '';
		let lastLoggedStatus = '';
		const pollIntervalMs = 3000; // Poll every 3 seconds

		while (Date.now() - startTime < timeoutSeconds * 1000) {
			await new Promise((resolve) => setTimeout(resolve, pollIntervalMs));
			try {
				const taskPsOutput = await runCommand(
					'docker',
					[
						'--context',
						contextName,
						'service',
						'ps',
						serviceId, // Use the captured serviceId
						'--format',
						'\'{{json .}}\'', // <<< MODIFIED HERE: Wrap Go template in single quotes for the shell
						'--no-trunc',
					],
					{ suppressOutput: true, env: { ...process.env } },
				);

				const taskLines = taskPsOutput
					.split('\n')
					.filter((line) => line.trim() !== '');
				if (taskLines.length === 0) {
					if (lastLoggedStatus !== 'waiting_for_task_info') {
						logInfo(
							`  Waiting for task info for service ${serviceId}... (no tasks found yet)`,
						);
						lastLoggedStatus = 'waiting_for_task_info';
					}
					continue;
				}

				// Assuming one task per service for these one-off tasks
				// The output from '{{json .}}' should be a clean JSON string per line.
				const taskData = JSON.parse(taskLines[0]);
				const taskId = taskData.ID;
				taskStatus = taskData.CurrentState || 'Unknown';
				taskError = taskData.Error || '';

				if (taskStatus !== lastLoggedStatus) {
					logInfo(
						`  Task ${chalk.blue(taskId)} status: ${chalk.magenta(taskStatus)}`,
					);
					lastLoggedStatus = taskStatus;
				}

				if (taskStatus.toLowerCase().startsWith('complete')) {
					taskCompleted = true;
					break;
				}

				const terminalFailureStates = [
					'failed',
					'rejected',
					'shutdown',
					'orphaned',
					'remove',
					'dead',
				];
				if (
					terminalFailureStates.some((state) =>
						taskStatus.toLowerCase().startsWith(state),
					)
				) {
					logError(
						`Task ${chalk.blue(taskId)} for service '${chalk.cyan(serviceName)}' finished with non-complete status: ${chalk.red(taskStatus)}`,
					);
					if (taskError && taskError.trim() !== '') {
						logError(`  Task Error Message: ${chalk.red(taskError.trim())}`);
					}
					try {
						const logsOutput = await runCommand(
							'docker',
							[
								'--context',
								contextName,
								'service',
								'logs',
								'--since',
								'0s',
								serviceId,
							], // Logs for the service
							{ suppressOutput: true, env: { ...process.env } },
						);
						logError('Service logs:');
						console.error(logsOutput.trim() || '[No logs found for service]');
					} catch (logErrorResult: any) {
						logError(
							`Failed to retrieve logs for service ${serviceId}: ${logErrorResult.message}`,
						);
					}
					throw new Error(
						`Task ${taskId} for service '${serviceName}' failed with status: ${taskStatus}`,
					);
				}
			} catch (pollError: any) {
				logWarning(
					`Error during task status poll for service ${serviceId}: ${pollError.message}`,
				);
				if (Date.now() - startTime > (timeoutSeconds - 10) * 1000) {
					// If close to overall timeout, then fail
					throw new Error(
						`Polling for task status of service '${serviceName}' failed repeatedly near timeout. Last error: ${pollError.message}`,
					);
				}
			}
		}

		if (!taskCompleted) {
			logError(
				`Task for service '${chalk.cyan(serviceName)}' (ID: ${chalk.yellow(serviceId)}) did not complete within ${timeoutSeconds} seconds.`,
			);
			logError(`Last known status: ${chalk.magenta(taskStatus || 'Unknown')}`);
			if (taskError && taskError.trim() !== '') {
				logError(`Last known task error: ${chalk.red(taskError.trim())}`);
			}
			// Attempt to get logs on timeout
			try {
				const logsOutput = await runCommand(
					'docker',
					[
						'--context',
						contextName,
						'service',
						'ps', // Changed from 'logs' to 'ps' to get final status on timeout
						serviceId,
						'--format',
						'\'{{json .}}\'', // Use single quotes for shell
						'--no-trunc',
					],
					{ suppressOutput: true, env: { ...process.env } },
				);
				logError('Final task status on timeout:');
				const finalTaskLines = logsOutput
					.split('\n')
					.filter((line) => line.trim() !== '');
				if (finalTaskLines.length > 0) {
					const cleanedJson = finalTaskLines[0].trim(); // Output from '{{json .}}' should be clean
					try {
						const finalTaskData = JSON.parse(cleanedJson);
						logError(
							`  ID: ${finalTaskData.ID}, Status: ${finalTaskData.CurrentState}, Error: ${finalTaskData.Error || 'None'}`,
						);
					} catch (parseError) {
						logError(`  Could not parse final task status: ${cleanedJson}`);
					}
				} else {
					logError('  No final task status found.');
				}

				// Also try to get service logs
				const serviceLogsOutput = await runCommand(
					'docker',
					[
						'--context',
						contextName,
						'service',
						'logs',
						'--since',
						'0s',
						serviceId,
					],
					{ suppressOutput: true, env: { ...process.env } },
				);
				logError('Service logs on timeout:');
				console.error(
					serviceLogsOutput.trim() || '[No logs found for service on timeout]',
				);
			} catch (logErrorResult: any) {
				logError(
					`Failed to retrieve final status or logs for service ${serviceId} on timeout: ${logErrorResult.message}`,
				);
			}
			throw new Error(`Task timeout for service '${serviceName}'`);
		}

		logSuccess(
			`Task for service '${chalk.cyan(serviceName)}' (ID: ${chalk.yellow(serviceId)}) completed successfully. Final status: ${chalk.magenta(taskStatus)}`,
		);
	} catch (error: any) {
		logError(
			`Overall process to run and monitor task service '${chalk.red(serviceName)}' failed.`,
		);
		if (
			error.message &&
			!error.message.toLowerCase().includes(serviceName.toLowerCase()) &&
			!error.message.toLowerCase().includes('task') &&
			!error.message.toLowerCase().includes('cli command execution timed out')
		) {
			logError(`  Details: ${error.message}`);
		}
		throw error; // Propagate error
	} finally {
		logStep(
			`Attempting cleanup for temporary service '${chalk.cyan(serviceName)}'...`,
		);
		const serviceToRemove = serviceId || serviceName; // Prefer ID if known
		try {
			await runCommand(
				'docker',
				['--context', contextName, 'service', 'rm', serviceToRemove],
				{ suppressOutput: true, env: { ...process.env } },
			);
			logInfo(`Service '${chalk.cyan(serviceToRemove)}' removed successfully.`);
		} catch (rmError: any) {
			if (rmError.message.includes('not found')) {
				logInfo(
					`Service '${chalk.cyan(serviceToRemove)}' was not found for removal (already removed or never fully created).`,
				);
			} else {
				// If serviceId was used and failed, try removing by name as a fallback
				if (serviceId && serviceToRemove === serviceId) {
					logWarning(
						`Failed to remove service by ID '${chalk.yellow(serviceId)}': ${rmError.message}. Attempting removal by name '${serviceName}'.`,
					);
					try {
						await runCommand(
							'docker',
							['--context', contextName, 'service', 'rm', serviceName],
							{ suppressOutput: true, env: { ...process.env } },
						);
						logInfo(
							`Service '${chalk.cyan(serviceName)}' removed successfully by name.`,
						);
					} catch (rmByNameError: any) {
						logWarning(
							`Failed to remove service '${chalk.cyan(serviceName)}' by name: ${rmByNameError.message}`,
						);
					}
				} else {
					logWarning(
						`Failed to remove service '${chalk.cyan(serviceToRemove)}': ${rmError.message}`,
					);
				}
			}
		}
	}
}

// --- Helper for Parsing Docker's Human-Readable Sizes ---
function parseSizeStringToBytes(sizeStr: string): number {
  if (!sizeStr) return 0;
  const sizeMatch = sizeStr.match(/^(\d+(\.\d+)?)\s*(B|KB|MB|GB|TB|PB)?$/i);
  if (!sizeMatch) return 0;

  const value = parseFloat(sizeMatch[1]);
  const unit = (sizeMatch[3] || 'B').toUpperCase();
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const power = units.indexOf(unit);

  if (power === -1) return 0;

  return Math.round(value * Math.pow(1024, power));
}

// --- New Functions for Docker Cleanup ---

/**
 * Gets a set of image digests currently in use by services in a given stack.
 * @param stackName The name of the Docker stack.
 * @returns A promise that resolves to a Set of in-use image digests.
 */
export async function getInUseImageDigests(
  stackName: string,
): Promise<Set<string>> {
  const serviceIdsOutput = await runCommand(
    'docker',
    [
      'service',
      'ls',
      '--filter',
      `label=com.docker.stack.namespace=${stackName}`,
      '--format',
      '{{.ID}}',
    ],
    { suppressOutput: true },
  );
  const serviceIds = serviceIdsOutput.split('\n').filter(Boolean);

  if (serviceIds.length === 0) {
    logWarning(`No services found for stack '${stackName}'.`);
    return new Set();
  }

  const digestPromises = serviceIds.map(async (id) => {
    try {
      const imageInspectOutput = await runCommand(
        'docker',
        [
          'service',
          'inspect',
          id,
          '--format',
          // This format reliably gets the image with its digest
          '{{.Spec.TaskTemplate.ContainerSpec.Image}}',
        ],
        { suppressOutput: true },
      );
      // Output is like: registry/repo/image:tag@sha256:digest
      const digestMatch = imageInspectOutput.match(/@sha256:([a-f0-9]+)/);
      return digestMatch ? `sha256:${digestMatch[1]}` : null;
    } catch (error) {
      logWarning(`Could not inspect service ${id}, it may have been removed.`);
      return null;
    }
  });

  const digests = (await Promise.all(digestPromises)).filter(
    (d): d is string => d !== null,
  );
  return new Set(digests);
}

/**
 * Gets a list of local Docker images, filtered by repository.
 * @param repoFilter The repository to filter by (e.g., 'registry.gitlab.com/my/project').
 * @returns A promise that resolves to an array of DockerImage objects.
 */
export async function getLocalImages(repoFilter: string): Promise<DockerImage[]> {
  // Using a custom Go template with a unique separator to safely parse the output.
  const formatTemplate = [
    '{{.ID}}',
    '{{.Repository}}',
    '{{.Tag}}',
    '{{.Digest}}',
    '{{.Size}}',
    '{{.CreatedAt}}',
  ].join('||');

  const output = await runCommand(
    'docker',
    ['images', `--filter=reference=${repoFilter}/*`, `--format=${formatTemplate}`],
    { suppressOutput: true },
  );

  if (!output) {
    return [];
  }

  const lines = output.split('\n').filter(Boolean);
  return lines.map((line) => {
    const [id, repository, tag, digest, size, createdAt] = line.split('||');
    return {
      id,
      repository,
      tag,
      digest,
      size,
      sizeBytes: parseSizeStringToBytes(size),
      createdAt,
    };
  });
}

/**
 * Removes a Docker image by its ID.
 * @param imageId The ID of the image to remove.
 */
export async function removeImageById(imageId: string): Promise<void> {
  // Using --force to remove images even if they have multiple tags.
  // The cleanup script logic ensures we only target unused images.
  await runCommand('docker', ['image', 'rm', '--force', imageId], {
    suppressOutput: true,
  });
}

/**
 * Prunes dangling (un-tagged) Docker images.
 */
export async function pruneDanglingImages(): Promise<void> {
  try {
    const output = await runCommand('docker', ['image', 'prune', '--force']);
    // The output of a successful prune contains "Total reclaimed space"
    if (output && !output.includes('Total reclaimed space: 0B')) {
      logSuccess(`Dangling images pruned. ${output}`);
    } else {
      logInfo('No dangling images to prune.');
    }
  } catch (error) {
    logWarning('Could not prune dangling images.');
  }
}

/**
 * Prunes stopped Docker containers.
 */
export async function pruneStoppedContainers(): Promise<void> {
  try {
    const output = await runCommand('docker', ['container', 'prune', '--force']);
    if (output && !output.includes('Total reclaimed space: 0B')) {
      logSuccess(`Stopped containers pruned. ${output}`);
    } else {
      logInfo('No stopped containers to prune.');
    }
  } catch (error) {
    logWarning('Could not prune stopped containers.');
  }
}

/**
 * Gets the image names used by all containers, categorized by running or stopped state.
 * @returns A promise that resolves to an object containing sets of image names for running and stopped containers.
 */
export async function getContainerUsageByImageName(): Promise<ContainerUsage> {
  // Use JSON format for robust parsing. The .Image field gives the name used to start the container.
  const output = await runCommand(
    'docker',
    // FIX: The format string should NOT be wrapped in extra quotes when passed as an array element.
    // The runCommand function handles argument spacing correctly.
    ['ps', '-a', '--format', "'{{json .}}'"],
    { suppressOutput: true },
  );

  if (!output) {
    return { running: new Set(), stopped: new Set() };
  }

  const running = new Set<string>();
  const stopped = new Set<string>();

  const lines = output.split('\n').filter(Boolean);
  for (const line of lines) {
    try {
      const container = JSON.parse(line);
      const image: string = container.Image;
      const state: string = container.State;

      if (image) {
        if (state === 'running') {
          running.add(image);
        } else {
          // Treat anything not 'running' (e.g., 'exited', 'created') as stopped.
          stopped.add(image);
        }
      }
    } catch (e) {
      logWarning(`Could not parse container info line: ${line}`);
    }
  }
  return { running, stopped };
}
